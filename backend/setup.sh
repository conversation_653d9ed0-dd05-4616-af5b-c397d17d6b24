#!/bin/bash

# Create necessary directories
mkdir -p storage
mkdir -p certificates

# Copy environment variables template if .env doesn't exist
if [ ! -f .env ]; then
  cp .env.example .env
  echo "Created .env file from template. Please update it with your configuration."
else
  echo ".env file already exists."
fi

# Check if Docker is installed
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
  echo "Docker and Docker Compose are installed."
  
  # Ask if user wants to start the application
  read -p "Do you want to start the application with Docker? (y/n) " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose up -d
    echo "Application started. You can access it at http://localhost:3000/api"
    echo "pgAdmin is available at http://localhost:5050"
    echo "  - Email: <EMAIL>"
    echo "  - Password: admin"
  fi
else
  echo "Docker and/or Docker Compose are not installed."
  echo "Please install them to run the application with Docker."
  echo "Alternatively, you can run the application locally:"
  echo "  1. Install dependencies: npm install"
  echo "  2. Start the application: npm run start:dev"
fi
