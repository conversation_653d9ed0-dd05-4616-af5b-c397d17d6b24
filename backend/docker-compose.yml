version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: eznfe-backend
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
      - DB_DATABASE=eznfe
      - DB_SYNCHRONIZE=false
      - JWT_SECRET=your-secret-key-change-in-production
      - JWT_ACCESS_EXPIRATION=15m
      - JWT_REFRESH_EXPIRATION=7d
      - REFRESH_TOKEN_EXPIRATION_MS=604800000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - STORAGE_TYPE=local
      - STORAGE_LOCAL_PATH=./storage
      - STORAGE_BASE_URL=http://localhost:3000/api/storage
      - CERTIFICATE_ENCRYPTION_KEY=your-encryption-key-change-in-production
      - CERTIFICATE_STORAGE_PATH=./certificates
      - WEBHOOK_MAX_RETRIES=5
      - WEBHOOK_RETRY_DELAY=60000
    volumes:
      - ./storage:/app/storage
      - ./certificates:/app/certificates
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - eznfe-network

  postgres:
    image: postgres:15-alpine
    container_name: eznfe-postgres
    restart: unless-stopped
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=eznfe
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - eznfe-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: eznfe-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    networks:
      - eznfe-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 5s
      timeout: 5s
      retries: 5

  pgadmin:
    image: dpage/pgadmin4
    container_name: eznfe-pgadmin
    restart: unless-stopped
    ports:
      - '5050:80'
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - eznfe-network

volumes:
  postgres-data:
  redis-data:
  pgadmin-data:

networks:
  eznfe-network:
    driver: bridge
