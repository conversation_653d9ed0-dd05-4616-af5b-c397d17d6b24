# Test environment configuration
NODE_ENV=test

# Use in-memory SQLite for tests
DB_TYPE=sqlite
DB_DATABASE=:memory:
DB_SYNCHRONIZE=true

# JWT
JWT_SECRET=test-secret-key
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d
REFRESH_TOKEN_EXPIRATION_MS=604800000

# Use local storage for tests
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=./test-storage
STORAGE_BASE_URL=http://localhost:3000/api/storage

# Certificate Encryption
CERTIFICATE_ENCRYPTION_KEY=32-char-test-encryption-key-for-cert
CERTIFICATE_STORAGE_PATH=./test-certificates

# Disable actual email sending in tests
MAIL_ENABLED=false
