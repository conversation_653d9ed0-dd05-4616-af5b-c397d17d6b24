#!/bin/sh
set -e

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
while ! nc -z ${DB_HOST} ${DB_PORT:-5432}; do
  sleep 1
done
echo "PostgreSQL is ready!"

# Add a delay to ensure PostgreSQL is fully ready
echo "Waiting for PostgreSQL to initialize..."
sleep 5
echo "PostgreSQL should be fully initialized now."

# Start the application
echo "Starting the application..."
exec "$@"
