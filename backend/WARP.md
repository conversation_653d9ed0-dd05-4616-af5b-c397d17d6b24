# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

Ez-NFe Backend is a NestJS-based API service for Brazilian fiscal compliance (NFe - Nota Fiscal Eletrônica). It features multi-tenant architecture with user management, digital certificate handling, invoice orchestration, and external integrations. The system uses PostgreSQL for data persistence, Redis for background processing, and supports both local and S3 storage.

## Architecture

### Tech Stack
- **Framework**: NestJS (TypeScript)
- **Database**: PostgreSQL with TypeORM
- **Cache/Queue**: Redis + BullMQ
- **Authentication**: JWT (access/refresh tokens)
- **Storage**: Local filesystem or AWS S3
- **Documentation**: Swagger/OpenAPI

### Multi-Tenant Structure
- **Users** can belong to multiple **Accounts**
- **Accounts** can have multiple **Emitters** (business entities)
- **Emitters** issue NFe documents and manage certificates
- All operations are scoped to the current emitter context

### Key Modules
- `auth/` - Authentication & authorization with JWT
- `users/` - User management and account relationships
- `emitters/` - Business entity management (CNPJ holders)
- `certificates/` - Digital certificate storage and validation
- `nfe/` - NFe document creation and lifecycle
- `storage/` - File storage abstraction (local/S3)
- `integration/` - External APIs and webhook handling

## Essential Development Commands

### Development Server
```bash
# Development with hot reload
npm run start:dev

# Development with webpack HMR (faster reloading)
npm run start:webpack

# Debug mode
npm run start:debug

# Production build and start
npm run build
npm run start:prod
```

### Testing
```bash
# Run all unit tests
npm run test

# Run tests with coverage report
npm run test:cov

# Run tests in watch mode
npm run test:watch

# Run end-to-end tests
npm run test:e2e

# Run specific test file
npm run test -- auth.service.spec.ts

# Run tests with debugging
npm run test:debug
```

### Database Operations
```bash
# Generate a new migration
npm run migration:generate -- MigrationName

# Run pending migrations
npm run migration:run

# Revert the last migration
npm run migration:revert
```

### Code Quality
```bash
# Lint and fix code issues
npm run lint

# Format code with Prettier
npm run format

# Check TypeScript types
npx tsc --noEmit
```

### Docker Development
```bash
# Start all services (app, postgres, redis, pgAdmin)
docker-compose up -d

# View application logs
docker-compose logs -f app

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up -d --build

# Setup script for quick start
./setup.sh
```

## Development Workflow

### Environment Setup
1. Copy environment template: `cp .env.example .env`
2. Update database credentials and secrets in `.env`
3. Run setup script: `./setup.sh` or manually install: `npm install`
4. Start development: `npm run start:dev`
5. Access API docs at: http://localhost:3000/api/docs

### Adding New Features
1. **Review Documentation**: Always check `docs/` directory, especially:
   - `database-schema.md` - Entity relationships and constraints
   - `technical-specifications.md` - Module structure patterns
   - `api-design-standards.md` - API conventions and DTOs
   - `testing-strategy.md` - Testing requirements (100% endpoint coverage)

2. **Follow Module Structure**: Use the standard template in `technical-specifications.md`:
   ```
   [module]/
   ├── [module].module.ts
   ├── controllers/
   ├── services/
   ├── entities/
   ├── dto/
   ├── repositories/ (if needed)
   └── tests/
   ```

3. **Testing Requirements**:
   - 100% coverage for all API endpoints
   - Unit tests for all service methods
   - Integration tests for API endpoints
   - E2E tests for complete user workflows

### Database Patterns
- All entities extend base entity with `id`, `createdAt`, `updatedAt`
- Multi-tenancy through `emitterId` foreign keys
- Use TypeORM decorators for validation and constraints
- Generate migrations for all schema changes

### API Design Standards
- RESTful endpoints with proper HTTP methods
- Standardized response format with `ApiResponse<T>`
- Comprehensive validation using class-validator
- Swagger documentation on all endpoints
- JWT authentication with role-based authorization

## Key Configuration Files

### Core Setup
- `src/main.ts` - Application bootstrap with middleware, CORS, Swagger
- `src/app.module.ts` - Root module with global configuration
- `package.json` - Scripts and dependencies
- `docker-compose.yml` - Development environment setup

### Development Tools
- `.env.example` - Comprehensive environment variable template
- `tsconfig.json` - TypeScript configuration
- `eslint.config.mjs` - Code linting rules
- `.prettierrc` - Code formatting rules
- `jest.config.js` - Test configuration

## Common Patterns

### Authentication Flow
1. User registers/signs in → receives JWT access/refresh tokens
2. Requests include `Authorization: Bearer <token>` header
3. Guards verify token and extract user context (`userId`, `accountId`, `emitterId`)
4. All operations are scoped to current emitter

### Error Handling
- Custom exception classes for business logic errors
- Global exception filters for consistent error responses
- Validation errors return HTTP 422
- Detailed error messages with error codes

### Background Processing
- BullMQ queues for NFe emission, DANFE generation, webhooks
- Workers handle async operations (certificate validation, document processing)
- Queue configurations in Redis with retry policies

### File Storage
- Abstract storage interface supports local filesystem or S3
- Files stored with organized path structure
- Presigned URLs for secure downloads
- Storage configuration via environment variables

## Important Notes

### Security Considerations
- Certificate passwords encrypted using `CERTIFICATE_ENCRYPTION_KEY`
- All sensitive operations require proper authentication
- Role-based access control (owner/admin/member)
- API keys for external integration access
- Webhook signatures for secure callbacks

### Multi-Tenant Context
- Always scope operations to current emitter
- Switch emitter context via `POST /auth/switch-emitter`
- JWT tokens carry current `emitterId` for proper isolation
- Database queries must include emitter-based filtering

### Brazilian Compliance
- NFe documents follow SEFAZ requirements
- Certificate validation for digital signatures
- Tax calculation based on Brazilian rules
- Integration with external PHP emissor service
- Webhook notifications for document status changes

## External Services

### Required Integrations
- **SEFAZ**: Brazilian tax authority for NFe transmission
- **Receita Federal API**: CNPJ validation and company data
- **ViaCEP**: Address lookup by postal code
- **PHP Emissor Service**: External service for SEFAZ communication

### Development Services
- **PostgreSQL**: Primary database (port 5432)
- **Redis**: Cache and job queues (port 6379)
- **pgAdmin**: Database administration (port 5050)
- **Swagger UI**: API documentation (/api/docs)

This backend is designed to be production-ready with comprehensive testing, proper error handling, and scalable architecture. Follow the established patterns and consult the extensive documentation in the `docs/` directory for detailed implementation guidance.
