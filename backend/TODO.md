# TODO
This file lists the remaining tasks to fully implement the ez-NFe backend.

## 1. Authentication & Authorization
- [ ] Add logout endpoint (`POST /auth/logout`) to revoke refresh tokens
  - In `AuthController`, add `POST /logout`
  - Accept `refreshToken` in body or Authorization header
  - In `AuthService.logout()`, delete the token record from `refresh_tokens` table
  - Respond with `{ success: true }` and HTTP 200
  - Write unit tests: ensure revoked tokens cannot be used for refresh
- [ ] Automatic purge or scheduled job for expired refresh tokens
  - Create `RefreshTokenCleanupTask` in `src/auth/tasks/refresh-token-cleanup.task.ts`
  - Use `@Cron(CronExpression.EVERY_HOUR)` or desired interval
  - In task, delete all `RefreshToken` entities where `expiresAt < now`
  - Register the task in `AuthModule`
  - Add integration test: seed expired tokens, run task, assert deletion

## 2. Multi-Tenant & Emitter Context
- [ ] Implement `POST /auth/switch-emitter` endpoint
  - In `AuthController`, add `POST /switch-emitter`
  - Body: `{ emitterId: string }`
  - Validate that `emitterId` belongs to `user.accountId`
  - Issue a new access token (and optionally refresh) with updated `emitterId` in payload
  - Respond with new `accessToken`, `refreshToken` (if rotated), and user context
  - Add e2e tests: change emitter context and ensure subsequent calls use new emitter
- [ ] Expose emitter selection on client UI / API
  - Document `switch-emitter` usage in API docs
  - Return available emitters in `GET /auth/me` for client dropdown

## 3. Emitters
- [ ] Enforce owner/admin role guard for emitter creation and updates
  - Create `RolesGuard` and `@Roles(...)` decorator (if not yet present)
  - Protect `POST /emitters` and `PATCH /emitters/:id` using `@Roles('owner','admin')`
  - In `EmittersController`, apply `@UseGuards(RolesGuard, JwtAuthGuard)`
  - Write tests: ensure `member` role cannot create/update emitters, but `admin`/`owner` can

## 4. Certificates
- [ ] Integration tests for automatic activation on expiration
  - Simulate expired `Certificate` rows in test DB
  - Run `CertificateExpirationTask.handleCertificateExpiration()` manually or via `@Cron` trigger
  - Assert expired certificates have `status=EXPIRED` and new `PENDING`→`VALID` activated, emitter status updated
- [ ] Validate certificate expiration cron in staging
  - Deploy to staging with real env, monitor logs at midnight or trigger manually
- [ ] Enforce single valid certificate per emitter
  - Test manually: upload two valid certificates, activate second, assert first is deactivated
  - Handle error cases when no pending certificates exist

## 5. API-Key & Webhooks
- [ ] Document webhook event payload and retry policies
  - Create `docs/webhooks.md` with JSON schema for POST body and headers (`X-Signature`)
  - Describe retry logic (up to 5 attempts, exponential backoff)
- [ ] Provide detailed API docs for API-key management
  - Use NestJS Swagger (`@nestjs/swagger`) to annotate `ApiKeysController`
  - Include example requests/responses in Swagger UI
- [ ] Support rotation of webhook secret
  - Extend `EmittersController` with `PATCH /emitters/:id/webhook-secret` endpoint
  - Generate new random secret, save in `emitter.webhookSecret`, return it once
  - Add tests for secret rotation and signature validation

## 6. Storage
- [ ] Implement S3 (or other cloud) storage provider
  - Create `S3StorageProvider` implementing `IStorageProvider`
  - Use AWS SDK v3 to `putObject` in bucket and return object key
  - Bind provider in `StorageModule` via env var switch (`useClass: process.env.USE_S3 === 'true' ? S3StorageProvider : LocalStorageProvider`)
- [ ] Generate presigned URLs for downloads
  - Extend `IStorageProvider` with `getPresignedUrl(key: string): Promise<string>`
  - In `StorageController`, return URL instead of streaming file
  - Add tests/mocks for presigned URL generation

## 7. NF-e Orchestration
- [ ] Real NF-e XML generation & XSD validation
  - Define TypeScript models matching SEFAZ schema
  - Use `xmlbuilder2` or similar to serialize models to XML
  - Validate generated XML against official XSDs with `libxmljs2` or external tool
- [ ] Digital signing of NF-e XML
  - Decrypt PFX blob via `CERTIFICATE_ENCRYPTION_KEY`
  - Extract private key and cert, sign `<infNFe>` according to SEFAZ rules
  - Embed `<Signature>` element in XML
- [ ] Create `nfe:transmit` queue processor
  - In `NfeTransmitProcessor`, call external PHP Emissor API or HTTP endpoint
  - Pass `nfeId`, decrypt certificate, send XML and metadata
  - Process response: update `NFeDocument.status`, `protocol`, `receiptNumber`, `errorMessage`
  - Enqueue `nfe:danfe` or webhook dispatch as needed
- [ ] Implement cancellation flow
  - Add `nfe:cancel` queue and `CancelNfeProcessor`
  - Call Emissor cancel API with reason code/justification
  - Update `NFeDocument.status = CANCELLED`, `cancelledAt`
  - Dispatch webhook event if configured

## 8. Testing & Documentation
- [ ] Unit tests for all modules
  - Use `jest` and `@nestjs/testing` to mock dependencies
  - Cover service logic (AuthService, UsersService, CertificatesService, etc.)
  - Write controller tests with `supertest` for each endpoint
- [ ] End-to-end tests (`test/e2e/*`)
  - Use in-memory SQLite DB and BullMQ local Redis
  - Script full flow: signUp → create emitter → upload certificate → issue NFe → cancel NFe → download XML/PDF
- [ ] Swagger / OpenAPI documentation
  - Install `@nestjs/swagger`, configure in `main.ts`
  - Annotate DTOs and controllers with `@Api*` decorators
  - Serve `/api/docs` endpoint with generated UI
- [ ] Provide `.env.example` and update README
  - List all env vars (`JWT_SECRET`, `DB_*`, `REDIS_HOST`, `CERTIFICATE_ENCRYPTION_KEY`, etc.)
  - Include sample values
- [ ] Pre-commit hook configuration
  - Install `husky` and `lint-staged`
  - Run `jest --bail --findRelatedTests` and `eslint --fix` on staged files