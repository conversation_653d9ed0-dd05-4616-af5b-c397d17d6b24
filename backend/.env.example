# =============================================================================
# EZ NFe SaaS - Environment Configuration
# =============================================================================
# This file contains all environment variables used throughout the application.
# Copy this file to .env and update the values according to your environment.

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application Basic Configuration
APP_NAME="EZ NFe SaaS"
APP_VERSION=1.0.0
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# API Configuration
API_GLOBAL_PREFIX=api
API_VERSION=v1

# Frontend URL (for email links and CORS)
FRONTEND_URL=http://localhost:3000

# CORS Configuration
CORS_ENABLED=true
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# Security Settings
BCRYPT_SALT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
LOCKOUT_DURATION_MINUTES=15
LOCKOUT_PROGRESSIVE_MULTIPLIER=2
MAX_LOCKOUT_DURATION_MINUTES=1440

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=pdf,xml,jpg,jpeg,png

# Pagination
DEFAULT_PAGINATION_LIMIT=20
MAX_PAGINATION_LIMIT=100

# Feature Flags
SWAGGER_ENABLED=true
METRICS_ENABLED=false
HEALTH_CHECK_ENABLED=true

# Monitoring and Logging
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=false

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database Settings
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=eznfe_saas

# Legacy environment variables (for backward compatibility)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=eznfe
DB_SYNCHRONIZE=true

# Database Connection Pool
DATABASE_MAX_CONNECTIONS=100
DATABASE_CONNECTION_TIMEOUT=30000
DATABASE_IDLE_TIMEOUT=30000

# Database SSL Configuration (for production)
# DATABASE_SSL=true
# DATABASE_SSL_REJECT_UNAUTHORIZED=true
# DATABASE_SSL_CA=path/to/ca.pem
# DATABASE_SSL_CERT=path/to/cert.pem
# DATABASE_SSL_KEY=path/to/key.pem

# Database Migrations and Sync
DATABASE_SYNCHRONIZE=false
DATABASE_MIGRATIONS_RUN=true
DATABASE_LOGGING=false
DATABASE_LOGGER=advanced-console

# Database Pool Configuration
DATABASE_POOL_MAX=100
DATABASE_POOL_MIN=10
DATABASE_ACQUIRE_TIMEOUT=60000
DATABASE_CREATE_TIMEOUT=30000
DATABASE_DESTROY_TIMEOUT=5000

# Database Additional Settings
DATABASE_STATEMENT_TIMEOUT=30000
DATABASE_QUERY_TIMEOUT=60000
DATABASE_APPLICATION_NAME=eznfe-saas-api
DATABASE_TIMEZONE=UTC
DATABASE_CHARSET=utf8mb4

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Basic Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
# REDIS_PASSWORD=your-redis-password
# REDIS_USERNAME=your-redis-username

# Redis Database Selection
REDIS_DB=0
REDIS_JOBS_DB=1
REDIS_SESSION_DB=2

# Redis Connection Options
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_LAZY_CONNECT=false
REDIS_KEEP_ALIVE=30000

# Redis Retry Strategy
REDIS_RETRY_DELAY_ON_FAILOVER=100
REDIS_ENABLE_READY_CHECK=true
REDIS_MAX_RETRIES_PER_REQUEST=3

# Redis Cache Configuration
REDIS_CACHE_TTL=300
REDIS_CACHE_MAX=1000
REDIS_CACHE_KEY_PREFIX=eznfe:cache:

# Redis Cache TTLs for Different Data Types
REDIS_USER_TTL=900
REDIS_SESSION_TTL=1800
REDIS_CNPJ_VALIDATION_TTL=86400
REDIS_CEP_VALIDATION_TTL=604800
REDIS_TAX_CALCULATION_TTL=3600
REDIS_BUSINESS_UNIT_TTL=1800
REDIS_PRODUCT_TTL=3600
REDIS_CERTIFICATE_TTL=900

# Redis Session Configuration
REDIS_SESSION_KEY_PREFIX=eznfe:session:
REDIS_SESSION_ROLLING=true
REDIS_SESSION_TOUCH_AFTER=300

# Redis Rate Limiting
REDIS_RATE_LIMIT_KEY_PREFIX=eznfe:rateLimit:
REDIS_RATE_LIMIT_WINDOW=60000
REDIS_RATE_LIMIT_MAX=100

# Redis Bull Queue Configuration
REDIS_BULL_KEY_PREFIX=eznfe:bull:
REDIS_BULL_DEFAULT_ATTEMPTS=3
REDIS_BULL_BACKOFF_TYPE=exponential
REDIS_BULL_BACKOFF_DELAY=2000
REDIS_BULL_REMOVE_ON_COMPLETE=100
REDIS_BULL_REMOVE_ON_FAIL=50
REDIS_BULL_TTL=86400000

# Redis Health Check
REDIS_HEALTH_CHECK_ENABLED=true
REDIS_HEALTH_CHECK_INTERVAL=30000
REDIS_HEALTH_CHECK_TIMEOUT=5000

# =============================================================================
# AUTHENTICATION & JWT CONFIGURATION
# =============================================================================

# JWT Access Token Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_ALGORITHM=HS256
JWT_AUDIENCE=eznfe-saas-api
JWT_ISSUER=eznfe-saas

# JWT Refresh Token Configuration
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_REFRESH_EXPIRES_IN=7d
JWT_REFRESH_ALGORITHM=HS256
JWT_REFRESH_AUDIENCE=eznfe-saas-api
JWT_REFRESH_ISSUER=eznfe-saas

# Legacy JWT Configuration (for backward compatibility)
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d
REFRESH_TOKEN_EXPIRATION_MS=604800000

# Email Verification Token
JWT_EMAIL_VERIFICATION_SECRET=your-email-verification-secret-change-in-production
JWT_EMAIL_VERIFICATION_EXPIRES_IN=24h

# Password Reset Token
JWT_PASSWORD_RESET_SECRET=your-password-reset-secret-change-in-production
JWT_PASSWORD_RESET_EXPIRES_IN=1h

# Password Security Configuration
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_LENGTH=128
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
PASSWORD_PREVENT_REUSE=5
PASSWORD_EXPIRATION_DAYS=0
PASSWORD_WARNING_DAYS=7

# Account Security
MAX_ACTIVE_SESSIONS=5
SESSION_TIMEOUT_MINUTES=30
REQUIRE_EMAIL_VERIFICATION=true
EMAIL_VERIFICATION_TOKEN_LENGTH=32

# Two-Factor Authentication
TWO_FACTOR_ENABLED=false
TWO_FACTOR_ISSUER="EZ NFe SaaS"
TWO_FACTOR_WINDOW=1

# OAuth Configuration (Future Implementation)
GOOGLE_OAUTH_ENABLED=false
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=/auth/google/callback

MICROSOFT_OAUTH_ENABLED=false
# MICROSOFT_CLIENT_ID=your-microsoft-client-id
# MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_CALLBACK_URL=/auth/microsoft/callback

# CSRF Protection
CSRF_ENABLED=false
CSRF_COOKIE_NAME=_csrf
# CSRF_SECRET=your-csrf-secret-change-in-production

# Authentication Rate Limiting
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX=5
AUTH_RATE_LIMIT_MESSAGE="Too many authentication attempts"

# IP Filtering (comma-separated)
# AUTH_IP_WHITELIST=***********/24,10.0.0.0/8
# AUTH_IP_BLACKLIST=*************,*********

# Cookie Configuration
REFRESH_TOKEN_COOKIE_NAME=refresh_token
SESSION_COOKIE_NAME=session
# COOKIE_DOMAIN=.yourdomain.com

# API Key Authentication
API_KEY_HEADER_NAME=X-API-Key
API_KEY_AUTH_ENABLED=true
API_KEY_FORMAT=ezn_
API_KEY_LENGTH=32

# Multi-tenancy Headers
TENANT_HEADER_NAME=X-Tenant-Id
BUSINESS_UNIT_HEADER_NAME=X-Business-Unit-Id
TENANT_REQUIRED=true
ALLOW_TENANT_SWITCHING=true

# Certificate Validation
CERT_VALIDATION_ENABLED=false
CERT_VALIDATION_TIMEOUT=10000
CERT_VALIDATION_RETRY_ATTEMPTS=3

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Storage Provider Selection
STORAGE_TYPE=local

# Local Storage Configuration
STORAGE_LOCAL_PATH=./storage
STORAGE_BASE_URL=http://localhost:3000/api/storage

# AWS S3 Configuration (when STORAGE_TYPE=s3)
AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_S3_BUCKET=your-s3-bucket-name

# =============================================================================
# CERTIFICATE MANAGEMENT
# =============================================================================

# Certificate Encryption and Storage
CERTIFICATE_ENCRYPTION_KEY=your-encryption-key-change-in-production
CERTIFICATE_STORAGE_PATH=./certificates

# Certificate Expiration Warnings
CERT_EXPIRATION_WARNING_DAYS=30

# =============================================================================
# EXTERNAL SERVICES CONFIGURATION
# =============================================================================

# Receita Federal API
RECEITA_FEDERAL_BASE_URL=https://www.receitaws.com.br/v1
RECEITA_FEDERAL_TIMEOUT=10000
RECEITA_FEDERAL_RATE_LIMIT=3

# ViaCEP API
VIACEP_BASE_URL=https://viacep.com.br/ws
VIACEP_TIMEOUT=5000
VIACEP_RATE_LIMIT=60

# SEFAZ Configuration
SEFAZ_HOMOLOGATION_URL=https://hom.nfe.fazenda.gov.br
SEFAZ_PRODUCTION_URL=https://www.nfe.fazenda.gov.br
SEFAZ_TIMEOUT=30000

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================

# Trial Configuration
TRIAL_DURATION_DAYS=14

# Invoice Numbering
INVOICE_START_NUMBER=1
INVOICE_RESET_MONTHLY=false

# Validation Cache TTLs (in minutes)
CNPJ_CACHE_TTL_MINUTES=1440
CEP_CACHE_TTL_MINUTES=10080

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Webhook Retry Configuration
WEBHOOK_MAX_RETRIES=5
WEBHOOK_RETRY_DELAY=60000

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Email Service Configuration (Future Implementation)
# EMAIL_PROVIDER=sendgrid # or 'ses', 'smtp'
# SENDGRID_API_KEY=your-sendgrid-api-key
# FROM_EMAIL=<EMAIL>
# FROM_NAME="EZ NFe SaaS"

# SMTP Configuration (if using SMTP provider)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-email-password

# AWS SES Configuration (if using SES provider)
# AWS_SES_REGION=us-east-1
# AWS_SES_ACCESS_KEY_ID=your-ses-access-key
# AWS_SES_SECRET_ACCESS_KEY=your-ses-secret-key

# =============================================================================
# DEVELOPMENT & TESTING CONFIGURATION
# =============================================================================

# Test Database (for running tests)
# TEST_DATABASE_HOST=localhost
# TEST_DATABASE_PORT=5433
# TEST_DATABASE_USERNAME=postgres
# TEST_DATABASE_PASSWORD=postgres
# TEST_DATABASE_NAME=eznfe_saas_test

# Seed Data Configuration
# SEED_DEFAULT_ADMIN_EMAIL=<EMAIL>
# SEED_DEFAULT_ADMIN_PASSWORD=Admin123!

# =============================================================================
# PRODUCTION-SPECIFIC CONFIGURATION
# =============================================================================

# When NODE_ENV=production, ensure these are properly configured:
# - All secrets should be cryptographically secure random strings
# - Database SSL should be enabled if required
# - Redis password should be set if required
# - AWS credentials should be properly configured
# - CORS_ORIGIN should be set to your production domain
# - FRONTEND_URL should be set to your production frontend URL
# - Certificate encryption key should be securely generated
# - Email service should be properly configured
