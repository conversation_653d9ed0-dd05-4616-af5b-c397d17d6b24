{"name": "backend", "version": "0.0.1", "description": "<p align=\"center\">   <a href=\"http://nestjs.com/\" target=\"blank\"><img src=\"https://nestjs.com/img/logo-small.svg\" width=\"120\" alt=\"Nest Logo\" /></a> </p>", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_ENV=development nest start --watch", "start:webpack": "NODE_ENV=development nest build --webpack --webpackPath webpack-hmr.config.js --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- migration:generate -d src/database/data-source.ts", "migration:run": "npm run typeorm -- migration:run -d src/database/data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/database/data-source.ts", "postinstall": "npm rebuild bcrypt --build-from-source", "prepare": "husky install"}, "dependencies": {"@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@nestjs/axios": "^4.0.1", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.6", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@swc/helpers": "^0.5.17", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bullmq": "^5.51.1", "cache-manager": "^7.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.1", "crypto": "^1.0.1", "helmet": "^8.1.0", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.15.6", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sqlite3": "^5.1.7", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.1", "prettier": "^3.4.2", "process": "^0.11.10", "run-script-webpack-plugin": "^0.2.3", "source-map-support": "^0.5.21", "stream-browserify": "^3.0.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.20.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "main": "index.js", "directories": {"test": "test"}, "keywords": [], "lint-staged": {"*.ts": ["eslint --fix", "jest --bail --findRelatedTests"]}}