# ez-NFe Backend

This is the backend service for the ez-NFe system, built with NestJS. It serves as the core API for user management, account and emitter configuration, NFe orchestration, certificate storage, and external integration.

## Features

- User authentication and management
- Multi-tenant architecture (multi-account per user, multi-emitter per account)
- Certificate management
- NFe document creation and management
- Webhook integration
- Background processing with Redis/BullMQ

## Prerequisites

- Node.js (v22 or higher)
- PostgreSQL (v15 or higher)
- Redis (v7 or higher)

## Installation

```bash
# Install dependencies
npm install
```

## Configuration

Create a `.env` file in the root directory based on the provided `.env.example`:

```bash
# Copy the example environment file
cp .env.example .env

# Edit the file with your specific configuration
nano .env
```

The configuration includes:

- **Application settings**: Port, environment
- **Database connection**: Host, port, credentials
- **JWT authentication**: Secret keys, token expiration
- **Redis connection**: For background job processing
- **Storage options**: Local filesystem or S3
- **Certificate encryption**: For secure storage of digital certificates
- **Webhook settings**: For integration with external systems

See `.env.example` for all available configuration options and their default values.

## Running the app

### Local Development

```bash
# Development
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

### Using Docker

The application can be run using Docker and Docker Compose:

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop all services
docker-compose down
```

This will start:

- The NestJS application on port 3000
- PostgreSQL database on port 5432
- Redis on port 6379
- pgAdmin (PostgreSQL admin interface) on port 5050

You can access pgAdmin at http://localhost:5050 with the following credentials:

- Email: <EMAIL>
- Password: admin

## API Documentation

The API is documented using Swagger/OpenAPI. Once the application is running, you can access the interactive API documentation at:

```
http://localhost:3000/api/docs
```

This provides a comprehensive interface to:

- Explore available endpoints
- View request/response schemas
- Test API calls directly from the browser
- Understand authentication requirements

## API Endpoints

### Authentication

- `POST /api/auth/signup` - Register a new user
- `POST /api/auth/signin` - Login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout (revoke refresh token)
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/switch-emitter` - Switch active emitter context

### Emitters

- `GET /api/emitters` - List emitters
- `POST /api/emitters` - Create a new emitter
- `GET /api/emitters/:id` - Get emitter details
- `PATCH /api/emitters/:id` - Update emitter
- `PATCH /api/emitters/:id/config` - Update emitter configuration
- `PATCH /api/emitters/:id/webhook-secret` - Rotate webhook secret

### Certificates

- `POST /api/certificates` - Upload a certificate
- `GET /api/certificates` - List certificates
- `GET /api/certificates/:id` - Get certificate details
- `POST /api/certificates/:id/activate` - Activate a certificate

### NFe Documents

- `POST /api/nfe` - Create a new NFe document
- `GET /api/nfe` - List NFe documents (with pagination)
- `GET /api/nfe/:id` - Get NFe document details
- `POST /api/nfe/:id/cancel` - Cancel an NFe document
- `GET /api/nfe/:id/xml` - Download XML
- `GET /api/nfe/:id/danfe` - Download DANFE
- `GET /api/nfe/:id/xml/url` - Get presigned URL for XML download
- `GET /api/nfe/:id/danfe/url` - Get presigned URL for DANFE download

### API Keys & External API

- `GET /api/api-keys` - List API keys
- `POST /api/api-keys` - Create a new API key
- `PATCH /api/api-keys/:id` - Update API key
- `DELETE /api/api-keys/:id` - Delete API key
- `POST /api/external/nfe` - Create a new NFe document via API
- `GET /api/external/nfe/:id` - Get NFe document status via API

## Testing

The application includes comprehensive test coverage:

```bash
# Run unit tests
npm run test

# Run tests with coverage report
npm run test:cov

# Run end-to-end tests
npm run test:e2e
```

### Test Coverage

Unit tests cover individual components:

- Service methods
- Controller endpoints
- Guards and interceptors
- Utility functions

End-to-end tests cover complete workflows:

- User registration and authentication
- Emitter and certificate management
- NFe document creation and lifecycle
- API key usage and external API access

## Development

### Code Structure

- `src/` - Application source code
  - `auth/` - Authentication and authorization
  - `users/` - User management
  - `accounts/` - Account management
  - `emitters/` - Emitter management
  - `certificates/` - Certificate management
  - `nfe/` - NFe document management
  - `storage/` - File storage (local and S3)
  - `integration/` - External API and webhooks
  - `common/` - Shared utilities and interfaces

### Database Migrations

```bash
# Generate a new migration
npm run migration:generate -- MigrationName

# Run migrations
npm run migration:run

# Revert the last migration
npm run migration:revert
```

## License

This project is proprietary and confidential.
