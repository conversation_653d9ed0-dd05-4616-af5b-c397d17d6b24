# ez-NFe Backend Specification (NestJS)

## 🧭 Overview
This document describes the backend service of the ez-NFe system, built with NestJS. It serves as the core API for user management, account and emitter configuration, NFe orchestration, certificate storage, and external integration.

### 🏗 Architecture
- **Language**: TypeScript
- **Framework**: NestJS
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Queue**: Redis + BullMQ
- **Auth**: JWT-based (access/refresh)
- **Multi-Tenant**: Yes (multi-account per user, multi-emitter per account)

---

## 📁 Modules

### 1. Auth & User Management Module
Handles sign up, sign in, token refreshing, guards, and user-account relationship management.

#### Entities
- **User**
  - `id`: UUID
  - `email`: string
  - `passwordHash`: string
  - `createdAt`, `updatedAt`

- **Account**
  - `id`: UUID
  - `name`: string
  - `createdAt`, `updatedAt`

- **UserAccount** (Join Table)
  - `userId`: UUID
  - `accountId`: UUID
  - `role`: enum (`owner`, `admin`, `member`)

- **Emitter**
  - `id`: UUID
  - `accountId`: UUID (FK)
  - `name`: string
  - `federalTaxId`: string (CNPJ/CPF)
  - `stateTaxId`: string (Inscrição Estadual)
  - `municipalTaxId`: string (Inscrição Municipal)
  - `billingEmail`: string
  - `phoneNumber`: string
  - `state`: string (UF)
  - `city`: string
  - `addressLine1`: string
  - `addressLine2`: string (optional)
  - `zipCode`: string
  - `taxRegime`: enum (`SimplesNacional`, `LucroPresumido`, `LucroReal`, etc.)
  - `cnae`: string
  - `legalNature`: string
  - `tradeName`: string
  - `municipalRegistrationDate`: Date (optional)
  - `certificateStatus`: enum (`pending`, `valid`, `expired`, `revoked`)
  - `createdAt`, `updatedAt`

- **Certificate**
  - `id`: UUID
  - `emitterId`: UUID (FK)
  - `pfxFile`: string (base64 or encrypted blob path)
  - `passwordEncrypted`: string
  - `validFrom`: Date
  - `validTo`: Date
  - `status`: enum (`pending`, `valid`, `expired`, `revoked`)
  - `uploadedAt`: Date
  - `activatedAt`: Date (nullable)
  - `createdAt`, `updatedAt`

#### Endpoints
- `POST /auth/signup`
- `POST /auth/signin`
- `POST /auth/refresh`
- `GET /auth/me`
- `GET /emitters`
- `POST /emitters`
- `POST /emitters/:id/certificates`
- `GET /emitters/:id/certificates`

#### Notes
- Users can belong to multiple accounts.
- Accounts own one or more emitters.
- JWT tokens will carry `userId`, selected `accountId`, and `emitterId`.
- Switching emitter context requires re-authentication or refresh.

#### DTOs
- `SignUpDto`: `email`, `password`, `accountName`, `emitter` (nested)
- `SignInDto`: `email`, `password`
- `CreateEmitterDto`: all emitter registration fields
- `UploadCertificateDto`: PFX file (base64 or multipart), password

#### Validation Rules
- `federalTaxId` must be valid (CNPJ format for now)
- `email`, `federalTaxId` must be unique
- PFX must be a valid certificate, validated on upload

---

## 🗂 Tenant Model
All entities (e.g., emitters, certificates, NFe documents) are scoped to an `emitterId`, which is associated with an `accountId`. Users operate in the context of a selected account and emitter.

---

### 🧾 Emitter Module

Each `Emitter` represents a CNPJ (or CPF) entity authorized to issue NF-e. It contains all necessary fiscal, address, and configuration details. The emitter must be created and configured before submitting any NF-e.

#### Endpoints
- `GET /emitters` — list emitters associated with the current account
- `POST /emitters` — register a new emitter under the current account
- `PATCH /emitters/:id` — update emitter metadata
- `GET /emitters/:id` — retrieve emitter details

#### Notes
- Each emitter must have at least one valid certificate to issue NF-e
- Emitters are linked to one `Account`, but an account can have multiple emitters (multi-CNPJ operation)

### 📦 NFe Orchestration Module (Frontend-Driven)

Handles the full lifecycle of NF-e documents:
- Validation
- Signature (using valid certificate)
- Transmission to SEFAZ via external PHP Emissor
- Status tracking

#### Entities
- **NFeDocument**
  - `id`: UUID
  - `emitterId`: UUID (FK)
  - `documentNumber`: int
  - `series`: int
  - `xmlPayload`: text (raw signed XML)
  - `status`: enum (`created`, `signed`, `sent`, `authorized`, `denied`, `cancelled`, `error`)
  - `receiptNumber`: string
  - `protocol`: string
  - `errorMessage`: string (nullable)
  - `generatedAt`, `transmittedAt`, `authorizedAt`, `cancelledAt`

#### Endpoints
- `POST /nfe` — submit new document for processing
- `GET /nfe/:id` — retrieve document status/details
- `GET /nfe` — list/filter documents by emitter
- `POST /nfe/:id/cancel` — submit cancellation request

#### Flow
1. `POST /nfe` receives a normalized payload from the frontend (customer, items, payment, emitter context, etc.)
2. Backend builds the necessary NF-e data model structure from this payload (no XML is received)
3. XML is generated and signed internally
4. XML + metadata + decrypted certificate is sent to the Emissor PHP service
5. SEFAZ response is received, parsed, and the `NFeDocument` is updated accordingly
6. A background job optionally triggers PDF (DANFE) generation

#### Responsibilities
- The frontend provides a user-friendly form for NFe issuance
- The backend translates this into the full NFe structure expected by SEFAZ
- Backend is responsible for ensuring that the payload is converted, validated, and only then signed and sent

#### Example `POST /nfe` Payload
```json
{
  "customer": {
    "name": "João da Silva",
    "federalTaxId": "12345678901",
    "email": "<EMAIL>",
    "address": {
      "street": "Rua Um",
      "number": "100",
      "neighborhood": "Centro",
      "city": "São Paulo",
      "state": "SP",
      "zipCode": "01234-000"
    }
  },
  "products": [
    { "description": "Produto A", "quantity": 2, "price": 100.00, "taxCode": "5102" }
  ],
  "payment": {
    "method": "credit_card",
    "amount": 200.00
  }
}
```

#### Transmission to PHP Emissor
- Shared access to PostgreSQL is used instead of transmitting full files between services
- PHP service reads:
  - Active certificate (`Certificate` table)
  - NFe structured data (`NFeDocument` + linked models)
- PHP service writes:
  - Status (`NFeDocument.status`)
  - Protocol, receipt, error messages
  - File paths for generated XML and DANFE
- Communication between services is triggered via queue (Redis/BullMQ) using only document IDs

---

### 📦 Storage Module

Responsible for handling persistence and retrieval of NF-e XML files and generated DANFE PDFs.

#### Entities
- **DocumentStorage**
  - `id`: UUID
  - `nfeDocumentId`: UUID (FK)
  - `xmlPath`: string (S3 or local storage path)
  - `danfePath`: string (optional)
  - `storedAt`: Date
  - `updatedAt`: Date

#### Endpoints
- `GET /nfe/:id/xml` — download signed XML
- `GET /nfe/:id/danfe` — download DANFE in PDF format
- (Internally) a background worker/service uploads and links these files

#### Notes
- Storage is managed via S3-compatible backend (e.g., AWS S3 or MinIO)
- File paths are saved in DB for fast reference
- Download URLs may be presigned or protected

---

### 🔗 Integration & Webhook Module

This module allows external systems to trigger emissions and receive status updates.

#### Endpoints (Public API)
- `POST /external/nfe` — issue NF-e on behalf of an emitter (using API key)
- `GET /external/nfe/:id` — get document status

#### Authentication
- Each `Account` may have one or more API keys
- API keys are scoped to specific `Emitter`s
- Headers: `X-API-Key: abc123`

#### Webhook Management
- Each `Emitter` can define a webhook URL
- Webhook payload example:
```json
{
  "nfeId": "...",
  "status": "authorized",
  "protocol": "***************",
  "timestamp": "2025-04-29T15:42:00Z"
}
```
- Events: `authorized`, `denied`, `cancelled`, `error`

#### Security
- Signature included in header (`X-Signature`) with HMAC using a shared secret

---

### 🧮 Invoice Numbering & Series Management

Each `Emitter` can define the starting point for `documentNumber` and the `series`. The system manages the incrementation automatically.

#### Fields (Emitter)
- `series`: integer (default: 1)
- `currentNumber`: integer (initial value set by user)

#### Rules
- On every successful emission:
  - `currentNumber` is incremented by 1
  - Operation must be transactional
- Users can update `series` and `currentNumber` manually via admin UI or API (with validation)

#### Endpoints
- `PATCH /emitters/:id/config`

---

### ⚙️ Background Workers Architecture (BullMQ)

#### Example: NF-e Emission Worker

**Queue:** `nfe:emit`

```ts
// src/nfe/nfe-emit.processor.ts
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bullmq';
import { NfeService } from './nfe.service';

@Processor('nfe:emit')
export class NfeEmitProcessor {
  constructor(private readonly nfeService: NfeService) {}

  @Process()
  async handle(job: Job<{ nfeId: string }>) {
    const { nfeId } = job.data;
    try {
      await this.nfeService.emitDocument(nfeId);
      job.updateProgress(100);
    } catch (error) {
      job.moveToFailed({ message: error.message });
      throw error;
    }
  }
}
```

**Service Signature:**
```ts
// src/nfe/nfe.service.ts
async emitDocument(nfeId: string): Promise<void> {
  // Load document, certificate, build XML
  // Write output/status to DB
  // Signal downstream queues (e.g., generate DANFE)
}
```

**Queue Initialization:**
```ts
// src/nfe/nfe.module.ts
BullModule.registerQueue({ name: 'nfe:emit' })
```


Each worker is implemented as an isolated consumer of a specific Redis queue. Workers can scale horizontally and may use concurrency where idempotency is guaranteed.

#### Worker Responsibilities
- Workers are deployed as dedicated NestJS modules using `@nestjs/bull`
- Jobs are typed and versioned (e.g., `nfe:emit:v1`)
- Workers log progress via centralized logging (e.g., Sentry, CloudWatch)

#### Architecture Components
- `QueueService`: wraps BullMQ queue interactions
- `WorkerModule`: per-queue listener with named processor functions
- `JobMetadata`: included in all jobs for tracking (e.g., emitterId, accountId, userId, retryCount)

#### Deployment Options
- Workers can be colocated with API or run as independent services
- Jobs are retried automatically with exponential backoff by default

#### Queues
- `nfe:emit`
  - Triggered on `POST /nfe`
  - Picks up payload, finds emitter + cert, calls PHP Emissor
- `nfe:danfe`
  - Triggered when NF-e is authorized
  - Calls DANFE generation and stores PDF
- `webhook:dispatch`
  - Dispatch webhook notification for state changes

#### Retry Policies
- Emission: 3 retries with exponential backoff
- Webhook: 5 retries, then mark as failed

---

### ⏱ Scheduled Jobs

Jobs executed on a regular schedule:

#### Cron Tasks
- `cert:check-expired`
  - Runs daily
  - Marks certificates as `expired` if past `validTo`
  - If `pending` exists, mark as `valid`

- `nfe:reprocess-errors`
  - Optionally retries failed emissions
  - Runs every 15–30 min

---

### 📥 Certificate Upload Flow

#### 📤 Endpoint: `POST /emitters/:id/certificates`
**Content-Type:** `multipart/form-data`

**Form Fields:**
- `pfxFile` (file): the `.pfx` certificate file
- `password` (string): password to decrypt the certificate

#### ✅ Successful Response
```json
{
  "id": "9cb2fd2c-3e64-4ddf-b88f-4c5cbce6e8fc",
  "emitterId": "e24f939c-78d7-4a21-bce5-47ffb663fbfd",
  "validFrom": "2025-04-01T00:00:00.000Z",
  "validTo": "2026-04-01T00:00:00.000Z",
  "status": "pending",
  "uploadedAt": "2025-04-29T14:00:00.000Z"
}
```

#### 🔄 Activation Logic
- Only one certificate can be marked as `valid` per emitter.
- New certificates are uploaded as `pending`.
- Activation options:
  - Manual: via `PATCH /certificates/:id/activate`
  - Automatic: when current certificate expires and a pending one exists

---

### 🔧 Manual Activation Endpoint

**Endpoint:** `PATCH /certificates/:id/activate`

**Authorization:**
- Only users with `owner` or `admin` role on the associated account can activate certificates.

**Response:**
```json
{
  "id": "9cb2fd2c-3e64-4ddf-b88f-4c5cbce6e8fc",
  "status": "valid",
  "activatedAt": "2025-04-30T12:00:00.000Z"
}
```

**Errors:**
- 403: Forbidden
- 409: Certificate already active or expired
- 404: Certificate not found

---

### 🔒 Security Considerations
- Passwords encrypted (AES-256)
- Certificate files stored encrypted (not base64 blobs)
- Validity dates are extracted from the certificate — not from user input

---

### 🧪 Test Cases
- ✅ Valid PFX upload → returns `pending`
- ❌ Corrupt/invalid PFX → 400
- ✅ Manual activation works and disables previous
- ❌ Duplicate active certificate → 409
- ⏳ Scheduled job handles expiration and automatic promotion of next certificate


