import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { NFeStatus } from '../src/nfe/entities/nfe-document.entity';
import { CertificateStatus } from '../src/common/enums/certificate-status.enum';

describe('Full Flow (e2e)', () => {
  let app: INestApplication;
  let accessToken: string;
  let refreshToken: string;
  let accountId: string;
  let emitterId: string;
  let certificateId: string;
  let nfeId: string;

  beforeAll(async () => {
    // Create a test module with in-memory SQLite and local Redis
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: () => ({
            type: 'sqlite',
            database: ':memory:',
            entities: [__dirname + '/../**/*.entity{.ts,.js}'],
            synchronize: true,
          }),
        }),
        BullModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: () => ({
            redis: {
              host: 'localhost',
              port: 6379,
            },
            defaultJobOptions: {
              attempts: 3,
              removeOnComplete: true,
              removeOnFail: false,
            },
          }),
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('Step 1: Sign up a new user with an emitter', async () => {
    const signUpDto = {
      email: '<EMAIL>',
      password: 'Password123!',
      accountName: 'Test Account',
      emitter: {
        name: 'Test Emitter',
        cnpj: '**************',
      },
    };

    const response = await request(app.getHttpServer())
      .post('/auth/signup')
      .send(signUpDto)
      .expect(201);

    expect(response.body).toHaveProperty('accessToken');
    expect(response.body).toHaveProperty('refreshToken');
    expect(response.body).toHaveProperty('user');
    expect(response.body.user).toHaveProperty('account');
    expect(response.body.user.account).toHaveProperty('id');
    expect(response.body.user).toHaveProperty('emitter');
    expect(response.body.user.emitter).toHaveProperty('id');

    accessToken = response.body.accessToken;
    refreshToken = response.body.refreshToken;
    accountId = response.body.user.account.id;
    emitterId = response.body.user.emitter.id;
  });

  it('Step 2: Upload a certificate', async () => {
    // Mock certificate data (in a real test, this would be a real PFX file)
    const mockCertificateData = Buffer.from('mock certificate data');
    
    const response = await request(app.getHttpServer())
      .post(`/certificates`)
      .set('Authorization', `Bearer ${accessToken}`)
      .field('emitterId', emitterId)
      .field('password', 'cert-password')
      .attach('file', mockCertificateData, 'certificate.pfx')
      .expect(201);

    expect(response.body).toHaveProperty('id');
    expect(response.body).toHaveProperty('emitterId', emitterId);
    expect(response.body).toHaveProperty('status', CertificateStatus.PENDING);

    certificateId = response.body.id;
  });

  it('Step 3: Activate the certificate', async () => {
    const response = await request(app.getHttpServer())
      .post(`/certificates/${certificateId}/activate`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('id', certificateId);
    expect(response.body).toHaveProperty('status', CertificateStatus.VALID);
    expect(response.body).toHaveProperty('isActive', true);
  });

  it('Step 4: Issue an NFe', async () => {
    const createNfeDto = {
      emitterId: emitterId,
      payload: {
        customer: {
          name: 'Test Customer',
          federalTaxId: '12345678901',
          email: '<EMAIL>',
          address: {
            street: 'Test Street',
            number: '123',
            neighborhood: 'Test Neighborhood',
            city: 'Test City',
            state: 'TS',
            zipCode: '12345678',
          },
        },
        products: [
          {
            description: 'Test Product',
            quantity: 1,
            price: 100,
            taxCode: '123',
          },
        ],
        payment: {
          method: 'cash',
          amount: 100,
        },
      },
    };

    const response = await request(app.getHttpServer())
      .post('/nfe')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(createNfeDto)
      .expect(201);

    expect(response.body).toHaveProperty('id');
    expect(response.body).toHaveProperty('emitterId', emitterId);
    expect(response.body).toHaveProperty('status', NFeStatus.CREATED);

    nfeId = response.body.id;
  });

  it('Step 5: Get NFe details', async () => {
    const response = await request(app.getHttpServer())
      .get(`/nfe/${nfeId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('id', nfeId);
    expect(response.body).toHaveProperty('emitterId', emitterId);
  });

  it('Step 6: Get NFe list with pagination', async () => {
    const response = await request(app.getHttpServer())
      .get('/nfe?page=1&limit=10')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('items');
    expect(response.body).toHaveProperty('meta');
    expect(response.body.meta).toHaveProperty('totalItems');
    expect(response.body.meta).toHaveProperty('itemsPerPage', 10);
    expect(response.body.meta).toHaveProperty('currentPage', 1);
    expect(response.body.items.length).toBeGreaterThan(0);
  });

  it('Step 7: Cancel the NFe', async () => {
    // In a real test, we would need to update the NFe status to AUTHORIZED first
    // For this test, we'll mock that by directly updating the database
    
    const cancelNfeDto = {
      reason: 'Test cancellation',
    };

    const response = await request(app.getHttpServer())
      .post(`/nfe/${nfeId}/cancel`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(cancelNfeDto)
      .expect(200);

    expect(response.body).toHaveProperty('id', nfeId);
    expect(response.body).toHaveProperty('status', NFeStatus.CANCELLED);
    expect(response.body).toHaveProperty('cancellationReason', 'Test cancellation');
  });

  it('Step 8: Get XML presigned URL', async () => {
    const response = await request(app.getHttpServer())
      .get(`/nfe/${nfeId}/xml/url`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('url');
    expect(response.body).toHaveProperty('expiresAt');
  });

  it('Step 9: Get DANFE presigned URL', async () => {
    const response = await request(app.getHttpServer())
      .get(`/nfe/${nfeId}/danfe/url`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('url');
    expect(response.body).toHaveProperty('expiresAt');
  });

  it('Step 10: Refresh token', async () => {
    const response = await request(app.getHttpServer())
      .post('/auth/refresh')
      .send({ refreshToken })
      .expect(200);

    expect(response.body).toHaveProperty('accessToken');
    expect(response.body).toHaveProperty('refreshToken');
  });

  it('Step 11: Logout', async () => {
    const response = await request(app.getHttpServer())
      .post('/auth/logout')
      .send({ refreshToken })
      .expect(200);

    expect(response.body).toHaveProperty('success', true);
  });
});
