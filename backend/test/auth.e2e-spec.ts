import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { RefreshToken } from '../src/auth/entities/refresh-token.entity';
import { Repository } from 'typeorm';
import { User } from '../src/users/entities/user.entity';
import { Account } from '../src/accounts/entities/account.entity';
import { UserAccount } from '../src/users/entities/user-account.entity';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

describe('AuthController (e2e)', () => {
  let app: INestApplication;
  let refreshTokenRepository: Repository<RefreshToken>;
  let userRepository: Repository<User>;
  let accountRepository: Repository<Account>;
  let userAccountRepository: Repository<UserAccount>;

  let testUser: User;
  let testAccount: Account;
  let testRefreshToken: RefreshToken;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    refreshTokenRepository = moduleFixture.get(
      getRepositoryToken(RefreshToken),
    );
    userRepository = moduleFixture.get(getRepositoryToken(User));
    accountRepository = moduleFixture.get(getRepositoryToken(Account));
    userAccountRepository = moduleFixture.get(getRepositoryToken(UserAccount));

    // Clean up any existing test data
    await refreshTokenRepository.delete({});
    await userAccountRepository.delete({});
    await userRepository.delete({ email: '<EMAIL>' });
    await accountRepository.delete({ name: 'Test Account' });

    // Create test user
    const passwordHash = await bcrypt.hash('password123', 10);
    testUser = await userRepository.save({
      email: '<EMAIL>',
      passwordHash,
    });

    // Create test account
    testAccount = await accountRepository.save({
      name: 'Test Account',
    });

    // Create user-account relationship
    await userAccountRepository.save({
      userId: testUser.id,
      accountId: testAccount.id,
      role: 'owner',
    });

    // Create test refresh token
    const token = crypto.randomBytes(64).toString('hex');
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    testRefreshToken = await refreshTokenRepository.save({
      userId: testUser.id,
      accountId: testAccount.id,
      token,
      expiresAt,
    });
  });

  afterAll(async () => {
    // Clean up test data
    await refreshTokenRepository.delete({});
    await userAccountRepository.delete({});
    await userRepository.delete({ email: '<EMAIL>' });
    await accountRepository.delete({ name: 'Test Account' });

    await app.close();
  });

  describe('/auth/logout (POST)', () => {
    it('should revoke a refresh token', async () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .send({ refreshToken: testRefreshToken.token })
        .expect(200)
        .expect({ success: true })
        .then(async () => {
          // Verify token was deleted
          const token = await refreshTokenRepository.findOne({
            where: { id: testRefreshToken.id },
          });
          expect(token).toBeNull();
        });
    });

    it('should return 404 for non-existent token', () => {
      return request(app.getHttpServer())
        .post('/auth/logout')
        .send({ refreshToken: 'non-existent-token' })
        .expect(404);
    });
  });
});
