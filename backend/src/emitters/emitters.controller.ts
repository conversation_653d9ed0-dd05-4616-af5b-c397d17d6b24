import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { UserRole } from '../database/entities/enums';
import { CreateEmitterDto } from './dto/create-emitter.dto';
import { UpdateEmitterConfigDto } from './dto/update-emitter-config.dto';
import { UpdateEmitterDto } from './dto/update-emitter.dto';
import { RotateWebhookSecretDto } from './dto/rotate-webhook-secret.dto';
import { EmittersService } from './emitters.service';

@Controller('emitters')
@UseGuards(JwtAuthGuard, RolesGuard)
export class EmittersController {
  constructor(private readonly emittersService: EmittersService) {}

  @Post()
  @Roles(UserRole.OWNER, UserRole.ADMIN)
  create(@Body() createEmitterDto: CreateEmitterDto, @CurrentUser() user) {
    return this.emittersService.create(user.accountId, createEmitterDto);
  }

  @Get()
  findAll(@CurrentUser() user) {
    return this.emittersService.findAll(user.accountId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @CurrentUser() user) {
    return this.emittersService.findOne(id, user.accountId);
  }

  @Patch(':id')
  @Roles(UserRole.OWNER, UserRole.ADMIN)
  update(
    @Param('id') id: string,
    @Body() updateEmitterDto: UpdateEmitterDto,
    @CurrentUser() user,
  ) {
    return this.emittersService.update(id, user.accountId, updateEmitterDto);
  }

  @Patch(':id/config')
  @Roles(UserRole.OWNER, UserRole.ADMIN)
  updateConfig(
    @Param('id') id: string,
    @Body() updateEmitterConfigDto: UpdateEmitterConfigDto,
    @CurrentUser() user,
  ) {
    return this.emittersService.updateConfig(
      id,
      user.accountId,
      updateEmitterConfigDto.series,
      updateEmitterConfigDto.currentNumber,
    );
  }

  @Patch(':id/webhook-secret')
  @Roles(UserRole.OWNER, UserRole.ADMIN)
  rotateWebhookSecret(
    @Param('id') id: string,
    @Body() rotateWebhookSecretDto: RotateWebhookSecretDto,
    @CurrentUser() user,
  ) {
    return this.emittersService.rotateWebhookSecret(id, user.accountId);
  }
}
