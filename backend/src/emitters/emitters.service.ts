import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as crypto from 'crypto';
import { CreateEmitterDto } from './dto/create-emitter.dto';
import { UpdateEmitterDto } from './dto/update-emitter.dto';
import { Emitter } from './entities/emitter.entity';

@Injectable()
export class EmittersService {
  constructor(
    @InjectRepository(Emitter)
    private emittersRepository: Repository<Emitter>,
  ) {}

  async create(
    accountId: string,
    createEmitterDto: CreateEmitterDto,
  ): Promise<Emitter> {
    // Check if emitter with the same CNPJ already exists
    const existingEmitter = await this.emittersRepository.findOne({
      where: { cnpj: createEmitterDto.cnpj },
    });

    if (existingEmitter) {
      throw new ConflictException('An emitter with this CNPJ already exists');
    }

    const emitter = this.emittersRepository.create({
      ...createEmitterDto,
      accountId,
    });

    return this.emittersRepository.save(emitter);
  }

  async findAll(accountId: string): Promise<Emitter[]> {
    return this.emittersRepository.find({
      where: { accountId },
    });
  }

  async findOne(id: string, accountId: string): Promise<Emitter> {
    const emitter = await this.emittersRepository.findOne({
      where: { id, accountId },
      relations: ['certificates'],
    });

    if (!emitter) {
      throw new NotFoundException(`Emitter with ID ${id} not found`);
    }

    return emitter;
  }

  async update(
    id: string,
    accountId: string,
    updateEmitterDto: UpdateEmitterDto,
  ): Promise<Emitter> {
    const emitter = await this.findOne(id, accountId);

    // Update the emitter
    Object.assign(emitter, updateEmitterDto);

    return this.emittersRepository.save(emitter);
  }

  async updateConfig(
    id: string,
    accountId: string,
    series: string,
    currentNumber: number,
  ): Promise<Emitter> {
    const emitter = await this.findOne(id, accountId);

    // Update the emitter configuration
    emitter.series = series;
    emitter.currentNumber = currentNumber;

    return this.emittersRepository.save(emitter);
  }

  /**
   * Rotate the webhook secret for an emitter
   * Generates a new random secret and returns it
   */
  async rotateWebhookSecret(id: string, accountId: string): Promise<{ secret: string }> {
    const emitter = await this.findOne(id, accountId);

    // Generate a new random secret
    const secret = crypto.randomBytes(32).toString('hex');

    // Update the emitter with the new secret
    emitter.webhookSecret = secret;
    await this.emittersRepository.save(emitter);

    // Return the new secret (it will only be shown once)
    return { secret };
  }
}
