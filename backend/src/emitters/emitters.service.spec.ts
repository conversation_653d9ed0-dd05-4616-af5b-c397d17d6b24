import { ConflictException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TaxRegime } from '../common/enums/tax-regime.enum';
import { CreateEmitterDto } from './dto/create-emitter.dto';
import { EmittersService } from './emitters.service';
import { Emitter } from './entities/emitter.entity';

const mockEmitterRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(),
  update: jest.fn(),
});

describe('EmittersService', () => {
  let service: EmittersService;
  let repository: Repository<Emitter>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmittersService,
        {
          provide: getRepositoryToken(Emitter),
          useFactory: mockEmitterRepository,
        },
      ],
    }).compile();

    service = module.get<EmittersService>(EmittersService);
    repository = module.get<Repository<Emitter>>(getRepositoryToken(Emitter));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new emitter', async () => {
      const createEmitterDto: CreateEmitterDto = {
        name: 'Test Emitter',
        cnpj: '**********1234',
        federalTaxId: '**********1234',
        taxRegime: TaxRegime.SIMPLE_NACIONAL,
        stateTaxId: '********9',
        cityTaxId: '123456',
        billingEmail: '<EMAIL>',
        phoneNumber: '**********',
        state: 'SP',
        city: 'São Paulo',
        addressLine1: 'Test Address',
        addressLine2: 'Test Address 2',
        zipCode: '********',
        cnae: '1234567',
        legalNature: '123-4',
        tradeName: 'Test Trade Name',
        municipalRegistrationDate: new Date(),
      };

      const mockEmitter = {
        id: 'test-id',
        ...createEmitterDto,
        accountId: 'test-account-id',
      };

      jest.spyOn(repository, 'findOne').mockResolvedValue(null);
      jest.spyOn(repository, 'create').mockReturnValue(mockEmitter as any);
      jest.spyOn(repository, 'save').mockResolvedValue(mockEmitter as any);

      const result = await service.create('test-account-id', createEmitterDto);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { cnpj: createEmitterDto.cnpj },
      });
      expect(repository.create).toHaveBeenCalledWith({
        ...createEmitterDto,
        accountId: 'test-account-id',
      });
      expect(repository.save).toHaveBeenCalledWith(mockEmitter);
      expect(result).toEqual(mockEmitter);
    });

    it('should throw ConflictException if emitter with same CNPJ already exists', async () => {
      const createEmitterDto: CreateEmitterDto = {
        name: 'Test Emitter',
        cnpj: '**********1234',
        federalTaxId: '**********1234',
        taxRegime: TaxRegime.SIMPLE_NACIONAL,
        stateTaxId: '********9',
        cityTaxId: '123456',
        billingEmail: '<EMAIL>',
        phoneNumber: '**********',
        state: 'SP',
        city: 'São Paulo',
        addressLine1: 'Test Address',
        addressLine2: 'Test Address 2',
        zipCode: '********',
        cnae: '1234567',
        legalNature: '123-4',
        tradeName: 'Test Trade Name',
        municipalRegistrationDate: new Date(),
      };

      const existingEmitter = {
        id: 'existing-id',
        ...createEmitterDto,
        accountId: 'test-account-id',
      };

      jest
        .spyOn(repository, 'findOne')
        .mockResolvedValue(existingEmitter as any);

      await expect(
        service.create('test-account-id', createEmitterDto),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('findAll', () => {
    it('should return all emitters for an account', async () => {
      const mockEmitters = [
        {
          id: 'test-id-1',
          name: 'Test Emitter 1',
          accountId: 'test-account-id',
        },
        {
          id: 'test-id-2',
          name: 'Test Emitter 2',
          accountId: 'test-account-id',
        },
      ];

      jest.spyOn(repository, 'find').mockResolvedValue(mockEmitters as any);

      const result = await service.findAll('test-account-id');

      expect(repository.find).toHaveBeenCalledWith({
        where: { accountId: 'test-account-id' },
      });
      expect(result).toEqual(mockEmitters);
    });
  });

  describe('findOne', () => {
    it('should return an emitter if found', async () => {
      const mockEmitter = {
        id: 'test-id',
        name: 'Test Emitter',
        accountId: 'test-account-id',
      };

      jest.spyOn(repository, 'findOne').mockResolvedValue(mockEmitter as any);

      const result = await service.findOne('test-id', 'test-account-id');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id', accountId: 'test-account-id' },
        relations: ['certificates'],
      });
      expect(result).toEqual(mockEmitter);
    });

    it('should throw NotFoundException if emitter not found', async () => {
      jest.spyOn(repository, 'findOne').mockResolvedValue(null);

      await expect(
        service.findOne('test-id', 'test-account-id'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateConfig', () => {
    it('should update emitter configuration', async () => {
      const mockEmitter = {
        id: 'test-id',
        name: 'Test Emitter',
        accountId: 'test-account-id',
        series: '1',
        currentNumber: 1,
      };

      const updatedEmitter = {
        ...mockEmitter,
        series: '2',
        currentNumber: 2,
      };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockEmitter as any);
      jest.spyOn(repository, 'save').mockResolvedValue(updatedEmitter as any);

      const result = await service.updateConfig(
        'test-id',
        'test-account-id',
        '2',
        2,
      );

      expect(service.findOne).toHaveBeenCalledWith(
        'test-id',
        'test-account-id',
      );
      expect(repository.save).toHaveBeenCalledWith({
        ...mockEmitter,
        series: '2',
        currentNumber: 2,
      });
      expect(result).toEqual(updatedEmitter);
    });

    it('should throw NotFoundException if emitter not found', async () => {
      jest.spyOn(service, 'findOne').mockImplementation(() => {
        throw new NotFoundException(`Emitter with ID test-id not found`);
      });

      await expect(
        service.updateConfig('test-id', 'test-account-id', '2', 2),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('rotateWebhookSecret', () => {
    it('should generate and save a new webhook secret', async () => {
      const mockEmitter = {
        id: 'test-id',
        name: 'Test Emitter',
        accountId: 'test-account-id',
        webhookSecret: 'old-secret',
      };

      const updatedEmitter = {
        ...mockEmitter,
        webhookSecret: 'new-secret',
      };

      // Mock crypto.randomBytes to return a predictable value
      jest.spyOn(crypto, 'randomBytes').mockImplementation(() => {
        return {
          toString: jest.fn().mockReturnValue('new-secret'),
        } as any;
      });

      jest.spyOn(service, 'findOne').mockResolvedValue(mockEmitter as any);
      jest.spyOn(repository, 'save').mockResolvedValue(updatedEmitter as any);

      const result = await service.rotateWebhookSecret(
        'test-id',
        'test-account-id',
      );

      expect(service.findOne).toHaveBeenCalledWith(
        'test-id',
        'test-account-id',
      );
      expect(repository.save).toHaveBeenCalledWith({
        ...mockEmitter,
        webhookSecret: 'new-secret',
      });
      expect(result).toEqual({ secret: 'new-secret' });
    });

    it('should throw NotFoundException if emitter not found', async () => {
      jest.spyOn(service, 'findOne').mockImplementation(() => {
        throw new NotFoundException(`Emitter with ID test-id not found`);
      });

      await expect(
        service.rotateWebhookSecret('test-id', 'test-account-id'),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
