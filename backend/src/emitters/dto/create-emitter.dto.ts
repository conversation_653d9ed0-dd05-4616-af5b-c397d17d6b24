import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { TaxRegime } from '../../common/enums/tax-regime.enum';

export class CreateEmitterDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  federalTaxId: string;

  @IsString()
  @IsNotEmpty()
  cnpj: string;

  @IsString()
  @IsOptional()
  stateTaxId?: string;

  @IsString()
  @IsOptional()
  cityTaxId?: string;

  @IsEmail()
  @IsNotEmpty()
  billingEmail: string;

  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsNotEmpty()
  state: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @IsString()
  @IsOptional()
  addressLine2?: string;

  @IsString()
  @IsNotEmpty()
  zipCode: string;

  @IsEnum(TaxRegime)
  @IsNotEmpty()
  taxRegime: TaxRegime;

  @IsString()
  @IsNotEmpty()
  cnae: string;

  @IsString()
  @IsNotEmpty()
  legalNature: string;

  @IsString()
  @IsNotEmpty()
  tradeName: string;

  @IsString()
  @IsOptional()
  municipalRegistrationDate?: Date;

  /** Optional webhook callback URL for this emitter */
  @IsString()
  @IsOptional()
  webhookUrl?: string;

  /** Optional secret for signing webhook payloads */
  @IsString()
  @IsOptional()
  webhookSecret?: string;
}
