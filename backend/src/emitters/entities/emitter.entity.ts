import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Account } from '../../accounts/entities/account.entity';
import { Certificate } from '../../certificates/entities/certificate.entity';
import { CertificateStatus } from '../../common/enums/certificate-status.enum';
import { TaxRegime } from '../../common/enums/tax-regime.enum';
import { NFeDocument } from '../../nfe/entities/nfe-document.entity';

@Entity('emitters')
export class Emitter {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  accountId: string;

  @ManyToOne(() => Account, (account) => account.emitters)
  account: Account;

  @Column()
  name: string;

  @Column({ unique: true })
  cnpj: string;

  @Column({ unique: true })
  federalTaxId: string;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'varchar' : 'enum',
    enum: TaxRegime,
    default: TaxRegime.SIMPLE_NACIONAL,
  })
  taxRegime: TaxRegime;

  @Column()
  stateTaxId: string;

  @Column()
  cityTaxId: string;

  @Column()
  addressLine1: string;

  @Column({ nullable: true })
  addressLine2: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column()
  zipCode: string;

  @Column()
  phoneNumber: string;

  @Column({ unique: true })
  billingEmail: string;

  @Column()
  cnae: string;

  @Column()
  legalNature: string;

  @Column()
  tradeName: string;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'datetime' : 'timestamp',
    nullable: true,
  })
  municipalRegistrationDate: Date;

  @Column({ default: 1 })
  currentNumber: number;

  @Column({ nullable: true })
  series: string;

  @Column({ nullable: true })
  webhookUrl: string;

  @Column({ nullable: true })
  webhookSecret: string;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'varchar' : 'enum',
    enum: CertificateStatus,
    default: CertificateStatus.PENDING,
  })
  certificateStatus: CertificateStatus;

  @OneToMany(() => Certificate, (certificate) => certificate.emitter)
  certificates: Certificate[];

  @OneToMany(() => NFeDocument, (nfeDocument) => nfeDocument.emitter)
  nfeDocuments: NFeDocument[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
