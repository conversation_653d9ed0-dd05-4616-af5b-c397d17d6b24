import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmittersController } from './emitters.controller';
import { EmittersService } from './emitters.service';
import { Emitter } from './entities/emitter.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Emitter])],
  controllers: [EmittersController],
  providers: [EmittersService],
  exports: [EmittersService],
})
export class EmittersModule {}
