import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { EmittersController } from './emitters.controller';
import { EmittersService } from './emitters.service';
import { RolesGuard } from '../auth/guards/roles.guard';
import { UserRole } from '../users/entities/user-account.entity';
import { CreateEmitterDto } from './dto/create-emitter.dto';
import { UpdateEmitterDto } from './dto/update-emitter.dto';
import { UpdateEmitterConfigDto } from './dto/update-emitter-config.dto';

const mockEmittersService = () => ({
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  updateConfig: jest.fn(),
});

describe('EmittersController', () => {
  let controller: EmittersController;
  let emittersService: EmittersService;
  let rolesGuard: RolesGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmittersController],
      providers: [
        {
          provide: EmittersService,
          useFactory: mockEmittersService,
        },
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
        RolesGuard,
      ],
    }).compile();

    controller = module.get<EmittersController>(EmittersController);
    emittersService = module.get<EmittersService>(EmittersService);
    rolesGuard = module.get<RolesGuard>(RolesGuard);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('Role-based access control', () => {
    const mockExecutionContext = {
      getHandler: jest.fn(),
      getClass: jest.fn(),
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn(),
      }),
    };

    it('should allow owner role to create emitters', () => {
      // Mock the reflector to return the required roles
      jest.spyOn(module.get(Reflector), 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      // Mock the request with an owner role
      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: { role: UserRole.OWNER },
      });

      expect(rolesGuard.canActivate(mockExecutionContext as any)).toBe(true);
    });

    it('should allow admin role to create emitters', () => {
      // Mock the reflector to return the required roles
      jest.spyOn(module.get(Reflector), 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      // Mock the request with an admin role
      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: { role: UserRole.ADMIN },
      });

      expect(rolesGuard.canActivate(mockExecutionContext as any)).toBe(true);
    });

    it('should deny member role from creating emitters', () => {
      // Mock the reflector to return the required roles
      jest.spyOn(module.get(Reflector), 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      // Mock the request with a member role
      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: { role: UserRole.MEMBER },
      });

      expect(() => rolesGuard.canActivate(mockExecutionContext as any)).toThrow(
        ForbiddenException,
      );
    });
  });

  describe('create', () => {
    it('should call emittersService.create with the correct parameters', async () => {
      const createEmitterDto: CreateEmitterDto = {
        name: 'Test Emitter',
        cnpj: '**************',
      };
      const user = { accountId: 'account-id' };
      const expectedResult = { id: 'emitter-id', ...createEmitterDto };

      jest.spyOn(emittersService, 'create').mockResolvedValue(expectedResult as any);

      const result = await controller.create(createEmitterDto, user);

      expect(emittersService.create).toHaveBeenCalledWith(
        user.accountId,
        createEmitterDto,
      );
      expect(result).toEqual(expectedResult);
    });
  });

  describe('update', () => {
    it('should call emittersService.update with the correct parameters', async () => {
      const id = 'emitter-id';
      const updateEmitterDto: UpdateEmitterDto = {
        name: 'Updated Emitter',
      };
      const user = { accountId: 'account-id' };
      const expectedResult = { id, name: 'Updated Emitter' };

      jest.spyOn(emittersService, 'update').mockResolvedValue(expectedResult as any);

      const result = await controller.update(id, updateEmitterDto, user);

      expect(emittersService.update).toHaveBeenCalledWith(
        id,
        user.accountId,
        updateEmitterDto,
      );
      expect(result).toEqual(expectedResult);
    });
  });

  describe('updateConfig', () => {
    it('should call emittersService.updateConfig with the correct parameters', async () => {
      const id = 'emitter-id';
      const updateEmitterConfigDto: UpdateEmitterConfigDto = {
        series: 1,
        currentNumber: 100,
      };
      const user = { accountId: 'account-id' };
      const expectedResult = { id, series: 1, currentNumber: 100 };

      jest.spyOn(emittersService, 'updateConfig').mockResolvedValue(expectedResult as any);

      const result = await controller.updateConfig(id, updateEmitterConfigDto, user);

      expect(emittersService.updateConfig).toHaveBeenCalledWith(
        id,
        user.accountId,
        updateEmitterConfigDto.series,
        updateEmitterConfigDto.currentNumber,
      );
      expect(result).toEqual(expectedResult);
    });
  });
});
