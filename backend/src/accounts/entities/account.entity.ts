import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { UserAccount } from '../../users/entities/user-account.entity';
import { Emitter } from '../../emitters/entities/emitter.entity';
import { Api<PERSON>ey } from '../../integration/entities/api-key.entity';

@Entity('accounts')
export class Account {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => UserAccount, (userAccount) => userAccount.account)
  userAccounts: UserAccount[];

  @OneToMany(() => Emitter, (emitter) => emitter.account)
  emitters: Emitter[];

  @OneToMany(() => ApiKey, (apiKey) => apiKey.account)
  apiKeys: Api<PERSON><PERSON>[];
}
