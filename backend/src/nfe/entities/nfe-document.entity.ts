import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { Emitter } from '../../emitters/entities/emitter.entity';
import { DocumentStorage } from '../../storage/entities/document-storage.entity';

export enum NFeStatus {
  CREATED = 'created',
  SIGNED = 'signed',
  SENT = 'sent',
  AUTHORIZED = 'authorized',
  DENIED = 'denied',
  CANCELLED = 'cancelled',
  ERROR = 'error',
}

@Entity('nfe_documents')
@Unique(['emitterId', 'documentNumber', 'series'])
export class NFeDocument {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  emitterId: string;

  @Column()
  documentNumber: number;

  @Column()
  series: number;

  /** Raw JSON payload for NF-e issuance */
  @Column({ type: 'simple-json', nullable: true })
  payload: any;

  @Column({ type: 'text', nullable: true })
  xmlPayload: string;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'varchar' : 'enum',
    enum: NFeStatus,
    default: NFeStatus.CREATED,
  })
  status: NFeStatus;

  @Column({ nullable: true })
  receiptNumber: string | null;

  @Column({ nullable: true })
  protocol: string | null;

  @Column({ nullable: true })
  errorMessage: string | null;

  @CreateDateColumn()
  generatedAt: Date;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'datetime' : 'timestamp',
    nullable: true,
  })
  transmittedAt: Date;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'datetime' : 'timestamp',
    nullable: true,
  })
  authorizedAt: Date;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'datetime' : 'timestamp',
    nullable: true,
  })
  cancelledAt: Date;
  /** Reason provided for cancellation of the document */
  @Column({ type: 'text', nullable: true })
  cancellationReason: string;

  @ManyToOne(() => Emitter, (emitter) => emitter.nfeDocuments)
  @JoinColumn({ name: 'emitterId' })
  emitter: Emitter;

  @OneToOne(() => DocumentStorage, (storage) => storage.nfeDocument)
  storage: DocumentStorage;
}
