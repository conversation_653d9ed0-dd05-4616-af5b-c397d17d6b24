import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { NfeService } from '../nfe.service';

@Processor('nfe:emit')
export class NfeEmitProcessor {
  private readonly logger = new Logger(NfeEmitProcessor.name);

  constructor(private readonly nfeService: NfeService) {}

  @Process('emit')
  async handleEmit(
    job: Job<{
      nfeId: string;
      accountId: string;
      emitterId: string;
      payload: any;
    }>,
  ) {
    this.logger.debug(
      `Processing NFe emission job ${job.id} for document ${job.data.nfeId}`,
    );

    try {
      await this.nfeService.emitDocument(job.data.nfeId);
      job.updateProgress(100);
      this.logger.debug(`NFe emission job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(
        `Error processing NFe emission job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
