import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bullmq';
import { Repository } from 'typeorm';
import { Certificate } from '../../certificates/entities/certificate.entity';
import { CertificateStatus } from '../../common/enums/certificate-status.enum';
import { Emitter } from '../../emitters/entities/emitter.entity';
import { NFeDocument, NFeStatus } from '../entities/nfe-document.entity';

@Processor('nfe:transmit')
@Injectable()
export class NfeTransmitProcessor {
  private readonly logger = new Logger(NfeTransmitProcessor.name);

  constructor(
    @InjectRepository(NFeDocument)
    private readonly nfeDocumentRepository: Repository<NFeDocument>,
    @InjectRepository(Emitter)
    private readonly emitterRepository: Repository<Emitter>,
    @InjectRepository(Certificate)
    private readonly certificateRepository: Repository<Certificate>,
  ) {}

  @Process('transmit')
  async handleTransmit(
    job: Job<{
      nfeId: string;
    }>,
  ) {
    const { nfeId } = job.data;
    this.logger.debug(`Processing NFe transmission job ${job.id} for document ${nfeId}`);

    try {
      // Load NFe document with emitter relation
      const nfeDocument = await this.nfeDocumentRepository.findOne({
        where: { id: nfeId },
        relations: ['emitter'],
      });

      if (!nfeDocument) {
        throw new Error(`NFe document with ID ${nfeId} not found`);
      }

      if (nfeDocument.status !== NFeStatus.SIGNED) {
        throw new Error(`NFe document ${nfeId} is not in signed status (current: ${nfeDocument.status})`);
      }

      // Get active certificate for the emitter
      const certificate = await this.certificateRepository.findOne({
        where: {
          emitterId: nfeDocument.emitterId,
          status: CertificateStatus.VALID,
        },
      });

      if (!certificate) {
        throw new Error(`No valid certificate found for emitter ${nfeDocument.emitterId}`);
      }

      // Update status to SENT before transmission
      nfeDocument.status = NFeStatus.SENT;
      nfeDocument.transmittedAt = new Date();
      await this.nfeDocumentRepository.save(nfeDocument);

      job.updateProgress(25);

      // Prepare transmission data for PHP Emissor
      const transmissionData = {
        nfeId: nfeDocument.id,
        emitterId: nfeDocument.emitterId,
        documentNumber: nfeDocument.documentNumber,
        series: nfeDocument.series,
        xmlPayload: nfeDocument.xmlPayload,
        certificateId: certificate.id,
        certificatePath: certificate.pfxEncrypted, // Encrypted PFX file
        certificatePassword: certificate.password, // Certificate password
        environment: 'PROD', // Default environment
      };

      job.updateProgress(50);

      // Call PHP Emissor API
      const transmissionResult = await this.transmitToPhpEmissor(transmissionData);

      job.updateProgress(75);

      // Process the response from PHP Emissor
      await this.processTransmissionResult(nfeDocument, transmissionResult);

      job.updateProgress(100);

      this.logger.debug(`NFe transmission job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(
        `Error processing NFe transmission job ${job.id}: ${error.message}`,
        error.stack,
      );

      // Update document status to ERROR
      try {
        const nfeDocument = await this.nfeDocumentRepository.findOne({
          where: { id: nfeId },
        });

        if (nfeDocument) {
          nfeDocument.status = NFeStatus.ERROR;
          nfeDocument.errorMessage = error.message;
          await this.nfeDocumentRepository.save(nfeDocument);
        }
      } catch (updateError) {
        this.logger.error('Failed to update NFe document error status', updateError);
      }

      throw error;
    }
  }

  /**
   * Transmit NFe to PHP Emissor API
   */
  private async transmitToPhpEmissor(data: {
    nfeId: string;
    emitterId: string;
    documentNumber: number;
    series: number;
    xmlPayload: string;
    certificateId: string;
    certificatePath: string;
    certificatePassword: string;
    environment: string;
  }): Promise<{
    success: boolean;
    protocol?: string;
    receiptNumber?: string;
    errorMessage?: string;
    errorCode?: string;
    responseXml?: string;
  }> {
    // TODO: Replace this with actual HTTP call to PHP Emissor
    // For now, simulate the API call
    
    this.logger.debug(`Transmitting NFe ${data.nfeId} to PHP Emissor`);
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // For development/testing purposes, simulate different outcomes
    const outcomes = ['success', 'rejection', 'error'];
    const outcome = outcomes[Math.floor(Math.random() * outcomes.length)];

    switch (outcome) {
      case 'success':
        return {
          success: true,
          protocol: `141${Date.now().toString().slice(-9)}001`,
          receiptNumber: `141${Date.now().toString().slice(-9)}001`,
        };
      
      case 'rejection':
        return {
          success: false,
          errorMessage: 'Rejeição 999: Documento rejeitado pela SEFAZ',
          errorCode: '999',
        };
      
      case 'error':
        return {
          success: false,
          errorMessage: 'Erro de comunicação com a SEFAZ',
          errorCode: 'COMM_ERROR',
        };
      
      default:
        throw new Error('Unexpected outcome');
    }

    /* TODO: Real implementation would look like this:
    
    const phpEmissorUrl = process.env.PHP_EMISSOR_URL || 'http://localhost:8080/api/transmit';
    
    try {
      const response = await fetch(phpEmissorUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': process.env.PHP_EMISSOR_API_KEY,
        },
        body: JSON.stringify(data),
        timeout: 30000, // 30 seconds
      });

      if (!response.ok) {
        throw new Error(`PHP Emissor API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      this.logger.error('Failed to communicate with PHP Emissor', error);
      throw new Error(`PHP Emissor communication error: ${error.message}`);
    }
    */
  }

  /**
   * Process the transmission result and update the NFe document
   */
  private async processTransmissionResult(
    nfeDocument: NFeDocument,
    result: {
      success: boolean;
      protocol?: string;
      receiptNumber?: string;
      errorMessage?: string;
      errorCode?: string;
      responseXml?: string;
    },
  ): Promise<void> {
    if (result.success) {
      // NFe was successfully authorized by SEFAZ
      nfeDocument.status = NFeStatus.AUTHORIZED;
      nfeDocument.protocol = result.protocol || null;
      nfeDocument.receiptNumber = result.receiptNumber || null;
      nfeDocument.authorizedAt = new Date();
      nfeDocument.errorMessage = null;

      this.logger.log(
        `NFe ${nfeDocument.id} successfully authorized with protocol ${result.protocol}`,
      );

      // TODO: Trigger DANFE generation queue
      // await this.nfeDanfeQueue.add('generate', { nfeId: nfeDocument.id });

      // TODO: Dispatch webhook notification
      // await this.webhookDispatcher.dispatch('nfe.authorized', nfeDocument);
    } else {
      // NFe was rejected or encountered an error
      nfeDocument.status = NFeStatus.DENIED;
      nfeDocument.errorMessage = result.errorMessage || null;
      nfeDocument.protocol = null;
      nfeDocument.receiptNumber = null;

      this.logger.error(
        `NFe ${nfeDocument.id} transmission failed: ${result.errorMessage}`,
      );

      // TODO: Dispatch webhook notification
      // await this.webhookDispatcher.dispatch('nfe.denied', nfeDocument);
    }

    await this.nfeDocumentRepository.save(nfeDocument);
  }
}
