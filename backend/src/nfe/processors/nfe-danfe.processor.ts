import { Process, Processor } from '@nestjs/bull';
import { Inject, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NFeDocument } from '../entities/nfe-document.entity';
import { DocumentStorage } from '../../storage/entities/document-storage.entity';
import { IStorageProvider } from '../../storage/interfaces/storage-provider.interface';

@Processor('nfe:danfe')
export class NfeDanfeProcessor {
  private readonly logger = new Logger(NfeDanfeProcessor.name);

  constructor(
    @InjectRepository(NFeDocument)
    private nfeDocumentsRepository: Repository<NFeDocument>,
    @InjectRepository(DocumentStorage)
    private documentStorageRepository: Repository<DocumentStorage>,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  @Process('generate')
  async handleGenerate(job: Job<{ nfeId: string }>) {
    this.logger.debug(
      `Processing DANFE generation job ${job.id} for document ${job.data.nfeId}`,
    );

    try {
      // Get the NFe document
      const nfeDocument = await this.nfeDocumentsRepository.findOne({
        where: { id: job.data.nfeId },
      });

      if (!nfeDocument) {
        throw new Error(`NFe document with ID ${job.data.nfeId} not found`);
      }

      // Store XML via storage provider
      const xmlPath = await this.storageProvider.saveXml(
        nfeDocument.id,
        nfeDocument.xmlPayload,
      );
      // Simulate DANFE generation and store PDF
      const danfeContent = Buffer.from('Simulated DANFE PDF content', 'utf8');
      const danfePath = await this.storageProvider.saveDanfe(
        nfeDocument.id,
        danfeContent,
      );

      // Create or update storage record
      let storage = await this.documentStorageRepository.findOne({
        where: { nfeDocumentId: nfeDocument.id },
      });

      if (storage) {
        storage.xmlPath = xmlPath;
        storage.danfePath = danfePath;
      } else {
        storage = this.documentStorageRepository.create({
          nfeDocumentId: nfeDocument.id,
          xmlPath,
          danfePath,
        });
      }

      await this.documentStorageRepository.save(storage);

      job.updateProgress(100);
      this.logger.debug(
        `DANFE generation job ${job.id} completed successfully`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing DANFE generation job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
