import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bullmq';
import { Repository } from 'typeorm';
import { Certificate } from '../../certificates/entities/certificate.entity';
import { CertificateStatus } from '../../common/enums/certificate-status.enum';
import { Emitter } from '../../emitters/entities/emitter.entity';
import { NFeDocument, NFeStatus } from '../entities/nfe-document.entity';

@Processor('nfe:cancel')
@Injectable()
export class NfeCancelProcessor {
  private readonly logger = new Logger(NfeCancelProcessor.name);

  constructor(
    @InjectRepository(NFeDocument)
    private readonly nfeDocumentRepository: Repository<NFeDocument>,
    @InjectRepository(Emitter)
    private readonly emitterRepository: Repository<Emitter>,
    @InjectRepository(Certificate)
    private readonly certificateRepository: Repository<Certificate>,
  ) {}

  @Process('cancel')
  async handleCancel(
    job: Job<{
      nfeId: string;
      justification: string;
    }>,
  ) {
    const { nfeId, justification } = job.data;
    this.logger.debug(`Processing NFe cancellation job ${job.id} for document ${nfeId}`);

    try {
      // Load NFe document with emitter relation
      const nfeDocument = await this.nfeDocumentRepository.findOne({
        where: { id: nfeId },
        relations: ['emitter'],
      });

      if (!nfeDocument) {
        throw new Error(`NFe document with ID ${nfeId} not found`);
      }

      if (nfeDocument.status !== NFeStatus.AUTHORIZED) {
        throw new Error(`NFe document ${nfeId} cannot be cancelled (current status: ${nfeDocument.status}). Only authorized documents can be cancelled.`);
      }

      if (!nfeDocument.protocol) {
        throw new Error(`NFe document ${nfeId} has no protocol number, cannot cancel`);
      }

      // Get active certificate for the emitter
      const certificate = await this.certificateRepository.findOne({
        where: {
          emitterId: nfeDocument.emitterId,
          status: CertificateStatus.VALID,
        },
      });

      if (!certificate) {
        throw new Error(`No valid certificate found for emitter ${nfeDocument.emitterId}`);
      }

      job.updateProgress(25);

      // Prepare cancellation data for PHP Emissor
      const cancellationData = {
        nfeId: nfeDocument.id,
        emitterId: nfeDocument.emitterId,
        documentNumber: nfeDocument.documentNumber,
        series: nfeDocument.series,
        protocol: nfeDocument.protocol,
        justification: justification,
        certificateId: certificate.id,
        certificatePath: certificate.pfxEncrypted,
        certificatePassword: certificate.password,
        environment: 'PROD', // Default environment
        accessKey: this.generateAccessKey(nfeDocument), // Generate access key for cancellation
      };

      job.updateProgress(50);

      // Call PHP Emissor API for cancellation
      const cancellationResult = await this.cancelWithPhpEmissor(cancellationData);

      job.updateProgress(75);

      // Process the cancellation response
      await this.processCancellationResult(nfeDocument, justification, cancellationResult);

      job.updateProgress(100);

      this.logger.debug(`NFe cancellation job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(
        `Error processing NFe cancellation job ${job.id}: ${error.message}`,
        error.stack,
      );

      // Update document with error information, but don't change status from AUTHORIZED
      try {
        const nfeDocument = await this.nfeDocumentRepository.findOne({
          where: { id: nfeId },
        });

        if (nfeDocument) {
          // Keep the document as AUTHORIZED but log the cancellation error
          nfeDocument.errorMessage = `Cancellation failed: ${error.message}`;
          await this.nfeDocumentRepository.save(nfeDocument);
        }
      } catch (updateError) {
        this.logger.error('Failed to update NFe document with cancellation error', updateError);
      }

      throw error;
    }
  }

  /**
   * Cancel NFe with PHP Emissor API
   */
  private async cancelWithPhpEmissor(data: {
    nfeId: string;
    emitterId: string;
    documentNumber: number;
    series: number;
    protocol: string;
    justification: string;
    certificateId: string;
    certificatePath: string;
    certificatePassword: string;
    environment: string;
    accessKey: string;
  }): Promise<{
    success: boolean;
    cancellationProtocol?: string;
    errorMessage?: string;
    errorCode?: string;
  }> {
    // TODO: Replace this with actual HTTP call to PHP Emissor
    // For now, simulate the API call
    
    this.logger.debug(`Cancelling NFe ${data.nfeId} via PHP Emissor`);
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1500));

    // For development/testing purposes, simulate different outcomes
    const outcomes = ['success', 'error'];
    const outcome = outcomes[Math.floor(Math.random() * outcomes.length)];

    switch (outcome) {
      case 'success':
        return {
          success: true,
          cancellationProtocol: `141${Date.now().toString().slice(-9)}002`,
        };
      
      case 'error':
        return {
          success: false,
          errorMessage: 'Erro ao cancelar NFe: Documento já foi cancelado anteriormente',
          errorCode: 'ALREADY_CANCELLED',
        };
      
      default:
        throw new Error('Unexpected outcome');
    }

    /* TODO: Real implementation would look like this:
    
    const phpEmissorUrl = process.env.PHP_EMISSOR_URL || 'http://localhost:8080/api/cancel';
    
    try {
      const response = await fetch(phpEmissorUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': process.env.PHP_EMISSOR_API_KEY,
        },
        body: JSON.stringify(data),
        timeout: 30000, // 30 seconds
      });

      if (!response.ok) {
        throw new Error(`PHP Emissor API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      this.logger.error('Failed to communicate with PHP Emissor for cancellation', error);
      throw new Error(`PHP Emissor communication error: ${error.message}`);
    }
    */
  }

  /**
   * Process the cancellation result and update the NFe document
   */
  private async processCancellationResult(
    nfeDocument: NFeDocument,
    justification: string,
    result: {
      success: boolean;
      cancellationProtocol?: string;
      errorMessage?: string;
      errorCode?: string;
    },
  ): Promise<void> {
    if (result.success) {
      // NFe was successfully cancelled
      nfeDocument.status = NFeStatus.CANCELLED;
      nfeDocument.cancelledAt = new Date();
      nfeDocument.cancellationReason = justification;
      nfeDocument.errorMessage = null;

      // Store cancellation protocol if provided
      if (result.cancellationProtocol) {
        // You might want to add a cancellationProtocol field to NFeDocument entity
        nfeDocument.errorMessage = `Cancelled with protocol: ${result.cancellationProtocol}`;
      }

      this.logger.log(
        `NFe ${nfeDocument.id} successfully cancelled with protocol ${result.cancellationProtocol}`,
      );

      // TODO: Dispatch webhook notification
      // await this.webhookDispatcher.dispatch('nfe.cancelled', {
      //   ...nfeDocument,
      //   cancellationProtocol: result.cancellationProtocol,
      //   cancellationReason: justification,
      // });
    } else {
      // Cancellation failed
      nfeDocument.errorMessage = `Cancellation failed: ${result.errorMessage}`;

      this.logger.error(
        `NFe ${nfeDocument.id} cancellation failed: ${result.errorMessage}`,
      );

      // TODO: Dispatch webhook notification for cancellation error
      // await this.webhookDispatcher.dispatch('nfe.error', {
      //   ...nfeDocument,
      //   errorType: 'cancellation_failed',
      //   errorMessage: result.errorMessage,
      // });
    }

    await this.nfeDocumentRepository.save(nfeDocument);
  }

  /**
   * Generate access key for the NFe document
   * This is a simplified version - real implementation should follow SEFAZ standards
   */
  private generateAccessKey(nfeDocument: NFeDocument): string {
    // TODO: Implement proper access key generation according to SEFAZ standards
    // Access key format: UF + AAMM + CNPJ + modelo + série + número + código_numérico + dv
    
    // For now, return a placeholder
    const uf = '35'; // São Paulo
    const yearMonth = new Date().toISOString().slice(2, 7).replace('-', ''); // YYMM
    const cnpj = nfeDocument.emitter?.cnpj?.replace(/\D/g, '') || '00000000000000';
    const model = '55'; // NFe model
    const series = nfeDocument.series.toString().padStart(3, '0');
    const number = nfeDocument.documentNumber.toString().padStart(9, '0');
    const randomCode = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
    
    const baseKey = uf + yearMonth + cnpj + model + series + number + randomCode;
    const checkDigit = this.calculateCheckDigit(baseKey);
    
    return baseKey + checkDigit;
  }

  /**
   * Calculate check digit for access key
   * This is a simplified version - real implementation should follow SEFAZ algorithm
   */
  private calculateCheckDigit(baseKey: string): string {
    // TODO: Implement proper check digit calculation according to SEFAZ standards
    // For now, return a simple modulo calculation
    const sum = baseKey.split('').reduce((acc, digit) => acc + parseInt(digit), 0);
    return (sum % 10).toString();
  }
}
