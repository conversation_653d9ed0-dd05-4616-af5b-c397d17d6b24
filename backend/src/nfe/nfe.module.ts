import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CertificatesModule } from '../certificates/certificates.module';
import { Certificate } from '../certificates/entities/certificate.entity';
import { EmittersModule } from '../emitters/emitters.module';
import { Emitter } from '../emitters/entities/emitter.entity';
import { DocumentStorage } from '../storage/entities/document-storage.entity';
import { NFeDocument } from './entities/nfe-document.entity';
import { NfeController } from './nfe.controller';
import { NfeService } from './nfe.service';
import { NfeDanfeProcessor } from './processors/nfe-danfe.processor';
import { NfeEmitProcessor } from './processors/nfe-emit.processor';
import { NfeTransmitProcessor } from './processors/nfe-transmit.processor';
import { NfeCancelProcessor } from './processors/nfe-cancel.processor';
import { NfeReprocessTask } from './tasks/nfe-reprocess.task';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      NFeDocument,
      Emitter,
      Certificate,
      DocumentStorage,
    ]),
    BullModule.registerQueue(
      { name: 'nfe:emit' },
      { name: 'nfe:danfe' },
      { name: 'nfe:transmit' },
      { name: 'nfe:cancel' },
    ),
    ScheduleModule.forRoot(),
    EmittersModule,
    CertificatesModule,
  ],
  controllers: [NfeController],
  providers: [
    NfeService,
    NfeEmitProcessor,
    NfeDanfeProcessor,
    NfeTransmitProcessor,
    NfeCancelProcessor,
    NfeReprocessTask,
  ],
  exports: [NfeService],
})
export class NfeModule {}
