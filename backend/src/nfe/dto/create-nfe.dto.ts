import {
  IsNotEmpty,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CustomerAddressDto {
  @IsString()
  @IsNotEmpty()
  street: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  neighborhood: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  state: string;

  @IsString()
  @IsNotEmpty()
  zipCode: string;
}

class CustomerDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  federalTaxId: string;

  @IsString()
  @IsNotEmpty()
  email: string;

  @ValidateNested()
  @Type(() => CustomerAddressDto)
  address: CustomerAddressDto;
}

class ProductDto {
  @IsString()
  @IsNotEmpty()
  description: string;

  @IsNotEmpty()
  quantity: number;

  @IsNotEmpty()
  price: number;

  @IsString()
  @IsNotEmpty()
  taxCode: string;
}

class PaymentDto {
  @IsString()
  @IsNotEmpty()
  method: string;

  @IsNotEmpty()
  amount: number;
}

class NfePayloadDto {
  @ValidateNested()
  @Type(() => CustomerDto)
  customer: CustomerDto;

  @ValidateNested({ each: true })
  @Type(() => ProductDto)
  products: ProductDto[];

  @ValidateNested()
  @Type(() => PaymentDto)
  payment: PaymentDto;
}

export class CreateNfeDto {
  @IsString()
  @IsNotEmpty()
  emitterId: string;

  @IsObject()
  @ValidateNested()
  @Type(() => NfePayloadDto)
  payload: NfePayloadDto;
}
