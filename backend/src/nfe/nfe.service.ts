import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bullmq';
import { DataSource, Repository } from 'typeorm';
import { Certificate } from '../certificates/entities/certificate.entity';
import { CertificateStatus } from '../common/enums/certificate-status.enum';
import { PaginationDto } from '../common/dto/pagination.dto';
import { PaginationResult } from '../common/interfaces/pagination-result.interface';
import { Emitter } from '../emitters/entities/emitter.entity';
import { CancelNfeDto } from './dto/cancel-nfe.dto';
import { CreateNfeDto } from './dto/create-nfe.dto';
import { NFeDocument, NFeStatus } from './entities/nfe-document.entity';

@Injectable()
export class NfeService {
  constructor(
    @InjectRepository(NFeDocument)
    private nfeDocumentsRepository: Repository<NFeDocument>,
    @InjectRepository(Emitter)
    private emittersRepository: Repository<Emitter>,
    @InjectRepository(Certificate)
    private certificatesRepository: Repository<Certificate>,
    @InjectQueue('nfe:emit') private nfeEmitQueue: Queue,
    @InjectQueue('nfe:danfe') private nfeDanfeQueue: Queue,
    @InjectQueue('nfe:transmit') private nfeTransmitQueue: Queue,
    @InjectQueue('nfe:cancel') private nfeCancelQueue: Queue,
    private dataSource: DataSource,
  ) {}

  async create(
    accountId: string,
    createNfeDto: CreateNfeDto,
  ): Promise<NFeDocument> {
    // Check if emitter exists and belongs to the account
    const emitter = await this.emittersRepository.findOne({
      where: { id: createNfeDto.emitterId, accountId },
    });

    if (!emitter) {
      throw new NotFoundException(
        `Emitter with ID ${createNfeDto.emitterId} not found`,
      );
    }

    // Check if emitter has a valid certificate
    if (emitter.certificateStatus !== CertificateStatus.VALID) {
      throw new BadRequestException(
        'Emitter does not have a valid certificate',
      );
    }

    // Start a transaction to create the NFe document and update the emitter's current number
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the NFe document, persisting the raw payload
      const nfeDocument = this.nfeDocumentsRepository.create({
        emitterId: emitter.id,
        documentNumber: emitter.currentNumber,
        // Use emitter.series if set, otherwise default to 1
        series: emitter.series ? parseInt(emitter.series, 10) : 1,
        status: NFeStatus.CREATED,
        payload: createNfeDto.payload,
      });

      await queryRunner.manager.save(nfeDocument);

      // Increment the emitter's current number
      emitter.currentNumber += 1;
      await queryRunner.manager.save(emitter);

      await queryRunner.commitTransaction();

      // Add the document to the emission queue (skip in tests)
      if (process.env.NODE_ENV !== 'test') {
        await this.nfeEmitQueue.add('emit', {
          nfeId: nfeDocument.id,
          accountId,
          emitterId: emitter.id,
          payload: createNfeDto.payload,
        });
      }

      return nfeDocument;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(
    accountId: string, 
    emitterId?: string,
    paginationDto?: PaginationDto
  ): Promise<PaginationResult<NFeDocument>> {
    const { page = 1, limit = 10 } = paginationDto || {};
    
    // Build the query
    const queryBuilder = this.nfeDocumentsRepository.createQueryBuilder('nfe');
    queryBuilder.leftJoinAndSelect('nfe.emitter', 'emitter');
    queryBuilder.leftJoinAndSelect('nfe.storage', 'storage');
    
    // Filter by emitter if provided
    if (emitterId) {
      // Check if emitter exists and belongs to the account
      const emitter = await this.emittersRepository.findOne({
        where: { id: emitterId, accountId },
      });

      if (!emitter) {
        throw new NotFoundException(`Emitter with ID ${emitterId} not found`);
      }

      queryBuilder.andWhere('nfe.emitterId = :emitterId', { emitterId });
    } else {
      // Get all emitters for the account
      const emitters = await this.emittersRepository.find({
        where: { accountId },
        select: ['id'],
      });

      if (emitters.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      }

      queryBuilder.andWhere('nfe.emitterId IN (:...emitterIds)', {
        emitterIds: emitters.map((e) => e.id),
      });
    }
    
    // Add sorting
    queryBuilder.orderBy('nfe.generatedAt', 'DESC');
    
    // Count total items
    const totalItems = await queryBuilder.getCount();
    
    // Add pagination
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);
    
    // Execute query
    const items = await queryBuilder.getMany();
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(totalItems / limit);
    
    return {
      items,
      meta: {
        totalItems,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string, accountId: string): Promise<NFeDocument> {
    const nfeDocument = await this.nfeDocumentsRepository.findOne({
      where: { id },
      relations: ['emitter', 'storage'],
    });

    if (!nfeDocument) {
      throw new NotFoundException(`NFe document with ID ${id} not found`);
    }

    // Check if the emitter belongs to the account
    const emitter = await this.emittersRepository.findOne({
      where: { id: nfeDocument.emitterId, accountId },
    });

    if (!emitter) {
      throw new NotFoundException(`NFe document with ID ${id} not found`);
    }

    return nfeDocument;
  }

  async cancel(
    id: string,
    accountId: string,
    cancelNfeDto: CancelNfeDto,
  ): Promise<NFeDocument> {
    const nfeDocument = await this.findOne(id, accountId);

    // Check if the document can be cancelled
    if (nfeDocument.status !== NFeStatus.AUTHORIZED) {
      throw new BadRequestException(
        `Cannot cancel document with status ${nfeDocument.status}`,
      );
    }

    // Add the document to the cancellation queue (skip in tests)
    if (process.env.NODE_ENV !== 'test') {
      await this.nfeCancelQueue.add('cancel', {
        nfeId: nfeDocument.id,
        justification: cancelNfeDto.justification,
      });
    } else {
      // For tests, just update directly
      nfeDocument.status = NFeStatus.CANCELLED;
      nfeDocument.cancelledAt = new Date();
      nfeDocument.cancellationReason = cancelNfeDto.justification;
      await this.nfeDocumentsRepository.save(nfeDocument);
    }

    return nfeDocument;
  }

  async emitDocument(nfeId: string): Promise<void> {
    // This method is called by the queue processor to build and sign the XML,
    // then hand off transmission to the PHP Emissor via a queue.
    const nfeDocument = await this.nfeDocumentsRepository.findOne({
      where: { id: nfeId },
    });
    if (!nfeDocument) {
      throw new NotFoundException(`NFe document with ID ${nfeId} not found`);
    }
    // 1. Generate and sign XML (placeholder implementation)
    const xml = `<nfe><infNFe id="${nfeId}">...signed...</infNFe></nfe>`;
    nfeDocument.xmlPayload = xml;
    nfeDocument.status = NFeStatus.SIGNED;
    await this.nfeDocumentsRepository.save(nfeDocument);
    // 2. Hand off to PHP Emissor via Bull queue
    await this.nfeTransmitQueue.add('transmit', { nfeId });
  }
}
