import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NfeService } from './nfe.service';
import { NFeDocument, NFeStatus } from './entities/nfe-document.entity';
import { Emitter } from '../emitters/entities/emitter.entity';
import { Certificate } from '../certificates/entities/certificate.entity';
import { PaginationDto } from '../common/dto/pagination.dto';

const mockNfeDocumentsRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getCount: jest.fn(),
    getMany: jest.fn(),
  })),
});

const mockEmittersRepository = () => ({
  findOne: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
});

const mockCertificatesRepository = () => ({
  findOne: jest.fn(),
});

const mockQueue = {
  add: jest.fn(),
};

const mockDataSource = {
  createQueryRunner: jest.fn(() => ({
    connect: jest.fn(),
    startTransaction: jest.fn(),
    manager: {
      save: jest.fn(),
    },
    commitTransaction: jest.fn(),
    rollbackTransaction: jest.fn(),
    release: jest.fn(),
  })),
};

describe('NfeService', () => {
  let service: NfeService;
  let nfeDocumentsRepository: any;
  let emittersRepository: any;
  let queryBuilder: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NfeService,
        {
          provide: getRepositoryToken(NFeDocument),
          useFactory: mockNfeDocumentsRepository,
        },
        {
          provide: getRepositoryToken(Emitter),
          useFactory: mockEmittersRepository,
        },
        {
          provide: getRepositoryToken(Certificate),
          useFactory: mockCertificatesRepository,
        },
        {
          provide: 'BullQueue_nfe:emit',
          useValue: mockQueue,
        },
        {
          provide: 'BullQueue_nfe:danfe',
          useValue: mockQueue,
        },
        {
          provide: 'BullQueue_nfe:transmit',
          useValue: mockQueue,
        },
        {
          provide: 'DataSource',
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<NfeService>(NfeService);
    nfeDocumentsRepository = module.get(getRepositoryToken(NFeDocument));
    emittersRepository = module.get(getRepositoryToken(Emitter));
    queryBuilder = nfeDocumentsRepository.createQueryBuilder();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll with pagination', () => {
    it('should return paginated results', async () => {
      const accountId = 'test-account-id';
      const paginationDto: PaginationDto = { page: 2, limit: 10 };
      
      // Mock emitters
      const mockEmitters = [
        { id: 'emitter-1' },
        { id: 'emitter-2' },
      ];
      
      // Mock NFe documents
      const mockNfeDocuments = [
        { id: 'nfe-1', emitterId: 'emitter-1', status: NFeStatus.AUTHORIZED },
        { id: 'nfe-2', emitterId: 'emitter-2', status: NFeStatus.CREATED },
      ];
      
      // Set up mocks
      emittersRepository.find.mockResolvedValue(mockEmitters);
      queryBuilder.getCount.mockResolvedValue(25); // Total items
      queryBuilder.getMany.mockResolvedValue(mockNfeDocuments);
      
      const result = await service.findAll(accountId, undefined, paginationDto);
      
      // Verify the result structure
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('meta');
      expect(result.items).toEqual(mockNfeDocuments);
      
      // Verify pagination metadata
      expect(result.meta).toEqual({
        totalItems: 25,
        itemsPerPage: 10,
        totalPages: 3,
        currentPage: 2,
        hasNextPage: true,
        hasPreviousPage: true,
      });
      
      // Verify query builder was called with correct parameters
      expect(queryBuilder.skip).toHaveBeenCalledWith(10); // (page-1) * limit
      expect(queryBuilder.take).toHaveBeenCalledWith(10);
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('nfe.emitterId IN (:...emitterIds)', {
        emitterIds: mockEmitters.map(e => e.id),
      });
    });
    
    it('should filter by emitterId if provided', async () => {
      const accountId = 'test-account-id';
      const emitterId = 'emitter-1';
      const paginationDto: PaginationDto = { page: 1, limit: 10 };
      
      // Mock emitter
      const mockEmitter = { id: emitterId, accountId };
      
      // Mock NFe documents
      const mockNfeDocuments = [
        { id: 'nfe-1', emitterId, status: NFeStatus.AUTHORIZED },
        { id: 'nfe-2', emitterId, status: NFeStatus.CREATED },
      ];
      
      // Set up mocks
      emittersRepository.findOne.mockResolvedValue(mockEmitter);
      queryBuilder.getCount.mockResolvedValue(2); // Total items
      queryBuilder.getMany.mockResolvedValue(mockNfeDocuments);
      
      const result = await service.findAll(accountId, emitterId, paginationDto);
      
      // Verify the result structure
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('meta');
      expect(result.items).toEqual(mockNfeDocuments);
      
      // Verify pagination metadata
      expect(result.meta).toEqual({
        totalItems: 2,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });
      
      // Verify query builder was called with correct parameters
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('nfe.emitterId = :emitterId', { emitterId });
    });
    
    it('should return empty result if no emitters found for account', async () => {
      const accountId = 'test-account-id';
      const paginationDto: PaginationDto = { page: 1, limit: 10 };
      
      // Mock empty emitters array
      emittersRepository.find.mockResolvedValue([]);
      
      const result = await service.findAll(accountId, undefined, paginationDto);
      
      // Verify the result structure
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('meta');
      expect(result.items).toEqual([]);
      
      // Verify pagination metadata
      expect(result.meta).toEqual({
        totalItems: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });
      
      // Verify query builder was not called
      expect(queryBuilder.getMany).not.toHaveBeenCalled();
    });
  });
});
