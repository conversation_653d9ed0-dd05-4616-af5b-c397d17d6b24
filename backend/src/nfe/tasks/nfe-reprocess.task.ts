import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bullmq';
import { NFeDocument, NFeStatus } from '../entities/nfe-document.entity';

@Injectable()
export class NfeReprocessTask {
  private readonly logger = new Logger(NfeReprocessTask.name);

  constructor(
    @InjectRepository(NFeDocument)
    private nfeDocumentsRepository: Repository<NFeDocument>,
    @InjectQueue('nfe:emit') private nfeEmitQueue: Queue,
  ) {}

  @Cron(CronExpression.EVERY_30_MINUTES)
  async handleFailedEmissions() {
    this.logger.debug('Running failed NFe emission reprocessing');

    // Find all documents in error state
    const failedDocuments = await this.nfeDocumentsRepository.find({
      where: { status: NFeStatus.ERROR },
      relations: ['emitter'],
    });

    this.logger.debug(
      `Found ${failedDocuments.length} failed documents to reprocess`,
    );

    for (const document of failedDocuments) {
      this.logger.debug(`Reprocessing document ${document.id}`);

      // Add the document back to the emission queue
      await this.nfeEmitQueue.add('emit', {
        nfeId: document.id,
        accountId: document.emitter.accountId,
        emitterId: document.emitterId,
      });
    }

    this.logger.debug('Failed NFe emission reprocessing completed');
  }
}
