import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PaginationDto } from '../common/dto/pagination.dto';
import { CancelNfeDto } from './dto/cancel-nfe.dto';
import { CreateNfeDto } from './dto/create-nfe.dto';
import { NfeService } from './nfe.service';

@ApiTags('NFe Documents')
@ApiBearerAuth()
@Controller('nfe')
@UseGuards(JwtAuthGuard)
export class NfeController {
  constructor(private readonly nfeService: NfeService) {}

  @ApiOperation({ summary: 'Create a new NFe document' })
  @ApiResponse({
    status: 201,
    description: 'The NFe document has been successfully created',
  })
  @Post()
  create(@Body() createNfeDto: CreateNfeDto, @CurrentUser() user) {
    return this.nfeService.create(user.accountId, createNfeDto);
  }

  @ApiOperation({ summary: 'Get all NFe documents with pagination' })
  @ApiQuery({
    name: 'emitterId',
    required: false,
    description: 'Filter by emitter ID',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (1-based)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'List of NFe documents with pagination metadata',
  })
  @Get()
  findAll(
    @Query('emitterId') emitterId: string,
    @Query() paginationDto: PaginationDto,
    @CurrentUser() user,
  ) {
    return this.nfeService.findAll(user.accountId, emitterId, paginationDto);
  }

  @ApiOperation({ summary: 'Get a specific NFe document by ID' })
  @ApiParam({ name: 'id', description: 'NFe document ID' })
  @ApiResponse({
    status: 200,
    description: 'The NFe document',
  })
  @ApiResponse({
    status: 404,
    description: 'NFe document not found',
  })
  @Get(':id')
  findOne(@Param('id') id: string, @CurrentUser() user) {
    return this.nfeService.findOne(id, user.accountId);
  }

  @ApiOperation({ summary: 'Cancel an NFe document' })
  @ApiParam({ name: 'id', description: 'NFe document ID' })
  @ApiResponse({
    status: 200,
    description: 'The NFe document has been successfully cancelled',
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot cancel document with current status',
  })
  @ApiResponse({
    status: 404,
    description: 'NFe document not found',
  })
  @Post(':id/cancel')
  cancel(
    @Param('id') id: string,
    @Body() cancelNfeDto: CancelNfeDto,
    @CurrentUser() user,
  ) {
    return this.nfeService.cancel(id, user.accountId, cancelNfeDto);
  }
}
