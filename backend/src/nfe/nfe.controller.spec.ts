import { Test, TestingModule } from '@nestjs/testing';
import { NfeController } from './nfe.controller';
import { NfeService } from './nfe.service';
import { CreateNfeDto } from './dto/create-nfe.dto';
import { CancelNfeDto } from './dto/cancel-nfe.dto';
import { PaginationDto } from '../common/dto/pagination.dto';
import { NFeStatus } from './entities/nfe-document.entity';

const mockNfeService = () => ({
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  cancel: jest.fn(),
});

describe('NfeController', () => {
  let controller: NfeController;
  let service: NfeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NfeController],
      providers: [
        {
          provide: NfeService,
          useFactory: mockNfeService,
        },
      ],
    }).compile();

    controller = module.get<NfeController>(NfeController);
    service = module.get<NfeService>(NfeService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should call service.findAll with correct parameters', async () => {
      const user = { accountId: 'test-account-id' };
      const emitterId = 'test-emitter-id';
      const paginationDto: PaginationDto = { page: 2, limit: 15 };
      
      const mockResult = {
        items: [
          { id: 'nfe-1', status: NFeStatus.AUTHORIZED },
          { id: 'nfe-2', status: NFeStatus.CREATED },
        ],
        meta: {
          totalItems: 25,
          itemsPerPage: 15,
          totalPages: 2,
          currentPage: 2,
          hasNextPage: false,
          hasPreviousPage: true,
        },
      };
      
      jest.spyOn(service, 'findAll').mockResolvedValue(mockResult);
      
      const result = await controller.findAll(emitterId, paginationDto, user);
      
      expect(service.findAll).toHaveBeenCalledWith(
        user.accountId,
        emitterId,
        paginationDto,
      );
      expect(result).toEqual(mockResult);
    });
    
    it('should use default pagination values if not provided', async () => {
      const user = { accountId: 'test-account-id' };
      const emitterId = 'test-emitter-id';
      const paginationDto = {};
      
      const mockResult = {
        items: [
          { id: 'nfe-1', status: NFeStatus.AUTHORIZED },
          { id: 'nfe-2', status: NFeStatus.CREATED },
        ],
        meta: {
          totalItems: 25,
          itemsPerPage: 10,
          totalPages: 3,
          currentPage: 1,
          hasNextPage: true,
          hasPreviousPage: false,
        },
      };
      
      jest.spyOn(service, 'findAll').mockResolvedValue(mockResult);
      
      const result = await controller.findAll(emitterId, paginationDto as PaginationDto, user);
      
      expect(service.findAll).toHaveBeenCalledWith(
        user.accountId,
        emitterId,
        paginationDto,
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('create', () => {
    it('should call service.create with correct parameters', async () => {
      const user = { accountId: 'test-account-id' };
      const createNfeDto: CreateNfeDto = {
        emitterId: 'test-emitter-id',
        payload: {
          customer: {
            name: 'Test Customer',
            federalTaxId: '***********',
            email: '<EMAIL>',
            address: {
              street: 'Test Street',
              number: '123',
              neighborhood: 'Test Neighborhood',
              city: 'Test City',
              state: 'TS',
              zipCode: '********',
            },
          },
          products: [
            {
              description: 'Test Product',
              quantity: 1,
              price: 100,
              taxCode: '123',
            },
          ],
          payment: {
            method: 'cash',
            amount: 100,
          },
        },
      };
      
      const mockResult = { id: 'nfe-1', status: NFeStatus.CREATED };
      
      jest.spyOn(service, 'create').mockResolvedValue(mockResult as any);
      
      const result = await controller.create(createNfeDto, user);
      
      expect(service.create).toHaveBeenCalledWith(
        user.accountId,
        createNfeDto,
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('findOne', () => {
    it('should call service.findOne with correct parameters', async () => {
      const user = { accountId: 'test-account-id' };
      const id = 'test-nfe-id';
      
      const mockResult = { id, status: NFeStatus.AUTHORIZED };
      
      jest.spyOn(service, 'findOne').mockResolvedValue(mockResult as any);
      
      const result = await controller.findOne(id, user);
      
      expect(service.findOne).toHaveBeenCalledWith(id, user.accountId);
      expect(result).toEqual(mockResult);
    });
  });

  describe('cancel', () => {
    it('should call service.cancel with correct parameters', async () => {
      const user = { accountId: 'test-account-id' };
      const id = 'test-nfe-id';
      const cancelNfeDto: CancelNfeDto = { reason: 'Test reason' };
      
      const mockResult = {
        id,
        status: NFeStatus.CANCELLED,
        cancelledAt: new Date(),
        cancellationReason: 'Test reason',
      };
      
      jest.spyOn(service, 'cancel').mockResolvedValue(mockResult as any);
      
      const result = await controller.cancel(id, cancelNfeDto, user);
      
      expect(service.cancel).toHaveBeenCalledWith(
        id,
        user.accountId,
        cancelNfeDto,
      );
      expect(result).toEqual(mockResult);
    });
  });
});
