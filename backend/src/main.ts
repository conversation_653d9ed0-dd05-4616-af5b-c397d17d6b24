import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import helmet from 'helmet';
import compression from 'compression';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    logger.log('🚀 Starting EZ NFe SaaS application...');

    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Get configuration service
    const configService = app.get(ConfigService);

    // Security middleware
    app.use(
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
          },
        },
        crossOriginEmbedderPolicy: false,
      }),
    );

    // Compression middleware
    app.use(compression());

    // CORS configuration
    if (configService.get('app.cors.enabled')) {
      app.enableCors({
        origin: configService.get('app.cors.origin'),
        methods: configService.get('app.cors.methods'),
        allowedHeaders: configService.get('app.cors.allowedHeaders'),
        credentials: configService.get('app.cors.credentials'),
      });
    } else {
      // Fallback CORS for development
      app.enableCors();
    }

    // Global API prefix
    const globalPrefix = configService.get('app.globalPrefix');
    if (globalPrefix) {
      app.setGlobalPrefix(globalPrefix);
    }

    // Additional global validation pipe (enhancing the one from AppModule)
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
        validateCustomDecorators: true,
        errorHttpStatusCode: 422,
      }),
    );

    // Swagger documentation
    if (configService.get('app.features.swaggerEnabled')) {
      const config = new DocumentBuilder()
        .setTitle('EZ NFe SaaS API')
        .setDescription(
          'API documentation for the EZ NFe SaaS platform - Brazilian fiscal compliance made easy',
        )
        .setVersion(configService.get('app.version') || '1.0.0')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth',
        )
        .addApiKey(
          {
            type: 'apiKey',
            name: 'X-API-Key',
            in: 'header',
            description: 'API Key for programmatic access',
          },
          'API-Key',
        )
        .addTag('Authentication', 'User authentication and authorization')
        .addTag('Users', 'User management')
        .addTag('Accounts', 'Multi-tenant account management')
        .addTag('Business Units', 'Company/CNPJ management within accounts')
        .addTag('Clients', 'Customer and supplier management')
        .addTag('Products', 'Product catalog with Brazilian tax configurations')
        .addTag('Carriers', 'Shipping and transport companies')
        .addTag('Payment Terms', 'Payment condition templates')
        .addTag('Invoices', 'Brazilian fiscal documents (NF-e, NFC-e, NFS-e)')
        .addTag('Tax Calculation', 'Brazilian tax calculation engine')
        .addTag('Certificates', 'Digital certificate management')
        .addTag('API Keys', 'API access key management')
        .addTag('Subscriptions', 'Account subscription and billing')
        .addTag('Brazilian Validation', 'CNPJ and CEP validation services')
        .addTag('Audit', 'System audit trail')
        .addTag('Notifications', 'System communications')
        .addTag('Health', 'Health check endpoints')
        .addServer(
          configService.get('app.environment') === 'production'
            ? 'https://api.eznfe.com'
            : 'http://localhost:3000',
        )
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          docExpansion: 'none',
          filter: true,
          showRequestHeaders: true,
          tryItOutEnabled: true,
        },
        customSiteTitle: 'EZ NFe SaaS API Documentation',
        customCss: '.swagger-ui .topbar { display: none }',
      });

      logger.log('📚 Swagger documentation enabled at /api/docs');
    }

    // Start server
    const port = configService.get('app.port') || process.env.PORT || 3000;
    const host = configService.get('app.host') || '0.0.0.0';

    await app.listen(port, host);

    logger.log(`🚀 Application running on http://${host}:${port}`);
    logger.log(`🌍 Environment: ${configService.get('app.environment')}`);
    logger.log(`📖 API Documentation: http://${host}:${port}/api/docs`);
    logger.log(`💪 Ready to serve Brazilian fiscal compliance!`);

    // Graceful shutdown handlers
    process.on('SIGTERM', async () => {
      logger.log('SIGTERM received, shutting down gracefully');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('SIGINT received, shutting down gracefully');
      await app.close();
      process.exit(0);
    });
  } catch (error) {
    logger.error('Failed to start application', error);
    process.exit(1);
  }
}

bootstrap().catch((err) => {
  console.error('Unhandled error during bootstrap:', err);
  process.exit(1);
});
