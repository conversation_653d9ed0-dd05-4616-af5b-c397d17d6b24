/**
 * Brazilian document validators
 * Validates CPF, CNPJ, CEP and other Brazilian-specific formats
 */

/**
 * Validate CPF (Cadastro de Pessoas Físicas)
 * @param cpf CPF number as string (with or without dots/dashes)
 * @returns boolean indicating if CPF is valid
 */
export function validateCpf(cpf: string): boolean {
  if (!cpf || typeof cpf !== 'string') return false;

  // Remove all non-digit characters
  const cleanCpf = cpf.replace(/\D/g, '');

  // Check if has 11 digits
  if (cleanCpf.length !== 11) return false;

  // Check for known invalid patterns (all same digits)
  if (/^(\d)\1{10}$/.test(cleanCpf)) return false;

  // Validate check digits
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
  }
  let digit1 = 11 - (sum % 11);
  if (digit1 === 10 || digit1 === 11) digit1 = 0;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
  }
  let digit2 = 11 - (sum % 11);
  if (digit2 === 10 || digit2 === 11) digit2 = 0;

  return (
    digit1 === parseInt(cleanCpf.charAt(9)) &&
    digit2 === parseInt(cleanCpf.charAt(10))
  );
}

/**
 * Validate CNPJ (Cadastro Nacional da Pessoa Jurídica)
 * @param cnpj CNPJ number as string (with or without dots/dashes/slash)
 * @returns boolean indicating if CNPJ is valid
 */
export function validateCnpj(cnpj: string): boolean {
  if (!cnpj || typeof cnpj !== 'string') return false;

  // Remove all non-digit characters
  const cleanCnpj = cnpj.replace(/\D/g, '');

  // Check if has 14 digits
  if (cleanCnpj.length !== 14) return false;

  // Check for known invalid patterns (all same digits)
  if (/^(\d)\1{13}$/.test(cleanCnpj)) return false;

  // Validate first check digit
  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cleanCnpj.charAt(i)) * weights1[i];
  }
  let digit1 = sum % 11;
  digit1 = digit1 < 2 ? 0 : 11 - digit1;

  // Validate second check digit
  const weights2 = [6, 7, 8, 9, 2, 3, 4, 5, 6, 7, 8, 9];
  sum = 0;
  for (let i = 0; i < 13; i++) {
    const weight = i < 12 ? weights2[i] : 2;
    sum += parseInt(cleanCnpj.charAt(i)) * weight;
  }
  let digit2 = sum % 11;
  digit2 = digit2 < 2 ? 0 : 11 - digit2;

  return (
    digit1 === parseInt(cleanCnpj.charAt(12)) &&
    digit2 === parseInt(cleanCnpj.charAt(13))
  );
}

/**
 * Validate CEP (Código de Endereçamento Postal)
 * @param cep CEP as string (with or without dash)
 * @returns boolean indicating if CEP format is valid
 */
export function validateCep(cep: string): boolean {
  if (!cep || typeof cep !== 'string') return false;

  // Remove all non-digit characters
  const cleanCep = cep.replace(/\D/g, '');

  // Check if has 8 digits and is not all zeros
  return cleanCep.length === 8 && cleanCep !== '00000000';
}

/**
 * Format CPF with standard dots and dash
 * @param cpf CPF as string
 * @returns formatted CPF (XXX.XXX.XXX-XX) or original string if invalid
 */
export function formatCpf(cpf: string): string {
  if (!validateCpf(cpf)) return cpf;
  
  const cleanCpf = cpf.replace(/\D/g, '');
  return cleanCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Format CNPJ with standard dots, slash and dash
 * @param cnpj CNPJ as string
 * @returns formatted CNPJ (XX.XXX.XXX/XXXX-XX) or original string if invalid
 */
export function formatCnpj(cnpj: string): string {
  if (!validateCnpj(cnpj)) return cnpj;
  
  const cleanCnpj = cnpj.replace(/\D/g, '');
  return cleanCnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
}

/**
 * Format CEP with standard dash
 * @param cep CEP as string
 * @returns formatted CEP (XXXXX-XXX) or original string if invalid
 */
export function formatCep(cep: string): string {
  if (!validateCep(cep)) return cep;
  
  const cleanCep = cep.replace(/\D/g, '');
  return cleanCep.replace(/(\d{5})(\d{3})/, '$1-$2');
}

/**
 * Remove formatting from Brazilian documents
 * @param document Document string with potential formatting
 * @returns clean string with only digits
 */
export function cleanBrazilianDocument(document: string): string {
  if (!document || typeof document !== 'string') return '';
  return document.replace(/\D/g, '');
}

/**
 * Validate Brazilian state code
 * @param state State code (2 letters)
 * @returns boolean indicating if state code is valid
 */
export function validateBrazilianState(state: string): boolean {
  if (!state || typeof state !== 'string') return false;
  
  const validStates = [
    'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 
    'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 
    'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
  ];
  
  return validStates.includes(state.toUpperCase());
}

/**
 * Get Brazilian state name from code
 * @param stateCode State code (2 letters)
 * @returns full state name or empty string if invalid
 */
export function getBrazilianStateName(stateCode: string): string {
  if (!stateCode || typeof stateCode !== 'string') return '';
  
  const stateNames: Record<string, string> = {
    'AC': 'Acre',
    'AL': 'Alagoas', 
    'AP': 'Amapá',
    'AM': 'Amazonas',
    'BA': 'Bahia',
    'CE': 'Ceará',
    'DF': 'Distrito Federal',
    'ES': 'Espírito Santo',
    'GO': 'Goiás',
    'MA': 'Maranhão',
    'MT': 'Mato Grosso',
    'MS': 'Mato Grosso do Sul',
    'MG': 'Minas Gerais',
    'PA': 'Pará',
    'PB': 'Paraíba',
    'PR': 'Paraná',
    'PE': 'Pernambuco',
    'PI': 'Piauí',
    'RJ': 'Rio de Janeiro',
    'RN': 'Rio Grande do Norte',
    'RS': 'Rio Grande do Sul',
    'RO': 'Rondônia',
    'RR': 'Roraima',
    'SC': 'Santa Catarina',
    'SP': 'São Paulo',
    'SE': 'Sergipe',
    'TO': 'Tocantins'
  };
  
  return stateNames[stateCode.toUpperCase()] || '';
}

/**
 * Validate Brazilian phone number
 * @param phone Phone number string
 * @returns boolean indicating if phone format is valid
 */
export function validateBrazilianPhone(phone: string): boolean {
  if (!phone || typeof phone !== 'string') return false;
  
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Valid formats: 10 digits (landline) or 11 digits (mobile with 9)
  return cleanPhone.length === 10 || cleanPhone.length === 11;
}

/**
 * Format Brazilian phone number
 * @param phone Phone number string
 * @returns formatted phone number or original if invalid
 */
export function formatBrazilianPhone(phone: string): string {
  if (!validateBrazilianPhone(phone)) return phone;
  
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length === 10) {
    // Landline: (XX) XXXX-XXXX
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  } else if (cleanPhone.length === 11) {
    // Mobile: (XX) 9XXXX-XXXX
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
}
