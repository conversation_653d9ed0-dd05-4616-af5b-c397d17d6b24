import { Injectable, BadRequestException, UnauthorizedException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { User } from '../../database/entities/user.entity';
import { BusinessUnit } from '../../database/entities/business-unit.entity';
import { TenantContext } from '../interfaces/tenant-context.interface';
import { BusinessUnitStatus } from '../../common/enums/business-unit-status.enum';
import { UserRole } from '../../common/enums/user-role.enum';

export interface BusinessUnitSwitchDto {
  businessUnitId: string;
}

export interface BusinessUnitSwitchResponse {
  success: boolean;
  businessUnit: {
    id: string;
    name: string;
    cnpj: string;
    status: BusinessUnitStatus;
  };
  permissions: string[];
  message: string;
}

@Injectable()
export class BusinessUnitSwitchService {
  private readonly logger = new Logger(BusinessUnitSwitchService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(BusinessUnit)
    private readonly businessUnitRepository: Repository<BusinessUnit>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Switch user to a different business unit within their account
   */
  async switchBusinessUnit(
    userId: string,
    currentContext: TenantContext,
    switchDto: BusinessUnitSwitchDto,
  ): Promise<BusinessUnitSwitchResponse> {
    this.logger.log(`User ${userId} attempting to switch to business unit ${switchDto.businessUnitId}`);

    try {
      // Validate the target business unit
      const targetBusinessUnit = await this.validateBusinessUnitAccess(
        userId,
        currentContext.account.id,
        switchDto.businessUnitId,
      );

      // Get user permissions for the target business unit
      const permissions = await this.getUserPermissions(userId, targetBusinessUnit.id);

      // Emit business unit switch event for audit logging
      this.eventEmitter.emit('business-unit.switched', {
        userId,
        fromBusinessUnitId: currentContext.businessUnit?.id,
        toBusinessUnitId: targetBusinessUnit.id,
        accountId: currentContext.account.id,
        timestamp: new Date(),
        // ipAddress and userAgent are not part of TenantContext - would need to be passed separately
      });

      this.logger.log(`User ${userId} successfully switched to business unit ${targetBusinessUnit.id}`);

      return {
        success: true,
        businessUnit: {
          id: targetBusinessUnit.id,
          name: targetBusinessUnit.name,
          cnpj: targetBusinessUnit.cnpj,
          status: targetBusinessUnit.status,
        },
        permissions,
        message: `Successfully switched to business unit: ${targetBusinessUnit.name}`,
      };
    } catch (error) {
      this.logger.error(`Failed to switch business unit for user ${userId}:`, error);
      
      // Emit failed switch event
      this.eventEmitter.emit('business-unit.switch-failed', {
        userId,
        targetBusinessUnitId: switchDto.businessUnitId,
        accountId: currentContext.account.id,
        error: error.message,
        timestamp: new Date(),
        // ipAddress not available in TenantContext
      });

      throw error;
    }
  }

  /**
   * Get all available business units for a user within their account
   */
  async getAvailableBusinessUnits(
    userId: string,
    accountId: string,
  ): Promise<BusinessUnit[]> {
    this.logger.log(`Getting available business units for user ${userId} in account ${accountId}`);

    // Get user with business unit associations
    const user = await this.userRepository.findOne({
      where: { id: userId, accountId },
      relations: ['businessUnits'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Filter only active business units
    const availableBusinessUnits = user.businessUnits.filter(
      (bu) => bu.status === BusinessUnitStatus.ACTIVE,
    );

    this.logger.log(`Found ${availableBusinessUnits.length} available business units for user ${userId}`);

    return availableBusinessUnits;
  }

  /**
   * Validate if user has access to the target business unit
   */
  private async validateBusinessUnitAccess(
    userId: string,
    accountId: string,
    businessUnitId: string,
  ): Promise<BusinessUnit> {
    // Get user with business unit associations
    const user = await this.userRepository.findOne({
      where: { id: userId, accountId },
      relations: ['businessUnits'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Check if user has access to the target business unit
    const targetBusinessUnit = user.businessUnits.find(
      (bu) => bu.id === businessUnitId && bu.status === BusinessUnitStatus.ACTIVE,
    );

    if (!targetBusinessUnit) {
      throw new BadRequestException(
        'Business unit not found or user does not have access to it',
      );
    }

    // Verify business unit belongs to the same account
    if (targetBusinessUnit.accountId !== accountId) {
      throw new BadRequestException('Business unit does not belong to the current account');
    }

    return targetBusinessUnit;
  }

  /**
   * Get user permissions for a specific business unit
   */
  private async getUserPermissions(userId: string, businessUnitId: string): Promise<string[]> {
    // Get user with role information
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'role'],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Define permissions based on user role
    const rolePermissions = this.getRolePermissions(user.role);

    this.logger.log(`User ${userId} has ${rolePermissions.length} permissions in business unit ${businessUnitId}`);

    return rolePermissions;
  }

  /**
   * Get permissions based on user role
   */
  private getRolePermissions(role: UserRole): string[] {
    const permissionsMap: Record<UserRole, string[]> = {
      [UserRole.SUPER_ADMIN]: [
        'invoice.create',
        'invoice.read',
        'invoice.update',
        'invoice.delete',
        'invoice.approve',
        'invoice.cancel',
        'client.create',
        'client.read',
        'client.update',
        'client.delete',
        'product.create',
        'product.read',
        'product.update',
        'product.delete',
        'carrier.create',
        'carrier.read',
        'carrier.update',
        'carrier.delete',
        'business-unit.manage',
        'user.manage',
        'certificate.manage',
        'tax.configure',
        'report.generate',
        'audit.read',
      ],
      [UserRole.OWNER]: [
        'invoice.create',
        'invoice.read',
        'invoice.update',
        'invoice.delete',
        'invoice.approve',
        'invoice.cancel',
        'client.create',
        'client.read',
        'client.update',
        'client.delete',
        'product.create',
        'product.read',
        'product.update',
        'product.delete',
        'carrier.create',
        'carrier.read',
        'carrier.update',
        'carrier.delete',
        'business-unit.manage',
        'user.manage',
        'certificate.manage',
        'tax.configure',
        'report.generate',
        'audit.read',
      ],
      [UserRole.ADMIN]: [
        'invoice.create',
        'invoice.read',
        'invoice.update',
        'invoice.approve',
        'client.create',
        'client.read',
        'client.update',
        'client.delete',
        'product.create',
        'product.read',
        'product.update',
        'product.delete',
        'carrier.create',
        'carrier.read',
        'carrier.update',
        'carrier.delete',
        'user.manage',
        'certificate.manage',
        'report.generate',
      ],
      [UserRole.MEMBER]: [
        'invoice.create',
        'invoice.read',
        'invoice.update',
        'client.read',
        'client.update',
        'product.read',
        'product.update',
        'carrier.read',
        'report.generate',
      ],
      [UserRole.OPERATOR]: [
        'invoice.create',
        'invoice.read',
        'invoice.update',
        'client.read',
        'client.update',
        'product.read',
        'product.update',
        'carrier.read',
        'report.generate',
      ],
      [UserRole.VIEWER]: [
        'invoice.read',
        'client.read',
        'product.read',
        'carrier.read',
        'report.generate',
      ],
    };

    return permissionsMap[role] || [];
  }

  /**
   * Check if user has specific permission in current context
   */
  async hasPermission(
    userId: string,
    businessUnitId: string,
    permission: string,
  ): Promise<boolean> {
    const permissions = await this.getUserPermissions(userId, businessUnitId);
    return permissions.includes(permission);
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(
    userId: string,
    businessUnitId: string,
    permissions: string[],
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, businessUnitId);
    return permissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * Check if user has all of the specified permissions
   */
  async hasAllPermissions(
    userId: string,
    businessUnitId: string,
    permissions: string[],
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId, businessUnitId);
    return permissions.every(permission => userPermissions.includes(permission));
  }
}
