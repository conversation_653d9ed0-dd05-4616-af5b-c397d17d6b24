import { Injectable, Logger, Inject } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Account } from '../../database/entities/account.entity';
import { BusinessUnit } from '../../database/entities/business-unit.entity';
import { User } from '../../database/entities/user.entity';
import { TenantContext } from '../interfaces/tenant-context.interface';
import { AccountStatus } from '../enums/account-status.enum';
import { BusinessUnitStatus } from '../enums/business-unit-status.enum';

export interface TenantInfo {
  account: Account;
  businessUnit?: BusinessUnit;
  user?: User;
}

export interface CachedTenantData {
  accountId: string;
  accountName: string;
  accountStatus: AccountStatus;
  businessUnitId?: string;
  businessUnitName?: string;
  businessUnitStatus?: BusinessUnitStatus;
  userId?: string;
  userName?: string;
  userRole?: string;
  cachedAt: Date;
}

@Injectable()
export class TenantContextService {
  private readonly logger = new Logger(TenantContextService.name);
  private static readonly CACHE_TTL = 300; // 5 minutes
  private static readonly CACHE_PREFIX = 'tenant-context';

  constructor(
    @Inject(REQUEST) private readonly request: Request,
    @InjectRepository(Account)
    private readonly accountRepository: Repository<Account>,
    @InjectRepository(BusinessUnit)
    private readonly businessUnitRepository: Repository<BusinessUnit>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
  ) {}

  /**
   * Get the current tenant context from the request
   */
  getCurrentContext(): TenantContext | null {
    const tenantContext = (this.request as any).tenantContext;
    
    if (!tenantContext) {
      this.logger.warn('No tenant context found in request');
      return null;
    }

    return tenantContext;
  }

  /**
   * Get the current account ID
   */
  getCurrentAccountId(): string | null {
    const context = this.getCurrentContext();
    return context?.account?.id || null;
  }

  /**
   * Get the current business unit ID
   */
  getCurrentBusinessUnitId(): string | null {
    const context = this.getCurrentContext();
    return context?.businessUnit?.id || null;
  }

  /**
   * Get the current user ID
   */
  getCurrentUserId(): string | null {
    // Note: User ID is not directly available in the new TenantContext structure
    // This method may need to be updated based on how user info is accessed
    return null;
  }

  /**
   * Get detailed tenant information with caching
   */
  async getTenantInfo(): Promise<TenantInfo | null> {
    const context = this.getCurrentContext();
    
    if (!context || !context.account?.id) {
      return null;
    }

    const cacheKey = `${TenantContextService.CACHE_PREFIX}:${context.account.id}:${context.businessUnit?.id || 'no-bu'}`;
    
    try {
      // Try to get from cache first
      const cached = await this.cacheManager.get<CachedTenantData>(cacheKey);
      
      if (cached && this.isCacheValid(cached)) {
        this.logger.debug(`Using cached tenant data for ${context.account.id}`);
        return this.buildTenantInfoFromCache(cached);
      }

      // Fetch fresh data
      const tenantInfo = await this.fetchTenantInfo(context);
      
      if (tenantInfo) {
        // Cache the result
        await this.cacheTenantInfo(cacheKey, tenantInfo);
      }

      return tenantInfo;
    } catch (error) {
      this.logger.error('Error getting tenant info:', error);
      return null;
    }
  }

  /**
   * Get account information
   */
  async getAccount(): Promise<Account | null> {
    const tenantInfo = await this.getTenantInfo();
    return tenantInfo?.account || null;
  }

  /**
   * Get business unit information
   */
  async getBusinessUnit(): Promise<BusinessUnit | null> {
    const tenantInfo = await this.getTenantInfo();
    return tenantInfo?.businessUnit || null;
  }

  /**
   * Get user information
   */
  async getUser(): Promise<User | null> {
    const tenantInfo = await this.getTenantInfo();
    return tenantInfo?.user || null;
  }

  /**
   * Check if current account is active
   */
  async isAccountActive(): Promise<boolean> {
    const account = await this.getAccount();
    return account?.status === AccountStatus.ACTIVE;
  }

  /**
   * Check if current business unit is active
   */
  async isBusinessUnitActive(): Promise<boolean> {
    const businessUnit = await this.getBusinessUnit();
    return businessUnit?.status === BusinessUnitStatus.ACTIVE;
  }

  /**
   * Check if tenant has access to specific resource
   */
  async hasResourceAccess(resourceAccountId: string, resourceBusinessUnitId?: string): Promise<boolean> {
    const context = this.getCurrentContext();
    
    if (!context) {
      return false;
    }

    // Check account access
    if (context.account?.id !== resourceAccountId) {
      return false;
    }

    // Check business unit access if specified
    if (resourceBusinessUnitId && context.businessUnit?.id !== resourceBusinessUnitId) {
      return false;
    }

    return true;
  }

  /**
   * Invalidate tenant cache
   */
  async invalidateCache(accountId?: string, businessUnitId?: string): Promise<void> {
    const context = this.getCurrentContext();
    const targetAccountId = accountId || context?.account?.id;
    const targetBusinessUnitId = businessUnitId || context?.businessUnit?.id;

    if (!targetAccountId) {
      return;
    }

    const cacheKey = `${TenantContextService.CACHE_PREFIX}:${targetAccountId}:${targetBusinessUnitId || 'no-bu'}`;
    
    try {
      await this.cacheManager.del(cacheKey);
      this.logger.log(`Invalidated tenant cache for ${targetAccountId}`);
    } catch (error) {
      this.logger.error('Error invalidating tenant cache:', error);
    }
  }

  /**
   * Refresh tenant information (bypasses cache)
   */
  async refreshTenantInfo(): Promise<TenantInfo | null> {
    const context = this.getCurrentContext();
    
    if (!context || !context.account?.id) {
      return null;
    }

    // Invalidate cache first
    await this.invalidateCache();

    // Fetch fresh data
    return this.fetchTenantInfo(context);
  }

  /**
   * Get tenant context for different business unit within same account
   */
  async getContextForBusinessUnit(businessUnitId: string): Promise<TenantContext | null> {
    const context = this.getCurrentContext();
    
    if (!context || !context.account?.id) {
      return null;
    }

    // Validate business unit belongs to the same account
    const businessUnit = await this.businessUnitRepository.findOne({
      where: {
        id: businessUnitId,
        accountId: context.account.id,
        status: BusinessUnitStatus.ACTIVE,
      },
    });

    if (!businessUnit) {
      return null;
    }

    return {
      ...context,
      businessUnit: {
        id: businessUnit.id,
        name: businessUnit.name,
        cnpj: businessUnit.cnpj,
        status: businessUnit.status,
        isDefault: false, // isDefault property doesn't exist in BusinessUnit
      },
    };
  }

  /**
   * Fetch tenant information from database
   */
  private async fetchTenantInfo(context: TenantContext): Promise<TenantInfo | null> {
    try {
      // Fetch account
      const account = await this.accountRepository.findOne({
        where: { id: context.account.id, status: AccountStatus.ACTIVE },
      });

      if (!account) {
        this.logger.warn(`Account ${context.account.id} not found or inactive`);
        return null;
      }

      const tenantInfo: TenantInfo = { account };

      // Fetch business unit if specified
      if (context.businessUnit?.id) {
        const businessUnit = await this.businessUnitRepository.findOne({
          where: {
            id: context.businessUnit.id,
            accountId: context.account.id,
            status: BusinessUnitStatus.ACTIVE,
          },
        });

        if (businessUnit) {
          tenantInfo.businessUnit = businessUnit;
        } else {
          this.logger.warn(`Business unit ${context.businessUnit.id} not found or inactive`);
        }
      }

      // Fetch user if specified - Note: userId is not available in TenantContext interface
      // This would need to be passed separately or retrieved from JWT payload

      return tenantInfo;
    } catch (error) {
      this.logger.error('Error fetching tenant info:', error);
      return null;
    }
  }

  /**
   * Cache tenant information
   */
  private async cacheTenantInfo(cacheKey: string, tenantInfo: TenantInfo): Promise<void> {
    try {
      const cachedData: CachedTenantData = {
        accountId: tenantInfo.account.id,
        accountName: tenantInfo.account.name,
        accountStatus: tenantInfo.account.status,
        businessUnitId: tenantInfo.businessUnit?.id,
        businessUnitName: tenantInfo.businessUnit?.name,
        businessUnitStatus: tenantInfo.businessUnit?.status,
        userId: tenantInfo.user?.id,
        userName: tenantInfo.user ? `${tenantInfo.user.firstName} ${tenantInfo.user.lastName}` : undefined,
        userRole: tenantInfo.user?.role,
        cachedAt: new Date(),
      };

      await this.cacheManager.set(cacheKey, cachedData, TenantContextService.CACHE_TTL * 1000);
      this.logger.debug(`Cached tenant data for ${tenantInfo.account.id}`);
    } catch (error) {
      this.logger.error('Error caching tenant info:', error);
    }
  }

  /**
   * Build tenant info from cached data
   */
  private buildTenantInfoFromCache(cached: CachedTenantData): TenantInfo {
    const account = new Account();
    account.id = cached.accountId;
    account.name = cached.accountName;
    account.status = cached.accountStatus;

    const tenantInfo: TenantInfo = { account };

    if (cached.businessUnitId) {
      const businessUnit = new BusinessUnit();
      businessUnit.id = cached.businessUnitId;
      businessUnit.name = cached.businessUnitName!;
      businessUnit.status = cached.businessUnitStatus!;
      businessUnit.accountId = cached.accountId;
      tenantInfo.businessUnit = businessUnit;
    }

    if (cached.userId) {
      const user = new User();
      user.id = cached.userId;
      user.firstName = cached.userName?.split(' ')[0] || '';
      user.lastName = cached.userName?.split(' ').slice(1).join(' ') || '';
      user.role = cached.userRole as any;
      user.accountId = cached.accountId;
      tenantInfo.user = user;
    }

    return tenantInfo;
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(cached: CachedTenantData): boolean {
    const now = new Date();
    const cacheAge = now.getTime() - new Date(cached.cachedAt).getTime();
    return cacheAge < TenantContextService.CACHE_TTL * 1000;
  }
}
