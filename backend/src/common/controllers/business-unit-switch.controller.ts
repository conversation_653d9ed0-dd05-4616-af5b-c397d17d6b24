import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantGuard } from '../guards/tenant.guard';
import { BusinessUnitSwitchService, BusinessUnitSwitchDto, BusinessUnitSwitchResponse } from '../services/business-unit-switch.service';
import { TenantContextService } from '../services/tenant-context.service';
import { BusinessUnit } from '../../database/entities/business-unit.entity';
import { CurrentTenant } from '../decorators/tenant-aware.decorator';
import { TenantContext } from '../interfaces/tenant-context.interface';

@ApiTags('Business Unit Switching')
@ApiBearerAuth()
@Controller('business-unit')
@UseGuards(JwtAuthGuard, TenantGuard)
export class BusinessUnitSwitchController {
  constructor(
    private readonly businessUnitSwitchService: BusinessUnitSwitchService,
    private readonly tenantContextService: TenantContextService,
  ) {}

  @Post('switch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Switch to a different business unit',
    description: 'Allows a user to switch their current business unit context within the same account.',
  })
  @ApiBody({
    description: 'Business unit switch request',
    type: 'object',
    schema: {
      type: 'object',
      properties: {
        businessUnitId: {
          type: 'string',
          example: 'bu-123e4567-e89b-12d3-a456-************'
        }
      },
      required: ['businessUnitId']
    },
    examples: {
      example1: {
        value: {
          businessUnitId: 'bu-123e4567-e89b-12d3-a456-************',
        },
        description: 'Switch to business unit with ID bu-123e4567-e89b-12d3-a456-************',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully switched business unit',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        businessUnit: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'bu-123e4567-e89b-12d3-a456-************' },
            name: { type: 'string', example: 'Main Store - São Paulo' },
            cnpj: { type: 'string', example: '12.345.678/0001-90' },
            status: { type: 'string', example: 'ACTIVE' },
          },
        },
        permissions: {
          type: 'array',
          items: { type: 'string' },
          example: ['invoice.create', 'invoice.read', 'client.manage'],
        },
        message: { type: 'string', example: 'Successfully switched to business unit: Main Store - São Paulo' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid business unit ID or user does not have access',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Business unit not found or user does not have access to it' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User not authenticated',
  })
  async switchBusinessUnit(
    @Body() switchDto: BusinessUnitSwitchDto,
    @CurrentTenant() tenantContext: TenantContext,
    @Request() req: any,
  ): Promise<BusinessUnitSwitchResponse> {
    // Get user ID from JWT payload in request
    const userId = req.user?.sub || req.user?.id;
    if (!userId) {
      throw new Error('User ID not found in request');
    }
    
    return this.businessUnitSwitchService.switchBusinessUnit(
      userId,
      tenantContext,
      switchDto,
    );
  }

  @Get('available')
  @ApiOperation({
    summary: 'Get available business units',
    description: 'Returns a list of business units that the current user has access to within their account.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of available business units',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'bu-123e4567-e89b-12d3-a456-************' },
              name: { type: 'string', example: 'Main Store - São Paulo' },
              cnpj: { type: 'string', example: '12.345.678/0001-90' },
              status: { type: 'string', example: 'ACTIVE' },
              city: { type: 'string', example: 'São Paulo' },
              state: { type: 'string', example: 'SP' },
              isCurrent: { type: 'boolean', example: true },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' },
            },
          },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 3 },
            current: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'bu-123e4567-e89b-12d3-a456-************' },
                name: { type: 'string', example: 'Main Store - São Paulo' },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User not authenticated',
  })
  async getAvailableBusinessUnits(
    @CurrentTenant() tenantContext: TenantContext,
    @Request() req: any,
  ): Promise<{
    success: boolean;
    data: any[];
    meta: {
      total: number;
      current?: {
        id: string;
        name: string;
      };
    };
  }> {
    // Get user ID from JWT payload in request
    const userId = req.user?.sub || req.user?.id;
    if (!userId) {
      throw new Error('User ID not found in request');
    }
    
    const availableUnits = await this.businessUnitSwitchService.getAvailableBusinessUnits(
      userId,
      tenantContext.account.id,
    );

    const currentBusinessUnit = await this.tenantContextService.getBusinessUnit();

    const data = availableUnits.map(unit => ({
      ...unit,
      isCurrent: unit.id === tenantContext.businessUnit?.id,
    }));

    return {
      success: true,
      data,
      meta: {
        total: availableUnits.length,
        current: currentBusinessUnit ? {
          id: currentBusinessUnit.id,
          name: currentBusinessUnit.name,
        } : undefined,
      },
    };
  }

  @Get('current')
  @ApiOperation({
    summary: 'Get current business unit context',
    description: 'Returns information about the current business unit context.',
  })
  @ApiResponse({
    status: 200,
    description: 'Current business unit information',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            account: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'acc-123e4567-e89b-12d3-a456-************' },
                name: { type: 'string', example: 'Tech Solutions LTDA' },
                status: { type: 'string', example: 'ACTIVE' },
              },
            },
            businessUnit: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'bu-123e4567-e89b-12d3-a456-************' },
                name: { type: 'string', example: 'Main Store - São Paulo' },
                cnpj: { type: 'string', example: '12.345.678/0001-90' },
                status: { type: 'string', example: 'ACTIVE' },
              },
            },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'usr-123e4567-e89b-12d3-a456-************' },
                firstName: { type: 'string', example: 'João' },
                lastName: { type: 'string', example: 'Silva' },
                email: { type: 'string', example: '<EMAIL>' },
                role: { type: 'string', example: 'ADMIN' },
              },
            },
          },
        },
      },
    },
  })
  async getCurrentContext(): Promise<{
    success: boolean;
    data: {
      account: any;
      businessUnit?: any;
      user?: any;
    } | null;
  }> {
    const tenantInfo = await this.tenantContextService.getTenantInfo();

    if (!tenantInfo) {
      return {
        success: false,
        data: null,
      };
    }

    return {
      success: true,
      data: {
        account: {
          id: tenantInfo.account.id,
          name: tenantInfo.account.name,
          status: tenantInfo.account.status,
        },
        businessUnit: tenantInfo.businessUnit ? {
          id: tenantInfo.businessUnit.id,
          name: tenantInfo.businessUnit.name,
          cnpj: tenantInfo.businessUnit.cnpj,
          status: tenantInfo.businessUnit.status,
        } : undefined,
        user: tenantInfo.user ? {
          id: tenantInfo.user.id,
          firstName: tenantInfo.user.firstName,
          lastName: tenantInfo.user.lastName,
          email: tenantInfo.user.email,
          role: tenantInfo.user.role,
        } : undefined,
      },
    };
  }
}
