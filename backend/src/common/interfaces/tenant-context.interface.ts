import { AccountStatus, SubscriptionTier, SubscriptionStatus, BusinessUnitStatus } from '../../database/entities/enums';

export interface TenantContext {
  account: {
    id: string;
    name: string;
    cnpj?: string;
    status: AccountStatus;
    trialEndsAt?: Date;
    settings?: Record<string, any>;
    metadata?: Record<string, any>;
  };
  businessUnit?: {
    id: string;
    name: string;
    cnpj: string;
    status: BusinessUnitStatus;
    isDefault: boolean;
    taxConfiguration?: Record<string, any>;
    nfeConfiguration?: Record<string, any>;
  } | null;
  subscription?: {
    tier: SubscriptionTier;
    status: SubscriptionStatus;
    features?: Record<string, any>;
    limits?: Record<string, any>;
  } | null;
}

export interface RequestWithTenant {
  tenant?: TenantContext;
  user?: any;
}
