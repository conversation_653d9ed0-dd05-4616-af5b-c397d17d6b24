import {
  Injectable,
  CanActivate,
  ExecutionContext,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';

import { Account } from '../../database/entities/account.entity';
import { BusinessUnit } from '../../database/entities/business-unit.entity';
import { User } from '../../database/entities/user.entity';
import { SubscriptionStatus } from '../../database/entities/enums';
import { SKIP_TENANT_CHECK_KEY } from '../decorators/skip-tenant-check.decorator';
import { TenantContext } from '../interfaces/tenant-context.interface';

export interface RequestWithTenant extends Request {
  tenant?: TenantContext;
}

@Injectable()
export class TenantGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private configService: ConfigService,
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(BusinessUnit)
    private businessUnitRepository: Repository<BusinessUnit>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if tenant validation should be skipped
    const skipTenantCheck = this.reflector.getAllAndOverride<boolean>(
      SKIP_TENANT_CHECK_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (skipTenantCheck) {
      return true;
    }

    const request = context.switchToHttp().getRequest<RequestWithTenant>();
    const user = request.user as any; // Assuming JWT auth has already populated this

    // Extract tenant information from headers
    const tenantHeaderName = this.configService.get<string>('auth.multiTenant.headerName') || 'x-tenant-id';
    const businessUnitHeaderName = this.configService.get<string>('auth.multiTenant.businessUnitHeaderName') || 'x-business-unit-id';
    
    const tenantId = request.headers[tenantHeaderName.toLowerCase()] as string;
    const businessUnitId = request.headers[businessUnitHeaderName.toLowerCase()] as string;

    // If no tenant ID is provided, check if it's required
    const tenantRequired = this.configService.get<boolean>('auth.multiTenant.required');
    if (!tenantId && tenantRequired) {
      throw new BadRequestException(`${tenantHeaderName} header is required`);
    }

    // If we have a user from JWT, validate tenant access
    if (user && tenantId) {
      await this.validateUserTenantAccess(user.id, tenantId, businessUnitId);
    }

    // Load tenant context
    if (tenantId) {
      const tenantContext = await this.loadTenantContext(tenantId, businessUnitId);
      request.tenant = tenantContext;
    }

    return true;
  }

  private async validateUserTenantAccess(
    userId: string,
    tenantId: string,
    businessUnitId?: string,
  ): Promise<void> {
    // Load user with account relationship
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['account'],
    });

    if (!user) {
      throw new ForbiddenException('User not found');
    }

    // Check if user belongs to the requested tenant
    if (user.accountId !== tenantId) {
      throw new ForbiddenException('Access denied to this tenant');
    }

    // If business unit is specified, validate access
    if (businessUnitId) {
      const businessUnit = await this.businessUnitRepository.findOne({
        where: {
          id: businessUnitId,
          accountId: tenantId,
        },
      });

      if (!businessUnit) {
        throw new ForbiddenException('Access denied to this business unit');
      }

      // Additional business unit access validation could be added here
      // For example, checking if user has specific permissions for this BU
    }
  }

  private async loadTenantContext(
    tenantId: string,
    businessUnitId?: string,
  ): Promise<TenantContext> {
    // Load account information
    const account = await this.accountRepository.findOne({
      where: { id: tenantId },
      relations: ['subscription'],
    });

    if (!account) {
      throw new BadRequestException('Invalid tenant ID');
    }

    // Check if account is active
    if (account.status === 'suspended' || account.status === 'cancelled') {
      throw new ForbiddenException(`Account is ${account.status}`);
    }

    let businessUnit: BusinessUnit | null = null;

    // Load business unit if specified
    if (businessUnitId) {
      businessUnit = await this.businessUnitRepository.findOne({
        where: {
          id: businessUnitId,
          accountId: tenantId,
        },
      });

      if (!businessUnit) {
        throw new BadRequestException('Invalid business unit ID');
      }

      // Check if business unit is active
      if (businessUnit.status !== 'active') {
        throw new ForbiddenException(`Business unit is ${businessUnit.status}`);
      }
    } else {
      // If no business unit specified, load default one
      businessUnit = await this.businessUnitRepository.findOne({
        where: {
          accountId: tenantId,
          // Remove isDefault property since it doesn't exist in the entity
        },
        order: { createdAt: 'ASC' }, // Get the first business unit as default
      });
    }

    // Build tenant context
    const context: TenantContext = {
      account: {
        id: account.id,
        name: account.name,
        cnpj: account.cnpj,
        status: account.status,
        trialEndsAt: account.trialEndsAt,
        settings: {}, // account.getSettings() method doesn't exist
        metadata: {}, // account.getMetadata() method doesn't exist
      },
      businessUnit: businessUnit ? {
        id: businessUnit.id,
        name: businessUnit.name,
        cnpj: businessUnit.cnpj,
        status: businessUnit.status,
        isDefault: false, // isDefault property doesn't exist in BusinessUnit
        taxConfiguration: {}, // getTaxConfiguration() method doesn't exist
        nfeConfiguration: {}, // getNfeConfiguration() method doesn't exist
      } : null,
      subscription: account.subscription ? {
        tier: account.subscription.tier,
        status: account.subscription.status as unknown as SubscriptionStatus,
        features: {}, // getFeatures() method doesn't exist
        limits: {}, // getLimits() method doesn't exist
      } : null,
    };

    return context;
  }
}
