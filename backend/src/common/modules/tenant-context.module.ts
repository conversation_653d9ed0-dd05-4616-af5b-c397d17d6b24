import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
// EventEmitterModule is now globally available from AppModule
import { BusinessUnitSwitchService } from '../services/business-unit-switch.service';
import { TenantContextService } from '../services/tenant-context.service';
import { BusinessUnitSwitchController } from '../controllers/business-unit-switch.controller';
import { User } from '../../database/entities/user.entity';
import { Account } from '../../database/entities/account.entity';
import { BusinessUnit } from '../../database/entities/business-unit.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Account, BusinessUnit]),
    CacheModule.register(),
    // EventEmitterModule is now globally available from AppModule
  ],
  controllers: [BusinessUnitSwitchController],
  providers: [BusinessUnitSwitchService, TenantContextService],
  exports: [BusinessUnitSwitchService, TenantContextService],
})
export class TenantContextModule {}
