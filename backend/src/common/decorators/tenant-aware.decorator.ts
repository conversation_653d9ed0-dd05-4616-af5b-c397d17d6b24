import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';
import { TenantContext } from '../interfaces/tenant-context.interface';
import { TenantQueryOptions } from '../helpers/tenant-query.helper';

/**
 * Key for tenant-aware metadata
 */
export const TENANT_AWARE_KEY = 'tenant-aware';

/**
 * Key for tenant query options metadata
 */
export const TENANT_QUERY_OPTIONS_KEY = 'tenant-query-options';

/**
 * Decorator to mark a method as tenant-aware
 * This will automatically apply tenant filters to database queries
 */
export const TenantAware = (options: TenantQueryOptions = {}) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // Set metadata to indicate this method is tenant-aware
    SetMetadata(TENANT_AWARE_KEY, true)(target, propertyKey, descriptor);
    SetMetadata(TENANT_QUERY_OPTIONS_KEY, options)(target, propertyKey, descriptor);
    
    return descriptor;
  };
};

/**
 * Parameter decorator to inject the current tenant context
 * Usage: async someMethod(@TenantContext() context: TenantContext)
 */
export const CurrentTenant = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): TenantContext => {
    const request = ctx.switchToHttp().getRequest();
    return request.tenantContext;
  },
);

/**
 * Decorator to skip tenant filtering for specific methods
 * Useful for admin operations or cross-tenant queries
 */
export const SkipTenantFilter = () => {
  return SetMetadata('skip-tenant-filter', true);
};

/**
 * Decorator to configure tenant filtering options for a method
 */
export const TenantFilterOptions = (options: TenantQueryOptions) => {
  return SetMetadata(TENANT_QUERY_OPTIONS_KEY, options);
};

/**
 * Decorator to mark a method as requiring account-level access only
 * (ignores business unit filtering)
 */
export const AccountLevel = () => {
  return TenantFilterOptions({
    filterByAccount: true,
    filterByBusinessUnit: false,
  });
};

/**
 * Decorator to mark a method as requiring business unit-level access
 * (applies both account and business unit filtering)
 */
export const BusinessUnitLevel = () => {
  return TenantFilterOptions({
    filterByAccount: true,
    filterByBusinessUnit: true,
  });
};

/**
 * Decorator to mark a method as cross-tenant (admin only)
 * Completely skips tenant filtering
 */
export const CrossTenant = () => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    SetMetadata('skip-tenant-filter', true)(target, propertyKey, descriptor);
    SetMetadata('admin-only', true)(target, propertyKey, descriptor);
    return descriptor;
  };
};
