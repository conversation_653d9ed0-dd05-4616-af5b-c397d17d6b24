import { SelectQueryBuilder, Repository, FindManyOptions, FindOneOptions, FindOptionsWhere } from 'typeorm';
import { TenantContext } from '../interfaces/tenant-context.interface';
import { Logger } from '@nestjs/common';

/**
 * Interface for entities that support tenant isolation
 */
export interface TenantAwareEntity {
  accountId?: string;
  businessUnitId?: string;
}

/**
 * Options for tenant-aware queries
 */
export interface TenantQueryOptions {
  /** Whether to filter by account ID */
  filterByAccount?: boolean;
  /** Whether to filter by business unit ID */
  filterByBusinessUnit?: boolean;
  /** Whether to include soft-deleted records */
  includeSoftDeleted?: boolean;
  /** Custom tenant context (overrides current context) */
  customContext?: Partial<TenantContext>;
}

/**
 * Helper class for creating tenant-aware database queries
 */
export class TenantQueryHelper {
  private static readonly logger = new Logger(TenantQueryHelper.name);

  /**
   * Apply tenant filters to a TypeORM query builder
   */
  static applyTenantFilter<T extends TenantAwareEntity>(
    queryBuilder: SelectQueryBuilder<T>,
    tenantContext: TenantContext,
    options: TenantQueryOptions = {},
  ): SelectQueryBuilder<T> {
    const {
      filterByAccount = true,
      filterByBusinessUnit = true,
      customContext,
    } = options;

    const context = customContext ? { ...tenantContext, ...customContext } : tenantContext;
    
    if (!context.account?.id) {
      this.logger.warn('No account ID in tenant context, skipping tenant filters');
      return queryBuilder;
    }

    const alias = queryBuilder.alias;

    // Apply account filter
    if (filterByAccount && context.account?.id) {
      queryBuilder.andWhere(`${alias}.accountId = :accountId`, {
        accountId: context.account.id,
      });
    }

    // Apply business unit filter if specified and available
    if (filterByBusinessUnit && context.businessUnit?.id) {
      queryBuilder.andWhere(`${alias}.businessUnitId = :businessUnitId`, {
        businessUnitId: context.businessUnit.id,
      });
    }

    this.logger.debug(
      `Applied tenant filters - Account: ${context.account.id}, BusinessUnit: ${context.businessUnit?.id || 'none'}`,
    );

    return queryBuilder;
  }

  /**
   * Apply tenant filters to TypeORM find options
   */
  static applyTenantFindOptions<T extends TenantAwareEntity>(
    findOptions: FindManyOptions<T> | FindOneOptions<T>,
    tenantContext: TenantContext,
    options: TenantQueryOptions = {},
  ): FindManyOptions<T> | FindOneOptions<T> {
    const {
      filterByAccount = true,
      filterByBusinessUnit = true,
      customContext,
    } = options;

    const context = customContext ? { ...tenantContext, ...customContext } : tenantContext;
    
    if (!context.account?.id) {
      this.logger.warn('No account ID in tenant context, skipping tenant filters');
      return findOptions;
    }

    const where = findOptions.where || ({} as FindOptionsWhere<T>);
    const whereArray = Array.isArray(where) ? where : [where];

    // Apply tenant filters to all where conditions
    const updatedWhere = whereArray.map((condition) => {
      const tenantCondition = { ...condition } as FindOptionsWhere<T>;

      if (filterByAccount && context.account?.id) {
        (tenantCondition as any).accountId = context.account.id;
      }

      if (filterByBusinessUnit && context.businessUnit?.id) {
        (tenantCondition as any).businessUnitId = context.businessUnit.id;
      }

      return tenantCondition;
    });

    return {
      ...findOptions,
      where: Array.isArray(where) ? updatedWhere : updatedWhere[0],
    };
  }

  /**
   * Create a tenant-aware repository wrapper
   */
  static createTenantRepository<T extends TenantAwareEntity>(
    repository: Repository<T>,
    tenantContext: TenantContext,
  ): TenantAwareRepository<T> {
    return new TenantAwareRepository(repository, tenantContext);
  }

  /**
   * Validate that an entity belongs to the current tenant
   */
  static validateTenantOwnership<T extends TenantAwareEntity>(
    entity: T,
    tenantContext: TenantContext,
    options: TenantQueryOptions = {},
  ): boolean {
    const {
      filterByAccount = true,
      filterByBusinessUnit = true,
    } = options;

    if (!tenantContext.account?.id) {
      this.logger.warn('No account ID in tenant context, skipping ownership validation');
      return false;
    }

    // Check account ownership
    if (filterByAccount && entity.accountId !== tenantContext.account.id) {
      this.logger.warn(
        `Entity account mismatch - Entity: ${entity.accountId}, Context: ${tenantContext.account.id}`,
      );
      return false;
    }

    // Check business unit ownership if specified
    if (filterByBusinessUnit && tenantContext.businessUnit?.id) {
      if (entity.businessUnitId !== tenantContext.businessUnit.id) {
        this.logger.warn(
          `Entity business unit mismatch - Entity: ${entity.businessUnitId}, Context: ${tenantContext.businessUnit.id}`,
        );
        return false;
      }
    }

    return true;
  }

  /**
   * Add tenant information to an entity before saving
   */
  static addTenantInfo<T extends TenantAwareEntity>(
    entity: T,
    tenantContext: TenantContext,
    options: TenantQueryOptions = {},
  ): T {
    const {
      filterByAccount = true,
      filterByBusinessUnit = true,
    } = options;

    if (filterByAccount && tenantContext.account?.id) {
      entity.accountId = tenantContext.account.id;
    }

    if (filterByBusinessUnit && tenantContext.businessUnit?.id) {
      entity.businessUnitId = tenantContext.businessUnit.id;
    }

    return entity;
  }
}

/**
 * Tenant-aware repository wrapper that automatically applies tenant filters
 */
export class TenantAwareRepository<T extends TenantAwareEntity> {
  private readonly logger = new Logger(TenantAwareRepository.name);

  constructor(
    private readonly repository: Repository<T>,
    private readonly tenantContext: TenantContext,
  ) {}

  /**
   * Create query builder with tenant filters applied
   */
  createTenantQueryBuilder(
    alias?: string,
    options: TenantQueryOptions = {},
  ): SelectQueryBuilder<T> {
    const queryBuilder = this.repository.createQueryBuilder(alias);
    return TenantQueryHelper.applyTenantFilter(queryBuilder, this.tenantContext, options);
  }

  /**
   * Find one entity with tenant filters
   */
  async findOne(
    options: FindOneOptions<T>,
    tenantOptions: TenantQueryOptions = {},
  ): Promise<T | null> {
    const updatedOptions = TenantQueryHelper.applyTenantFindOptions(
      options,
      this.tenantContext,
      tenantOptions,
    ) as FindOneOptions<T>;

    return this.repository.findOne(updatedOptions);
  }

  /**
   * Find many entities with tenant filters
   */
  async find(
    options: FindManyOptions<T> = {},
    tenantOptions: TenantQueryOptions = {},
  ): Promise<T[]> {
    const updatedOptions = TenantQueryHelper.applyTenantFindOptions(
      options,
      this.tenantContext,
      tenantOptions,
    ) as FindManyOptions<T>;

    return this.repository.find(updatedOptions);
  }

  /**
   * Count entities with tenant filters
   */
  async count(
    options: FindManyOptions<T> = {},
    tenantOptions: TenantQueryOptions = {},
  ): Promise<number> {
    const updatedOptions = TenantQueryHelper.applyTenantFindOptions(
      options,
      this.tenantContext,
      tenantOptions,
    ) as FindManyOptions<T>;

    return this.repository.count(updatedOptions);
  }

  /**
   * Save entity with tenant information
   */
  async save(
    entity: T,
    tenantOptions: TenantQueryOptions = {},
  ): Promise<T> {
    const entityWithTenant = TenantQueryHelper.addTenantInfo(
      entity,
      this.tenantContext,
      tenantOptions,
    );

    return this.repository.save(entityWithTenant);
  }

  /**
   * Save multiple entities with tenant information
   */
  async saveMany(
    entities: T[],
    tenantOptions: TenantQueryOptions = {},
  ): Promise<T[]> {
    const entitiesWithTenant = entities.map((entity) =>
      TenantQueryHelper.addTenantInfo(entity, this.tenantContext, tenantOptions),
    );

    return this.repository.save(entitiesWithTenant);
  }

  /**
   * Delete entity with tenant validation
   */
  async delete(
    id: string,
    tenantOptions: TenantQueryOptions = {},
  ): Promise<void> {
    // First verify the entity belongs to the tenant
    const entity = await this.findOne({ where: { id } as any }, tenantOptions);
    
    if (!entity) {
      throw new Error('Entity not found or access denied');
    }

    await this.repository.delete(id);
    this.logger.log(`Deleted entity ${id} for tenant ${this.tenantContext.account?.id}`);
  }

  /**
   * Soft delete entity with tenant validation
   */
  async softDelete(
    id: string,
    tenantOptions: TenantQueryOptions = {},
  ): Promise<void> {
    // First verify the entity belongs to the tenant
    const entity = await this.findOne({ where: { id } as any }, tenantOptions);
    
    if (!entity) {
      throw new Error('Entity not found or access denied');
    }

    await this.repository.softDelete(id);
    this.logger.log(`Soft deleted entity ${id} for tenant ${this.tenantContext.account?.id}`);
  }

  /**
   * Get the underlying repository
   */
  getRepository(): Repository<T> {
    return this.repository;
  }

  /**
   * Get the current tenant context
   */
  getTenantContext(): TenantContext {
    return this.tenantContext;
  }
}
