import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable, tap, catchError, throwError } from 'rxjs';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Request, Response } from 'express';

import { RequestWithTenant } from '../interfaces/tenant-context.interface';
import { SKIP_AUDIT_KEY } from '../decorators/skip-audit.decorator';

export interface AuditEventData {
  // Request Information
  method: string;
  url: string;
  endpoint: string;
  userAgent?: string;
  ipAddress?: string;
  
  // User and Tenant Context
  userId?: string;
  accountId?: string;
  businessUnitId?: string;
  
  // Request/Response Data
  requestBody?: any;
  responseBody?: any;
  statusCode: number;
  
  // Timing
  startTime: Date;
  endTime: Date;
  duration: number;
  
  // Error Information
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
  
  // Additional Context
  sessionId?: string;
  requestId?: string;
  apiKeyId?: string;
  
  // Security Context
  isSuspicious?: boolean;
  riskScore?: number;
  securityFlags?: string[];
}

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditInterceptor.name);

  constructor(
    private reflector: Reflector,
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Check if audit should be skipped
    const skipAudit = this.reflector.getAllAndOverride<boolean>(
      SKIP_AUDIT_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (skipAudit) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<RequestWithTenant & Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = new Date();

    // Extract audit data from request
    const auditData = this.extractAuditData(request, response, startTime);

    return next.handle().pipe(
      tap((responseData) => {
        this.handleSuccess(auditData, responseData, response);
      }),
      catchError((error) => {
        this.handleError(auditData, error, response);
        return throwError(() => error);
      }),
    );
  }

  private extractAuditData(
    request: RequestWithTenant & Request,
    response: Response,
    startTime: Date,
  ): Partial<AuditEventData> {
    const user = request.user as any;
    const tenant = request.tenant;

    return {
      method: request.method,
      url: request.url,
      endpoint: `${request.method} ${request.route?.path || request.url}`,
      userAgent: request.headers['user-agent'],
      ipAddress: this.getClientIp(request),
      userId: user?.id,
      accountId: tenant?.account?.id,
      businessUnitId: tenant?.businessUnit?.id,
      requestBody: this.sanitizeRequestBody(request.body),
      startTime,
      sessionId: request.headers['x-session-id'] as string,
      requestId: request.headers['x-request-id'] as string,
      apiKeyId: request.headers['x-api-key-id'] as string,
    };
  }

  private handleSuccess(
    auditData: Partial<AuditEventData>,
    responseData: any,
    response: Response,
  ): void {
    const endTime = new Date();
    const duration = auditData.startTime ? endTime.getTime() - auditData.startTime.getTime() : 0;

    const completeAuditData: AuditEventData = {
      ...auditData,
      responseBody: this.sanitizeResponseBody(responseData),
      statusCode: response.statusCode,
      endTime,
      duration,
    } as AuditEventData;

    // Assess security risk
    this.assessSecurityRisk(completeAuditData);

    // Emit audit event
    this.emitAuditEvent('api.request.success', completeAuditData);

    // Log if enabled
    if (this.configService.get('app.monitoring.enableRequestLogging')) {
      this.logger.log(`${completeAuditData.method} ${completeAuditData.url} - ${completeAuditData.statusCode} - ${duration}ms`);
    }
  }

  private handleError(
    auditData: Partial<AuditEventData>,
    error: any,
    response: Response,
  ): void {
    const endTime = new Date();
    const duration = auditData.startTime ? endTime.getTime() - auditData.startTime.getTime() : 0;

    const completeAuditData: AuditEventData = {
      ...auditData,
      statusCode: response.statusCode || 500,
      endTime,
      duration,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code || error.status?.toString(),
      },
    } as AuditEventData;

    // Mark as potentially suspicious due to error
    completeAuditData.isSuspicious = this.isErrorSuspicious(error);
    completeAuditData.riskScore = completeAuditData.isSuspicious ? 25 : 10;

    // Assess security risk
    this.assessSecurityRisk(completeAuditData);

    // Emit audit event
    this.emitAuditEvent('api.request.error', completeAuditData);

    // Log error
    this.logger.error(
      `${completeAuditData.method} ${completeAuditData.url} - ${completeAuditData.statusCode} - ${duration}ms - ${error.message}`,
      error.stack,
    );
  }

  private assessSecurityRisk(auditData: AuditEventData): void {
    const securityFlags: string[] = [];
    let riskScore = auditData.riskScore || 0;

    // Check for suspicious patterns
    if (auditData.method === 'POST' || auditData.method === 'PUT' || auditData.method === 'DELETE') {
      riskScore += 5;
    }

    // Check for authentication endpoints
    if (auditData.url.includes('/auth/')) {
      riskScore += 10;
      securityFlags.push('auth_endpoint');
    }

    // Check for admin endpoints
    if (auditData.url.includes('/admin/')) {
      riskScore += 15;
      securityFlags.push('admin_endpoint');
    }

    // Check for failed authentication
    if (auditData.statusCode === 401 || auditData.statusCode === 403) {
      riskScore += 20;
      securityFlags.push('access_denied');
    }

    // Check for multiple requests from same IP (basic detection)
    // This would require a cache/store to track, simplified here
    if (auditData.error) {
      riskScore += 15;
      securityFlags.push('error_occurred');
    }

    // Check for unusual user agents
    if (this.isUnusualUserAgent(auditData.userAgent)) {
      riskScore += 10;
      securityFlags.push('unusual_user_agent');
    }

    // Update audit data
    auditData.riskScore = Math.min(riskScore, 100); // Cap at 100
    auditData.isSuspicious = riskScore >= 50;
    auditData.securityFlags = securityFlags;
  }

  private isErrorSuspicious(error: any): boolean {
    // Check for security-related errors
    const suspiciousErrors = [
      'Unauthorized',
      'Forbidden',
      'Invalid token',
      'Access denied',
      'Authentication failed',
      'Invalid credentials',
    ];

    return suspiciousErrors.some(pattern => 
      error.message?.includes(pattern) || error.name?.includes(pattern)
    );
  }

  private isUnusualUserAgent(userAgent?: string): boolean {
    if (!userAgent) return true;

    // Check for bot patterns or unusual user agents
    const botPatterns = [
      'bot', 'crawler', 'spider', 'scraper',
      'curl', 'wget', 'python', 'java',
      'postman', 'insomnia',
    ];

    return botPatterns.some(pattern => 
      userAgent.toLowerCase().includes(pattern)
    );
  }

  private sanitizeRequestBody(body: any): any {
    if (!body) return null;

    // Clone and remove sensitive data
    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private sanitizeResponseBody(body: any): any {
    if (!body) return null;

    // For large responses, only include metadata
    if (typeof body === 'object' && JSON.stringify(body).length > 10000) {
      return {
        _meta: {
          type: Array.isArray(body) ? 'array' : 'object',
          size: Array.isArray(body) ? body.length : Object.keys(body).length,
          truncated: true,
        }
      };
    }

    // Clone and remove sensitive data
    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];

    const removeSensitiveData = (obj: any) => {
      if (typeof obj === 'object' && obj !== null) {
        for (const [key, value] of Object.entries(obj)) {
          if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
            obj[key] = '[REDACTED]';
          } else if (typeof value === 'object') {
            removeSensitiveData(value);
          }
        }
      }
    };

    removeSensitiveData(sanitized);
    return sanitized;
  }

  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }

  private emitAuditEvent(eventName: string, data: AuditEventData): void {
    try {
      this.eventEmitter.emit(eventName, data);
    } catch (error) {
      this.logger.error('Failed to emit audit event', error);
    }
  }
}
