import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON>x<PERSON>,
  CallHandler,
} from '@nestjs/common';
import { Observable, map } from 'rxjs';
import { Response } from 'express';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  meta?: {
    timestamp: string;
    path: string;
    method: string;
    version: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  errors?: any[];
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse<Response>();

    return next.handle().pipe(
      map((data) => {
        // Don't transform response if it's already in the correct format
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }

        // Handle different response types
        let responseData: any = data;
        let pagination: any = undefined;

        // Check if response includes pagination metadata
        if (data && typeof data === 'object' && 'items' in data && 'meta' in data) {
          responseData = data.items;
          pagination = {
            page: data.meta.page || 1,
            limit: data.meta.limit || 20,
            total: data.meta.total || 0,
            totalPages: data.meta.totalPages || 1,
            hasNext: data.meta.hasNext || false,
            hasPrev: data.meta.hasPrev || false,
          };
        }

        // Build standardized response
        const apiResponse: ApiResponse<T> = {
          success: true,
          data: responseData,
          meta: {
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method,
            version: 'v1', // Could be extracted from headers or config
          },
        };

        // Add pagination metadata if present
        if (pagination) {
          if (apiResponse.meta) {
            apiResponse.meta.pagination = pagination;
          }
        }

        // Add success message for certain operations
        if (request.method === 'POST') {
          apiResponse.message = 'Resource created successfully';
        } else if (request.method === 'PUT' || request.method === 'PATCH') {
          apiResponse.message = 'Resource updated successfully';
        } else if (request.method === 'DELETE') {
          apiResponse.message = 'Resource deleted successfully';
          // For DELETE operations, we might not have data
          if (!responseData) {
            delete apiResponse.data;
          }
        }

        return apiResponse;
      }),
    );
  }
}
