import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { DataSource, Repository } from 'typeorm';
import { Account } from '../accounts/entities/account.entity';
import { Emitter } from '../emitters/entities/emitter.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UserAccount } from './entities/user-account.entity';
import { UserRole } from '../database/entities/enums';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Account)
    private accountsRepository: Repository<Account>,
    @InjectRepository(UserAccount)
    private userAccountsRepository: Repository<UserAccount>,
    private dataSource: DataSource,
  ) {}

  async create(createUserDto: CreateUserDto) {
    const { email, password, accountName, emitter: emitterDto } = createUserDto;

    // Check if user already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('Email already in use');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 10);

    // Create user, account, and link them in a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create user
      const user = this.usersRepository.create({
        email,
        passwordHash,
      });
      await queryRunner.manager.save(user);

      // Create account
      const account = this.accountsRepository.create({
        name: accountName,
      });
      await queryRunner.manager.save(account);

      // Link user to account with owner role
      const userAccount = this.userAccountsRepository.create({
        userId: user.id,
        accountId: account.id,
        role: UserRole.OWNER,
      });
      await queryRunner.manager.save(userAccount);

      // Optionally create an emitter for this account
      let createdEmitter: Emitter | null = null;
      if (emitterDto) {
        const emitterEntity = queryRunner.manager.create(Emitter, {
          accountId: account.id,
          ...emitterDto,
        });
        createdEmitter = await queryRunner.manager.save(emitterEntity);
      }

      await queryRunner.commitTransaction();

      // Prepare return payload
      const result: any = {
        id: user.id,
        email: user.email,
        account: {
          id: account.id,
          name: account.name,
        },
      };
      if (createdEmitter) {
        result.emitter = createdEmitter;
      }
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findByEmail(email: string) {
    const user = await this.usersRepository.findOne({
      where: { email },
      relations: {
        userAccounts: {
          account: true,
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findById(id: string) {
    const user = await this.usersRepository.findOne({
      where: { id },
      relations: {
        userAccounts: {
          account: true,
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async validateUser(email: string, password: string) {
    try {
      const user = await this.findByEmail(email);
      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

      if (isPasswordValid) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { passwordHash, ...result } = user;
        return result;
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}
