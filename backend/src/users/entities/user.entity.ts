import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import type { RefreshToken } from '../../auth/entities/refresh-token.entity';
import { UserAccount } from './user-account.entity';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  passwordHash: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => UserAccount, (userAccount) => userAccount.user)
  userAccounts: UserAccount[];

  @OneToMany(() => require('../../auth/entities/refresh-token.entity').RefreshToken,
             (refreshToken: RefreshToken) => refreshToken.user)
  refreshTokens: RefreshToken[];
}
