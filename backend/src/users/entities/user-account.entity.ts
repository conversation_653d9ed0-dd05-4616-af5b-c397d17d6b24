import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { Account } from '../../accounts/entities/account.entity';
import { User } from './user.entity';
import { UserRole } from '../../database/entities/enums';

@Entity('user_accounts')
@Unique(['userId', 'accountId'])
export class UserAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  accountId: string;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'varchar' : 'enum',
    enum: UserRole,
    default: UserRole.MEMBER,
  })
  role: UserRole;

  @ManyToOne(() => User, (user) => user.userAccounts)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Account, (account) => account.userAccounts)
  @JoinColumn({ name: 'accountId' })
  account: Account;
}
