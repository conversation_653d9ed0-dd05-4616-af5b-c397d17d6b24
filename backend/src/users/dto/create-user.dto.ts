import {
  IsEmail,
  IsNotEmpty,
  IsString,
  <PERSON><PERSON>ength,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateEmitterDto } from '../../emitters/dto/create-emitter.dto';

export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;

  @IsString()
  @IsNotEmpty()
  accountName: string;

  /** Optional initial emitter to create for the user */
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateEmitterDto)
  emitter?: CreateEmitterDto;
}
