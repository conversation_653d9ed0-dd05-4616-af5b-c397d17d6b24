import { Readable } from 'stream';

/**
 * Abstraction for file storage providers (local disk, S3, etc.)
 */
export interface IStorageProvider {
  /**
   * Persist XML data for the given NFe document ID, return a file key or path.
   */
  saveXml(nfeId: string, xml: string | Buffer): Promise<string>;

  /**
   * Persist PDF data (DANFE) for the given NFe document ID, return a file key or path.
   */
  saveDanfe(nfeId: string, pdf: Buffer): Promise<string>;

  /**
   * Retrieve a read stream for the stored file (by key or path).
   */
  getFileStream(key: string): Promise<Readable>;

  /**
   * Generate a presigned URL for downloading the file.
   * @param key The file key or path
   * @param expiresIn Expiration time in seconds (default: 3600)
   * @returns A presigned URL for downloading the file
   */
  getPresignedUrl(key: string, expiresIn?: number): Promise<string>;
}
