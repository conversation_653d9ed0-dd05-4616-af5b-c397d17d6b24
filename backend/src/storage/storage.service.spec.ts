import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, StreamableFile } from '@nestjs/common';
import { StorageService } from './storage.service';
import { DocumentStorage } from './entities/document-storage.entity';
import { NFeDocument } from '../nfe/entities/nfe-document.entity';
import { Emitter } from '../emitters/entities/emitter.entity';
import { IStorageProvider } from './interfaces/storage-provider.interface';
import { Readable } from 'stream';

const mockDocumentStorageRepository = () => ({
  findOne: jest.fn(),
});

const mockNfeDocumentsRepository = () => ({
  findOne: jest.fn(),
});

const mockEmittersRepository = () => ({
  findOne: jest.fn(),
});

const mockStorageProvider = () => ({
  getFileStream: jest.fn(),
  getPresignedUrl: jest.fn(),
});

describe('StorageService', () => {
  let service: StorageService;
  let documentStorageRepository: Repository<DocumentStorage>;
  let nfeDocumentsRepository: Repository<NFeDocument>;
  let storageProvider: IStorageProvider;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageService,
        {
          provide: getRepositoryToken(DocumentStorage),
          useFactory: mockDocumentStorageRepository,
        },
        {
          provide: getRepositoryToken(NFeDocument),
          useFactory: mockNfeDocumentsRepository,
        },
        {
          provide: getRepositoryToken(Emitter),
          useFactory: mockEmittersRepository,
        },
        {
          provide: 'IStorageProvider',
          useFactory: mockStorageProvider,
        },
      ],
    }).compile();

    service = module.get<StorageService>(StorageService);
    documentStorageRepository = module.get<Repository<DocumentStorage>>(
      getRepositoryToken(DocumentStorage),
    );
    nfeDocumentsRepository = module.get<Repository<NFeDocument>>(
      getRepositoryToken(NFeDocument),
    );
    storageProvider = module.get<IStorageProvider>('IStorageProvider');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getXmlFile', () => {
    it('should return a StreamableFile with the XML content', async () => {
      const nfeId = 'test-nfe-id';
      const accountId = 'test-account-id';
      
      // Mock NFe document
      const mockNfeDocument = {
        id: nfeId,
        emitter: {
          accountId,
        },
      };
      
      // Mock storage record
      const mockStorage = {
        nfeDocumentId: nfeId,
        xmlPath: '/path/to/xml',
      };
      
      // Mock readable stream
      const mockStream = new Readable();
      mockStream.push('test xml content');
      mockStream.push(null);
      
      jest.spyOn(nfeDocumentsRepository, 'findOne').mockResolvedValue(mockNfeDocument as any);
      jest.spyOn(documentStorageRepository, 'findOne').mockResolvedValue(mockStorage as any);
      jest.spyOn(storageProvider, 'getFileStream').mockResolvedValue(mockStream);
      
      const result = await service.getXmlFile(nfeId, accountId);
      
      expect(result).toBeInstanceOf(StreamableFile);
      expect(nfeDocumentsRepository.findOne).toHaveBeenCalledWith({
        where: { id: nfeId },
        relations: ['emitter'],
      });
      expect(documentStorageRepository.findOne).toHaveBeenCalledWith({
        where: { nfeDocumentId: nfeId },
      });
      expect(storageProvider.getFileStream).toHaveBeenCalledWith(mockStorage.xmlPath);
    });
    
    it('should throw NotFoundException if XML path is not found', async () => {
      const nfeId = 'test-nfe-id';
      const accountId = 'test-account-id';
      
      // Mock NFe document
      const mockNfeDocument = {
        id: nfeId,
        emitter: {
          accountId,
        },
      };
      
      // Mock storage record without XML path
      const mockStorage = {
        nfeDocumentId: nfeId,
        xmlPath: null,
      };
      
      jest.spyOn(nfeDocumentsRepository, 'findOne').mockResolvedValue(mockNfeDocument as any);
      jest.spyOn(documentStorageRepository, 'findOne').mockResolvedValue(mockStorage as any);
      
      await expect(service.getXmlFile(nfeId, accountId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getXmlPresignedUrl', () => {
    it('should return a presigned URL for the XML file', async () => {
      const nfeId = 'test-nfe-id';
      const accountId = 'test-account-id';
      const expiresIn = 1800; // 30 minutes
      
      // Mock NFe document
      const mockNfeDocument = {
        id: nfeId,
        emitter: {
          accountId,
        },
      };
      
      // Mock storage record
      const mockStorage = {
        nfeDocumentId: nfeId,
        xmlPath: '/path/to/xml',
      };
      
      // Mock presigned URL
      const mockPresignedUrl = 'https://example.com/presigned-url';
      
      jest.spyOn(nfeDocumentsRepository, 'findOne').mockResolvedValue(mockNfeDocument as any);
      jest.spyOn(documentStorageRepository, 'findOne').mockResolvedValue(mockStorage as any);
      jest.spyOn(storageProvider, 'getPresignedUrl').mockResolvedValue(mockPresignedUrl);
      
      const result = await service.getXmlPresignedUrl(nfeId, accountId, expiresIn);
      
      expect(result).toBe(mockPresignedUrl);
      expect(nfeDocumentsRepository.findOne).toHaveBeenCalledWith({
        where: { id: nfeId },
        relations: ['emitter'],
      });
      expect(documentStorageRepository.findOne).toHaveBeenCalledWith({
        where: { nfeDocumentId: nfeId },
      });
      expect(storageProvider.getPresignedUrl).toHaveBeenCalledWith(mockStorage.xmlPath, expiresIn);
    });
    
    it('should throw NotFoundException if XML path is not found', async () => {
      const nfeId = 'test-nfe-id';
      const accountId = 'test-account-id';
      
      // Mock NFe document
      const mockNfeDocument = {
        id: nfeId,
        emitter: {
          accountId,
        },
      };
      
      // Mock storage record without XML path
      const mockStorage = {
        nfeDocumentId: nfeId,
        xmlPath: null,
      };
      
      jest.spyOn(nfeDocumentsRepository, 'findOne').mockResolvedValue(mockNfeDocument as any);
      jest.spyOn(documentStorageRepository, 'findOne').mockResolvedValue(mockStorage as any);
      
      await expect(service.getXmlPresignedUrl(nfeId, accountId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getDanfePresignedUrl', () => {
    it('should return a presigned URL for the DANFE file', async () => {
      const nfeId = 'test-nfe-id';
      const accountId = 'test-account-id';
      const expiresIn = 1800; // 30 minutes
      
      // Mock NFe document
      const mockNfeDocument = {
        id: nfeId,
        emitter: {
          accountId,
        },
      };
      
      // Mock storage record
      const mockStorage = {
        nfeDocumentId: nfeId,
        danfePath: '/path/to/danfe',
      };
      
      // Mock presigned URL
      const mockPresignedUrl = 'https://example.com/presigned-url';
      
      jest.spyOn(nfeDocumentsRepository, 'findOne').mockResolvedValue(mockNfeDocument as any);
      jest.spyOn(documentStorageRepository, 'findOne').mockResolvedValue(mockStorage as any);
      jest.spyOn(storageProvider, 'getPresignedUrl').mockResolvedValue(mockPresignedUrl);
      
      const result = await service.getDanfePresignedUrl(nfeId, accountId, expiresIn);
      
      expect(result).toBe(mockPresignedUrl);
      expect(nfeDocumentsRepository.findOne).toHaveBeenCalledWith({
        where: { id: nfeId },
        relations: ['emitter'],
      });
      expect(documentStorageRepository.findOne).toHaveBeenCalledWith({
        where: { nfeDocumentId: nfeId },
      });
      expect(storageProvider.getPresignedUrl).toHaveBeenCalledWith(mockStorage.danfePath, expiresIn);
    });
  });
});
