import {
  Inject,
  Injectable,
  NotFoundException,
  StreamableFile,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Emitter } from '../emitters/entities/emitter.entity';
import { NFeDocument } from '../nfe/entities/nfe-document.entity';
import { DocumentStorage } from './entities/document-storage.entity';
import { IStorageProvider } from './interfaces/storage-provider.interface';

@Injectable()
export class StorageService {
  constructor(
    @InjectRepository(DocumentStorage)
    private documentStorageRepository: Repository<DocumentStorage>,
    @InjectRepository(NFeDocument)
    private nfeDocumentsRepository: Repository<NFeDocument>,
    @InjectRepository(Emitter)
    private emittersRepository: Repository<Emitter>,
    @Inject('IStorageProvider')
    private storageProvider: IStorageProvider,
  ) {}

  async getXmlFile(nfeId: string, accountId: string): Promise<StreamableFile> {
    const storage = await this.getStorage(nfeId, accountId);

    if (!storage.xmlPath) {
      throw new NotFoundException('XML file not found');
    }

    const stream = await this.storageProvider.getFileStream(storage.xmlPath);
    return new StreamableFile(stream);
  }

  async getDanfeFile(
    nfeId: string,
    accountId: string,
  ): Promise<StreamableFile> {
    const storage = await this.getStorage(nfeId, accountId);

    if (!storage.danfePath) {
      throw new NotFoundException('DANFE file not found');
    }

    const stream = await this.storageProvider.getFileStream(storage.danfePath);
    return new StreamableFile(stream);
  }

  /**
   * Generate a presigned URL for downloading the XML file
   * @param nfeId The NFe document ID
   * @param accountId The account ID
   * @param expiresIn Expiration time in seconds (default: 3600)
   * @returns A presigned URL for downloading the XML file
   */
  async getXmlPresignedUrl(
    nfeId: string,
    accountId: string,
    expiresIn: number = 3600,
  ): Promise<string> {
    const storage = await this.getStorage(nfeId, accountId);

    if (!storage.xmlPath) {
      throw new NotFoundException('XML file not found');
    }

    return this.storageProvider.getPresignedUrl(storage.xmlPath, expiresIn);
  }

  /**
   * Generate a presigned URL for downloading the DANFE file
   * @param nfeId The NFe document ID
   * @param accountId The account ID
   * @param expiresIn Expiration time in seconds (default: 3600)
   * @returns A presigned URL for downloading the DANFE file
   */
  async getDanfePresignedUrl(
    nfeId: string,
    accountId: string,
    expiresIn: number = 3600,
  ): Promise<string> {
    const storage = await this.getStorage(nfeId, accountId);

    if (!storage.danfePath) {
      throw new NotFoundException('DANFE file not found');
    }

    return this.storageProvider.getPresignedUrl(storage.danfePath, expiresIn);
  }

  private async getStorage(
    nfeId: string,
    accountId: string,
  ): Promise<DocumentStorage> {
    // Get the NFe document
    const nfeDocument = await this.nfeDocumentsRepository.findOne({
      where: { id: nfeId },
      relations: ['emitter'],
    });

    if (!nfeDocument) {
      throw new NotFoundException(`NFe document with ID ${nfeId} not found`);
    }

    // Check if the emitter belongs to the account
    if (nfeDocument.emitter.accountId !== accountId) {
      throw new NotFoundException(`NFe document with ID ${nfeId} not found`);
    }

    // Get the storage record
    const storage = await this.documentStorageRepository.findOne({
      where: { nfeDocumentId: nfeId },
    });

    if (!storage) {
      throw new NotFoundException('Storage record not found');
    }

    return storage;
  }
}
