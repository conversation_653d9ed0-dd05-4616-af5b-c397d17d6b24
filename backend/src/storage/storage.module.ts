import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Emitter } from '../emitters/entities/emitter.entity';
import { NFeDocument } from '../nfe/entities/nfe-document.entity';
import { DocumentStorage } from './entities/document-storage.entity';
import { LocalStorageProvider } from './providers/local-storage.provider';
import { S3StorageProvider } from './providers/s3-storage.provider';
import { StorageController } from './storage.controller';
import { StorageService } from './storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([DocumentStorage, NFeDocument, Emitter])],
  controllers: [StorageController],
  providers: [
    StorageService,
    LocalStorageProvider,
    S3StorageProvider,
    {
      provide: 'IStorageProvider',
      useFactory: () => {
        const storageType = process.env.STORAGE_TYPE || 'local';

        if (storageType === 's3') {
          return new S3StorageProvider();
        }

        return new LocalStorageProvider();
      },
    },
  ],
  exports: [StorageService],
})
export class StorageModule {}
