import { <PERSON>, Get, Param, <PERSON>G<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Query } from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { StorageService } from './storage.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';

@ApiTags('NFe Storage')
@ApiBearerAuth()
@Controller('nfe')
@UseGuards(JwtAuthGuard)
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @ApiOperation({ summary: 'Get XML file for an NFe' })
  @ApiParam({ name: 'id', description: 'NFe document ID' })
  @ApiResponse({
    status: 200,
    description: 'XML file',
    content: {
      'application/xml': {},
    },
  })
  @ApiResponse({
    status: 404,
    description: 'NFe document or XML file not found',
  })
  @Get(':id/xml')
  @Header('Content-Type', 'application/xml')
  async getXml(@Param('id') id: string, @CurrentUser() user, @Res() res: Response) {
    const file = await this.storageService.getXmlFile(id, user.accountId);
    file.getStream().pipe(res);
  }

  @ApiOperation({ summary: 'Get DANFE file for an NFe' })
  @ApiParam({ name: 'id', description: 'NFe document ID' })
  @ApiResponse({
    status: 200,
    description: 'PDF file',
    content: {
      'application/pdf': {},
    },
  })
  @ApiResponse({
    status: 404,
    description: 'NFe document or DANFE file not found',
  })
  @Get(':id/danfe')
  @Header('Content-Type', 'application/pdf')
  async getDanfe(@Param('id') id: string, @CurrentUser() user, @Res() res: Response) {
    const file = await this.storageService.getDanfeFile(id, user.accountId);
    file.getStream().pipe(res);
  }

  @ApiOperation({ summary: 'Get presigned URL for downloading XML file' })
  @ApiParam({ name: 'id', description: 'NFe document ID' })
  @ApiQuery({
    name: 'expiresIn',
    description: 'Expiration time in seconds (default: 3600)',
    required: false,
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Presigned URL for downloading XML file',
    schema: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'Presigned URL',
          example: 'https://example.com/presigned-url',
        },
        expiresAt: {
          type: 'string',
          description: 'Expiration timestamp',
          example: '2023-01-15T12:00:00Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'NFe document or XML file not found',
  })
  @Get(':id/xml/url')
  async getXmlUrl(
    @Param('id') id: string,
    @CurrentUser() user,
    @Query('expiresIn') expiresIn?: number,
  ) {
    const url = await this.storageService.getXmlPresignedUrl(
      id,
      user.accountId,
      expiresIn ? parseInt(expiresIn.toString(), 10) : undefined,
    );
    
    return {
      url,
      expiresAt: new Date(Date.now() + (expiresIn || 3600) * 1000).toISOString(),
    };
  }

  @ApiOperation({ summary: 'Get presigned URL for downloading DANFE file' })
  @ApiParam({ name: 'id', description: 'NFe document ID' })
  @ApiQuery({
    name: 'expiresIn',
    description: 'Expiration time in seconds (default: 3600)',
    required: false,
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Presigned URL for downloading DANFE file',
    schema: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'Presigned URL',
          example: 'https://example.com/presigned-url',
        },
        expiresAt: {
          type: 'string',
          description: 'Expiration timestamp',
          example: '2023-01-15T12:00:00Z',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'NFe document or DANFE file not found',
  })
  @Get(':id/danfe/url')
  async getDanfeUrl(
    @Param('id') id: string,
    @CurrentUser() user,
    @Query('expiresIn') expiresIn?: number,
  ) {
    const url = await this.storageService.getDanfePresignedUrl(
      id,
      user.accountId,
      expiresIn ? parseInt(expiresIn.toString(), 10) : undefined,
    );
    
    return {
      url,
      expiresAt: new Date(Date.now() + (expiresIn || 3600) * 1000).toISOString(),
    };
  }
}
