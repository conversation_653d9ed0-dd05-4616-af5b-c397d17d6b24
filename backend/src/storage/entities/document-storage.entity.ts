import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { NFeDocument } from '../../nfe/entities/nfe-document.entity';

@Entity('document_storage')
export class DocumentStorage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  nfeDocumentId: string;

  @Column()
  xmlPath: string;

  @Column({ nullable: true })
  danfePath: string;

  @CreateDateColumn()
  storedAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => NFeDocument, (nfeDocument) => nfeDocument.storage, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'nfeDocumentId' })
  nfeDocument: NFeDocument;
}
