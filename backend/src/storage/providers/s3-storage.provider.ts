import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Readable } from 'stream';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { IStorageProvider } from '../interfaces/storage-provider.interface';

/**
 * AWS S3 storage provider.
 */
@Injectable()
export class S3StorageProvider implements IStorageProvider, OnModuleInit {
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly logger = new Logger(S3StorageProvider.name);

  constructor() {
    // Initialize S3 client with credentials from environment variables
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    // Get bucket name from environment variable
    this.bucketName = process.env.AWS_S3_BUCKET || 'ez-nfe-documents';
  }

  onModuleInit() {
    this.logger.log(`S3 storage provider initialized with bucket: ${this.bucketName}`);
  }

  async saveXml(nfeId: string, xml: string | Buffer): Promise<string> {
    const key = `xml/${nfeId}_${Date.now()}.xml`;
    
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      Body: xml,
      ContentType: 'application/xml',
    });
    
    await this.s3Client.send(command);
    return key;
  }

  async saveDanfe(nfeId: string, pdf: Buffer): Promise<string> {
    const key = `pdf/${nfeId}_${Date.now()}.pdf`;
    
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      Body: pdf,
      ContentType: 'application/pdf',
    });
    
    await this.s3Client.send(command);
    return key;
  }

  async getFileStream(key: string): Promise<Readable> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });
    
    const response = await this.s3Client.send(command);
    
    // Convert the response body to a readable stream
    return response.Body as Readable;
  }

  async getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });
    
    // Generate a presigned URL that expires after the specified time
    return getSignedUrl(this.s3Client, command, { expiresIn });
  }
}
