import { Injectable, OnModuleInit } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';
import * as url from 'url';
import { IStorageProvider } from '../interfaces/storage-provider.interface';

/**
 * Local filesystem storage provider.
 */
@Injectable()
export class LocalStorageProvider implements IStorageProvider, OnModuleInit {
  private readonly storagePath: string;
  private readonly baseUrl: string;

  constructor() {
    // Default to STORAGE_LOCAL_PATH or ./storage under project root
    this.storagePath =
      process.env.STORAGE_LOCAL_PATH || path.join(process.cwd(), 'storage');
    
    // Base URL for generating presigned URLs
    this.baseUrl = process.env.STORAGE_BASE_URL || 'http://localhost:3000/api/storage';
  }

  onModuleInit() {
    if (!fs.existsSync(this.storagePath)) {
      fs.mkdirSync(this.storagePath, { recursive: true });
    }
  }

  async saveXml(nfeId: string, xml: string | Buffer): Promise<string> {
    const fileName = `${nfeId}_${Date.now()}.xml`;
    const filePath = path.join(this.storagePath, fileName);
    await fs.promises.writeFile(filePath, xml);
    return filePath;
  }

  async saveDanfe(nfeId: string, pdf: Buffer): Promise<string> {
    const fileName = `${nfeId}_${Date.now()}.pdf`;
    const filePath = path.join(this.storagePath, fileName);
    await fs.promises.writeFile(filePath, pdf);
    return filePath;
  }

  async getFileStream(key: string): Promise<Readable> {
    // key is a filesystem path
    return fs.createReadStream(key);
  }

  async getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    // For local storage, we'll just return a URL to the file
    // In a real implementation, this would be a signed URL with an expiration
    
    // Extract the filename from the path
    const fileName = path.basename(key);
    
    // Create a URL with the filename as a query parameter
    const fileUrl = new URL(`${this.baseUrl}/file`);
    fileUrl.searchParams.append('key', fileName);
    fileUrl.searchParams.append('expires', (Date.now() + expiresIn * 1000).toString());
    
    // Add a simple signature to prevent tampering
    const signature = this.generateSignature(fileName, expiresIn);
    fileUrl.searchParams.append('signature', signature);
    
    return fileUrl.toString();
  }
  
  private generateSignature(fileName: string, expiresIn: number): string {
    // This is a simple implementation for demonstration purposes
    // In a real implementation, you would use a proper cryptographic signature
    const data = `${fileName}:${Date.now() + expiresIn * 1000}`;
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      hash = (hash << 5) - hash + data.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }
}
