import { Test, TestingModule } from '@nestjs/testing';
import { S3StorageProvider } from './s3-storage.provider';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'stream';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');
jest.mock('@aws-sdk/s3-request-presigner');

describe('S3StorageProvider', () => {
  let provider: S3StorageProvider;
  let s3ClientMock: jest.Mocked<S3Client>;

  beforeEach(async () => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.AWS_REGION = 'us-east-1';
    process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
    process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';
    process.env.AWS_S3_BUCKET = 'test-bucket';
    
    // Create a new instance of the provider
    provider = new S3StorageProvider();
    
    // Get the S3Client instance from the provider
    s3ClientMock = (provider as any).s3Client;
  });

  it('should be defined', () => {
    expect(provider).toBeDefined();
  });

  describe('saveXml', () => {
    it('should upload XML to S3 and return the key', async () => {
      const nfeId = 'test-nfe-id';
      const xml = '<xml>test</xml>';
      
      // Mock S3 client send method
      s3ClientMock.send = jest.fn().mockResolvedValue({});
      
      const result = await provider.saveXml(nfeId, xml);
      
      // Verify the key format
      expect(result).toMatch(/^xml\/test-nfe-id_\d+\.xml$/);
      
      // Verify S3 client was called with correct parameters
      expect(s3ClientMock.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            Bucket: 'test-bucket',
            Key: result,
            Body: xml,
            ContentType: 'application/xml',
          },
        }),
      );
    });
  });

  describe('saveDanfe', () => {
    it('should upload PDF to S3 and return the key', async () => {
      const nfeId = 'test-nfe-id';
      const pdf = Buffer.from('test pdf content');
      
      // Mock S3 client send method
      s3ClientMock.send = jest.fn().mockResolvedValue({});
      
      const result = await provider.saveDanfe(nfeId, pdf);
      
      // Verify the key format
      expect(result).toMatch(/^pdf\/test-nfe-id_\d+\.pdf$/);
      
      // Verify S3 client was called with correct parameters
      expect(s3ClientMock.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            Bucket: 'test-bucket',
            Key: result,
            Body: pdf,
            ContentType: 'application/pdf',
          },
        }),
      );
    });
  });

  describe('getFileStream', () => {
    it('should get a file stream from S3', async () => {
      const key = 'test-key';
      const mockStream = new Readable();
      
      // Mock S3 client send method
      s3ClientMock.send = jest.fn().mockResolvedValue({
        Body: mockStream,
      });
      
      const result = await provider.getFileStream(key);
      
      // Verify the result is the mock stream
      expect(result).toBe(mockStream);
      
      // Verify S3 client was called with correct parameters
      expect(s3ClientMock.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: {
            Bucket: 'test-bucket',
            Key: key,
          },
        }),
      );
    });
  });

  describe('getPresignedUrl', () => {
    it('should generate a presigned URL', async () => {
      const key = 'test-key';
      const expiresIn = 1800; // 30 minutes
      const mockPresignedUrl = 'https://example.com/presigned-url';
      
      // Mock getSignedUrl function
      (getSignedUrl as jest.Mock).mockResolvedValue(mockPresignedUrl);
      
      const result = await provider.getPresignedUrl(key, expiresIn);
      
      // Verify the result is the mock presigned URL
      expect(result).toBe(mockPresignedUrl);
      
      // Verify getSignedUrl was called with correct parameters
      expect(getSignedUrl).toHaveBeenCalledWith(
        s3ClientMock,
        expect.objectContaining({
          input: {
            Bucket: 'test-bucket',
            Key: key,
          },
        }),
        { expiresIn },
      );
    });
    
    it('should use default expiration time if not provided', async () => {
      const key = 'test-key';
      const mockPresignedUrl = 'https://example.com/presigned-url';
      
      // Mock getSignedUrl function
      (getSignedUrl as jest.Mock).mockResolvedValue(mockPresignedUrl);
      
      const result = await provider.getPresignedUrl(key);
      
      // Verify the result is the mock presigned URL
      expect(result).toBe(mockPresignedUrl);
      
      // Verify getSignedUrl was called with correct parameters
      expect(getSignedUrl).toHaveBeenCalledWith(
        s3ClientMock,
        expect.any(Object),
        { expiresIn: 3600 }, // Default is 1 hour
      );
    });
  });
});
