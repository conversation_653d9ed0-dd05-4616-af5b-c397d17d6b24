import { Module, ValidationPipe } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_PIPE } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Configuration
import { databaseConfig } from './config/database.config';
import { authConfig } from './config/auth.config';
import { appConfig } from './config/app.config';

// Controllers and Services
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Core Modules
import { AuthModule } from './modules/auth/auth.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, databaseConfig, authConfig],
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.name'),
        entities: [__dirname + '/database/entities/**/*.entity{.ts,.js}'],
        migrations: [__dirname + '/database/migrations/**/*{.ts,.js}'],
        synchronize: configService.get('database.synchronize', false),
        logging: configService.get('database.logging', false),
        ssl: configService.get('database.ssl', false),
        extra: {
          max: configService.get('database.maxConnections', 100),
          connectionTimeoutMillis: configService.get(
            'database.connectionTimeout',
            30000,
          ),
          idleTimeoutMillis: configService.get('database.idleTimeout', 30000),
        },
      }),
      inject: [ConfigService],
    }),

    // Scheduling for background tasks
    ScheduleModule.forRoot(),

    // Event emitter for application events
    EventEmitterModule.forRoot({
      // Use this instance across the entire application
      global: true,
      // Set this to `true` to use wildcards
      wildcard: false,
      // The delimiter used to segment namespaces
      delimiter: '.',
      // Set this to `true` if you want to emit the newListener event
      newListener: false,
      // Set this to `true` if you want to emit the removeListener event
      removeListener: false,
      // The maximum amount of listeners that can be assigned to an event
      maxListeners: 10,
      // Show event name in memory leak message when more than maximum amount of listeners is assigned
      verboseMemoryLeak: false,
      // Disable throwing uncaughtException if an error event is emitted and it has no listeners
      ignoreErrors: false,
    }),

    // Core Modules
    AuthModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,

    // Global validation pipe
    {
      provide: APP_PIPE,
      useFactory: () =>
        new ValidationPipe({
          whitelist: true, // Strip properties not defined in DTOs
          forbidNonWhitelisted: true, // Throw error for non-whitelisted properties
          transform: true, // Transform payloads to DTO instances
          transformOptions: {
            enableImplicitConversion: true, // Allow implicit type conversion
          },
          validateCustomDecorators: true,
          errorHttpStatusCode: 422, // Use 422 for validation errors
        }),
    },
  ],
})
export class AppModule {}
