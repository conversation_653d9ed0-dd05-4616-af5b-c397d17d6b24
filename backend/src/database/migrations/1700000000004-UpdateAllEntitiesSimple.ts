import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAllEntitiesSimple1700000000004
  implements MigrationInterface
{
  name = 'UpdateAllEntitiesSimple1700000000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. Create new enum types (only if they don't exist)
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "public"."approval_status_enum" AS ENUM('pending', 'approved', 'rejected', 'not_required');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "public"."tax_origin_enum" AS ENUM('nacional', 'estrangeira', 'nacional_mercosul');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "public"."payment_method_enum" AS ENUM('01', '02', '03', '04', '05', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '90', '99');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "public"."payment_timing_enum" AS ENUM('0', '1');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "public"."payment_status_enum" AS ENUM('pending', 'paid', 'failed', 'overdue', 'refunded');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // 2. Add new enum values to existing enums (ignore errors if they exist)
    await queryRunner.query(
      `ALTER TYPE "public"."invoice_status_enum" ADD VALUE 'sent_to_sefaz';`,
    );

    await queryRunner.query(
      `ALTER TYPE "public"."invoice_status_enum" ADD VALUE 'authorized';`,
    );

    await queryRunner.query(
      `ALTER TYPE "public"."invoice_status_enum" ADD VALUE 'denied';`,
    );

    // 3. Add columns to accounts table
    await queryRunner.query(
      `ALTER TABLE "accounts" ADD COLUMN IF NOT EXISTS "slug" character varying(100);`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" ADD COLUMN IF NOT EXISTS "email" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" ADD COLUMN IF NOT EXISTS "phone" character varying(20);`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" ADD COLUMN IF NOT EXISTS "website" character varying(255);`,
    );

    // 4. Add columns to business_units table
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "tradeName" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "legalName" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "street" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "number" character varying(20);`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "city" character varying(100);`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "state" character(2);`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" ADD COLUMN IF NOT EXISTS "zipCode" character varying(9);`,
    );

    // 5. Add columns to clients table
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "tradeName" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "legalName" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "street" character varying(255);`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "number" character varying(20);`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "city" character varying(100);`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "state" character(2);`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" ADD COLUMN IF NOT EXISTS "zipCode" character varying(9);`,
    );

    // 6. Add columns to products table
    await queryRunner.query(
      `ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "taxOrigin" "tax_origin_enum" DEFAULT 'nacional';`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "icmsCst" character varying(3);`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "icmsRate" numeric(5,4) DEFAULT 0;`,
    );

    // 7. Add columns to invoices table
    await queryRunner.query(
      `ALTER TABLE "invoices" ADD COLUMN IF NOT EXISTS "approvalStatus" "approval_status_enum" DEFAULT 'not_required';`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" ADD COLUMN IF NOT EXISTS "sentToSefazAt" timestamp;`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" ADD COLUMN IF NOT EXISTS "authorizedAt" timestamp;`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" ADD COLUMN IF NOT EXISTS "paymentMethod" "payment_method_enum" DEFAULT '01';`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" ADD COLUMN IF NOT EXISTS "paymentTiming" "payment_timing_enum" DEFAULT '0';`,
    );

    // 8. Add columns to subscriptions table
    await queryRunner.query(
      `ALTER TABLE "subscriptions" ADD COLUMN IF NOT EXISTS "monthlyPrice" numeric(10,2) DEFAULT 0;`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" ADD COLUMN IF NOT EXISTS "currentPrice" numeric(10,2) DEFAULT 0;`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" ADD COLUMN IF NOT EXISTS "paymentStatus" "payment_status_enum" DEFAULT 'pending';`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1. Remove columns from subscriptions table
    await queryRunner.query(
      `ALTER TABLE "subscriptions" DROP COLUMN IF EXISTS "paymentStatus";`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" DROP COLUMN IF EXISTS "currentPrice";`,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" DROP COLUMN IF EXISTS "monthlyPrice";`,
    );

    // 2. Remove columns from invoices table
    await queryRunner.query(
      `ALTER TABLE "invoices" DROP COLUMN IF EXISTS "paymentTiming";`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" DROP COLUMN IF EXISTS "paymentMethod";`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" DROP COLUMN IF EXISTS "authorizedAt";`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" DROP COLUMN IF EXISTS "sentToSefazAt";`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoices" DROP COLUMN IF EXISTS "approvalStatus";`,
    );

    // 3. Remove columns from products table
    await queryRunner.query(
      `ALTER TABLE "products" DROP COLUMN IF EXISTS "icmsRate";`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" DROP COLUMN IF EXISTS "icmsCst";`,
    );
    await queryRunner.query(
      `ALTER TABLE "products" DROP COLUMN IF EXISTS "taxOrigin";`,
    );

    // 4. Remove columns from clients table
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "zipCode";`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "state";`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "city";`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "number";`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "street";`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "legalName";`,
    );
    await queryRunner.query(
      `ALTER TABLE "clients" DROP COLUMN IF EXISTS "tradeName";`,
    );

    // 5. Remove columns from business_units table
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "zipCode";`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "state";`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "city";`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "number";`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "street";`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "legalName";`,
    );
    await queryRunner.query(
      `ALTER TABLE "business_units" DROP COLUMN IF EXISTS "tradeName";`,
    );

    // 6. Remove columns from accounts table
    await queryRunner.query(
      `ALTER TABLE "accounts" DROP COLUMN IF EXISTS "website";`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" DROP COLUMN IF EXISTS "phone";`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" DROP COLUMN IF EXISTS "email";`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" DROP COLUMN IF EXISTS "slug";`,
    );

    // 7. Drop enum types (only if no tables are using them)
    await queryRunner.query(`
      DO $$ BEGIN
        DROP TYPE IF EXISTS "public"."payment_status_enum";
      EXCEPTION
        WHEN others THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        DROP TYPE IF EXISTS "public"."payment_timing_enum";
      EXCEPTION
        WHEN others THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        DROP TYPE IF EXISTS "public"."payment_method_enum";
      EXCEPTION
        WHEN others THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        DROP TYPE IF EXISTS "public"."tax_origin_enum";
      EXCEPTION
        WHEN others THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        DROP TYPE IF EXISTS "public"."approval_status_enum";
      EXCEPTION
        WHEN others THEN null;
      END $$;
    `);

    // Note: Cannot remove enum values added to existing invoice_status_enum
    // as PostgreSQL doesn't support removing enum values directly
    console.log(
      'Warning: Enum values added to invoice_status_enum cannot be automatically removed',
    );
  }
}
