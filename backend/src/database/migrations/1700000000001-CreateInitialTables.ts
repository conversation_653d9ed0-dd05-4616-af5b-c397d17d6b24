import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialTables1700********* implements MigrationInterface {
  name = 'CreateInitialTables1700*********';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enums
    await queryRunner.query(`
      CREATE TYPE "public"."user_role_enum" AS ENUM('owner', 'admin', 'member', 'viewer')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "public"."user_status_enum" AS ENUM('active', 'inactive', 'suspended', 'pending_verification')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."account_status_enum" AS ENUM('active', 'suspended', 'cancelled', 'trial')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."subscription_tier_enum" AS ENUM('free', 'basic', 'professional', 'enterprise')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."subscription_status_enum" AS ENUM('active', 'cancelled', 'expired', 'suspended', 'trialing')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."subscription_billing_cycle_enum" AS ENUM('monthly', 'quarterly', 'yearly')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."business_unit_status_enum" AS ENUM('active', 'inactive', 'suspended')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."company_type_enum" AS ENUM('mei', 'ltda', 'sa', 'eireli', 'ei', 'other')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."invoice_status_enum" AS ENUM('draft', 'pending_approval', 'approved', 'issued', 'sent', 'received', 'cancelled', 'error')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."invoice_type_enum" AS ENUM('nfe', 'nfce', 'nfse')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."certificate_status_enum" AS ENUM('active', 'expired', 'revoked', 'suspended', 'pending')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."certificate_type_enum" AS ENUM('a1', 'a3')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."api_key_status_enum" AS ENUM('active', 'suspended', 'revoked')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."audit_action_enum" AS ENUM('create', 'update', 'delete', 'view', 'login', 'logout', 'export', 'import', 'approve', 'reject', 'cancel', 'send', 'download', 'upload', 'generate', 'revoke', 'suspend', 'reactivate')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."audit_entity_type_enum" AS ENUM('user', 'account', 'business_unit', 'client', 'product', 'carrier', 'payment_term', 'invoice', 'invoice_item', 'certificate', 'subscription', 'api_key', 'system')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."audit_severity_enum" AS ENUM('low', 'medium', 'high', 'critical')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."audit_category_enum" AS ENUM('security', 'data', 'financial', 'compliance', 'system', 'user_action', 'api', 'integration')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."notification_type_enum" AS ENUM('info', 'success', 'warning', 'error', 'critical')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."notification_category_enum" AS ENUM('system', 'invoice', 'certificate', 'subscription', 'payment', 'compliance', 'security', 'integration', 'user_action', 'maintenance')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."notification_channel_enum" AS ENUM('in_app', 'email', 'sms', 'push', 'webhook', 'slack')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."notification_priority_enum" AS ENUM('low', 'normal', 'high', 'urgent')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."notification_status_enum" AS ENUM('pending', 'sent', 'delivered', 'read', 'failed', 'cancelled')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."cnpj_status_enum" AS ENUM('active', 'suspended', 'cancelled', 'null', 'invalid')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."company_size_enum" AS ENUM('mei', 'me', 'epp', 'medium', 'large')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."tax_regime_enum" AS ENUM('simples_nacional', 'lucro_presumido', 'lucro_real', 'lucro_arbitrado', 'isento')
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."cep_status_enum" AS ENUM('valid', 'invalid', 'not_found', 'error')
    `);

    // Create accounts table
    await queryRunner.query(`
      CREATE TABLE "accounts" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "cnpj" character varying(14),
        "status" "account_status_enum" NOT NULL DEFAULT 'trial',
        "trialEndsAt" TIMESTAMP,
        "settings" text,
        "metadata" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_accounts" PRIMARY KEY ("id")
      )
    `);

    // Create subscriptions table
    await queryRunner.query(`
      CREATE TABLE "subscriptions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "tier" "subscription_tier_enum" NOT NULL DEFAULT 'free',
        "status" "subscription_status_enum" NOT NULL DEFAULT 'trialing',
        "billingCycle" "subscription_billing_cycle_enum" NOT NULL DEFAULT 'monthly',
        "price" numeric(10,2) NOT NULL DEFAULT 0,
        "discount" numeric(5,2) NOT NULL DEFAULT 0,
        "trialDays" integer NOT NULL DEFAULT 14,
        "trialStartedAt" TIMESTAMP,
        "trialEndsAt" TIMESTAMP,
        "currentPeriodStart" TIMESTAMP,
        "currentPeriodEnd" TIMESTAMP,
        "cancelledAt" TIMESTAMP,
        "cancelAtPeriodEnd" boolean NOT NULL DEFAULT false,
        "features" text,
        "limits" text,
        "usageCurrent" text,
        "usageReset" text,
        "paymentMethod" character varying(100),
        "stripeSubscriptionId" character varying(255),
        "stripeCustomerId" character varying(255),
        "accountId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_subscriptions" PRIMARY KEY ("id")
      )
    `);

    // Create business_units table
    await queryRunner.query(`
      CREATE TABLE "business_units" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "cnpj" character varying(14) NOT NULL,
        "stateRegistration" character varying(20),
        "municipalRegistration" character varying(20),
        "companyType" "company_type_enum" NOT NULL DEFAULT 'ltda',
        "status" "business_unit_status_enum" NOT NULL DEFAULT 'active',
        "isDefault" boolean NOT NULL DEFAULT false,
        "address" text,
        "phone" character varying(20),
        "email" character varying(255),
        "website" character varying(255),
        "taxConfiguration" text,
        "nfeConfiguration" text,
        "accountId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_business_units" PRIMARY KEY ("id")
      )
    `);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "email" character varying(255) NOT NULL,
        "password" character varying(255) NOT NULL,
        "firstName" character varying(100) NOT NULL,
        "lastName" character varying(100) NOT NULL,
        "phone" character varying(20),
        "role" "user_role_enum" NOT NULL DEFAULT 'member',
        "status" "user_status_enum" NOT NULL DEFAULT 'pending_verification',
        "refreshToken" character varying(255),
        "refreshTokenExpiresAt" TIMESTAMP,
        "emailVerificationToken" character varying(255),
        "emailVerifiedAt" TIMESTAMP,
        "passwordResetToken" character varying(255),
        "passwordResetExpiresAt" TIMESTAMP,
        "lastLoginAt" TIMESTAMP,
        "lastLoginIp" character varying(45),
        "loginAttempts" integer NOT NULL DEFAULT 0,
        "lockedAt" TIMESTAMP,
        "accountId" uuid NOT NULL,
        "currentBusinessUnitId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_users" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_users_email" UNIQUE ("email")
      )
    `);

    // Create clients table
    await queryRunner.query(`
      CREATE TABLE "clients" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "document" character varying(18) NOT NULL,
        "documentType" character varying(10) NOT NULL,
        "email" character varying(255),
        "phone" character varying(20),
        "address" text,
        "isActive" boolean NOT NULL DEFAULT true,
        "tags" text,
        "notes" text,
        "metadata" text,
        "accountId" uuid NOT NULL,
        "businessUnitId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_clients" PRIMARY KEY ("id")
      )
    `);

    // Create products table
    await queryRunner.query(`
      CREATE TABLE "products" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "description" text,
        "sku" character varying(100),
        "ean" character varying(14),
        "ncm" character varying(8) NOT NULL,
        "cfop" character varying(4) NOT NULL,
        "unitOfMeasure" character varying(10) NOT NULL,
        "price" numeric(10,4) NOT NULL,
        "costPrice" numeric(10,4),
        "weight" numeric(8,3),
        "dimensions" text,
        "isActive" boolean NOT NULL DEFAULT true,
        "taxConfiguration" text,
        "tags" text,
        "metadata" text,
        "accountId" uuid NOT NULL,
        "businessUnitId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_products" PRIMARY KEY ("id")
      )
    `);

    // Create carriers table
    await queryRunner.query(`
      CREATE TABLE "carriers" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "document" character varying(18) NOT NULL,
        "documentType" character varying(10) NOT NULL,
        "stateRegistration" character varying(20),
        "address" text,
        "phone" character varying(20),
        "email" character varying(255),
        "isActive" boolean NOT NULL DEFAULT true,
        "metadata" text,
        "accountId" uuid NOT NULL,
        "businessUnitId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_carriers" PRIMARY KEY ("id")
      )
    `);

    // Create payment_terms table
    await queryRunner.query(`
      CREATE TABLE "payment_terms" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "description" text,
        "installments" integer NOT NULL DEFAULT 1,
        "intervalDays" integer NOT NULL DEFAULT 30,
        "firstInstallmentDays" integer NOT NULL DEFAULT 0,
        "discountPercentage" numeric(5,2) NOT NULL DEFAULT 0,
        "finePercentage" numeric(5,2) NOT NULL DEFAULT 0,
        "interestPercentage" numeric(5,2) NOT NULL DEFAULT 0,
        "isDefault" boolean NOT NULL DEFAULT false,
        "isActive" boolean NOT NULL DEFAULT true,
        "accountId" uuid NOT NULL,
        "businessUnitId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_payment_terms" PRIMARY KEY ("id")
      )
    `);

    // Create certificates table
    await queryRunner.query(`
      CREATE TABLE "certificates" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "type" "certificate_type_enum" NOT NULL,
        "serialNumber" character varying(255) NOT NULL,
        "issuer" character varying(255) NOT NULL,
        "subject" character varying(255) NOT NULL,
        "validFrom" TIMESTAMP NOT NULL,
        "validUntil" TIMESTAMP NOT NULL,
        "status" "certificate_status_enum" NOT NULL DEFAULT 'active',
        "thumbprint" character varying(255),
        "publicKey" text,
        "privateKeyPath" character varying(500),
        "password" character varying(255),
        "isInstalled" boolean NOT NULL DEFAULT false,
        "installationPath" character varying(500),
        "lastUsedAt" TIMESTAMP,
        "usageCount" integer NOT NULL DEFAULT 0,
        "revokedAt" TIMESTAMP,
        "revokedReason" character varying(255),
        "backupPath" character varying(500),
        "backupCreatedAt" TIMESTAMP,
        "alertDaysBefore" integer NOT NULL DEFAULT 30,
        "isAlertEnabled" boolean NOT NULL DEFAULT true,
        "businessUnitId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_certificates" PRIMARY KEY ("id")
      )
    `);

    // Create api_keys table
    await queryRunner.query(`
      CREATE TABLE "api_keys" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "keyPrefix" character varying(20) NOT NULL,
        "hashedKey" character varying(255) NOT NULL,
        "status" "api_key_status_enum" NOT NULL DEFAULT 'active',
        "scopes" text,
        "rateLimit" integer NOT NULL DEFAULT 1000,
        "rateLimitWindow" integer NOT NULL DEFAULT 3600,
        "allowedIps" text,
        "allowedOrigins" text,
        "expiresAt" TIMESTAMP,
        "lastUsedAt" TIMESTAMP,
        "usageCount" integer NOT NULL DEFAULT 0,
        "currentUsage" integer NOT NULL DEFAULT 0,
        "usageResetAt" TIMESTAMP,
        "revokedAt" TIMESTAMP,
        "revokedReason" character varying(255),
        "suspendedAt" TIMESTAMP,
        "suspendedReason" character varying(255),
        "metadata" text,
        "accountId" uuid NOT NULL,
        "createdByUserId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_api_keys" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_api_keys_keyPrefix" UNIQUE ("keyPrefix")
      )
    `);

    // Create invoices table
    await queryRunner.query(`
      CREATE TABLE "invoices" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "number" integer,
        "series" integer NOT NULL DEFAULT 1,
        "type" "invoice_type_enum" NOT NULL DEFAULT 'nfe',
        "status" "invoice_status_enum" NOT NULL DEFAULT 'draft',
        "issueDate" TIMESTAMP,
        "dueDate" TIMESTAMP,
        "accessKey" character varying(44),
        "authorizationCode" character varying(255),
        "protocolNumber" character varying(255),
        "qrCode" text,
        "xmlContent" text,
        "xmlSigned" text,
        "pdfPath" character varying(500),
        "subtotal" numeric(12,4) NOT NULL DEFAULT 0,
        "discountAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "shippingAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "insuranceAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "otherExpensesAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "totalTaxes" numeric(12,4) NOT NULL DEFAULT 0,
        "totalAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "icmsTotal" numeric(12,4) NOT NULL DEFAULT 0,
        "icmsStTotal" numeric(12,4) NOT NULL DEFAULT 0,
        "ipiTotal" numeric(12,4) NOT NULL DEFAULT 0,
        "pisTotal" numeric(12,4) NOT NULL DEFAULT 0,
        "cofinsTotal" numeric(12,4) NOT NULL DEFAULT 0,
        "issTotal" numeric(12,4) NOT NULL DEFAULT 0,
        "additionalInformation" text,
        "fiscalObservation" text,
        "transportMode" integer,
        "shippingCompanyDocument" character varying(18),
        "shippingCompanyName" character varying(255),
        "vehiclePlate" character varying(10),
        "vehicleState" character varying(2),
        "shippingAddress" text,
        "grossWeight" numeric(8,3),
        "netWeight" numeric(8,3),
        "volumesQuantity" integer,
        "volumesType" character varying(100),
        "paymentMethod" integer,
        "installments" integer,
        "approvedAt" TIMESTAMP,
        "approvedByUserId" uuid,
        "rejectedAt" TIMESTAMP,
        "rejectedByUserId" uuid,
        "rejectionReason" text,
        "sentAt" TIMESTAMP,
        "sentByUserId" uuid,
        "receivedAt" TIMESTAMP,
        "cancelledAt" TIMESTAMP,
        "cancelledByUserId" uuid,
        "cancellationReason" text,
        "cancellationProtocol" character varying(255),
        "errors" text,
        "warnings" text,
        "metadata" text,
        "accountId" uuid NOT NULL,
        "businessUnitId" uuid NOT NULL,
        "clientId" uuid NOT NULL,
        "carrierId" uuid,
        "paymentTermId" uuid,
        "createdByUserId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_invoices" PRIMARY KEY ("id")
      )
    `);

    // Create invoice_items table
    await queryRunner.query(`
      CREATE TABLE "invoice_items" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "sequence" integer NOT NULL,
        "itemCode" character varying(100),
        "description" character varying(500) NOT NULL,
        "ncm" character varying(8) NOT NULL,
        "cfop" character varying(4) NOT NULL,
        "unitOfMeasure" character varying(10) NOT NULL,
        "quantity" numeric(12,4) NOT NULL,
        "unitPrice" numeric(12,4) NOT NULL,
        "totalPrice" numeric(12,4) NOT NULL,
        "discount" numeric(12,4) NOT NULL DEFAULT 0,
        "grossWeight" numeric(8,3),
        "netWeight" numeric(8,3),
        "ean" character varying(14),
        "eanTributary" character varying(14),
        "icmsOrigin" integer NOT NULL DEFAULT 0,
        "icmsCst" character varying(3),
        "icmsPercentage" numeric(5,2),
        "icmsAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "icmsStPercentage" numeric(5,2),
        "icmsStAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "ipiCst" character varying(3),
        "ipiPercentage" numeric(5,2),
        "ipiAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "pisCst" character varying(2),
        "pisPercentage" numeric(5,4),
        "pisAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "cofinsCst" character varying(2),
        "cofinsPercentage" numeric(5,4),
        "cofinsAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "issPercentage" numeric(5,2),
        "issAmount" numeric(12,4) NOT NULL DEFAULT 0,
        "additionalInformation" text,
        "importDeclaration" text,
        "fuelData" text,
        "invoiceId" uuid NOT NULL,
        "productId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_invoice_items" PRIMARY KEY ("id")
      )
    `);

    // Create audit_logs table
    await queryRunner.query(`
      CREATE TABLE "audit_logs" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "action" "audit_action_enum" NOT NULL,
        "entityType" "audit_entity_type_enum" NOT NULL,
        "entityId" uuid NOT NULL,
        "entityName" character varying(255),
        "severity" "audit_severity_enum" NOT NULL DEFAULT 'low',
        "category" "audit_category_enum" NOT NULL DEFAULT 'user_action',
        "description" character varying(500) NOT NULL,
        "details" text,
        "oldValues" text,
        "newValues" text,
        "ipAddress" character varying(45),
        "userAgent" character varying(255),
        "sessionId" character varying(100),
        "requestId" character varying(255),
        "apiEndpoint" character varying(100),
        "httpMethod" character varying(10),
        "httpStatusCode" integer,
        "country" character varying(100),
        "city" character varying(100),
        "timezone" character varying(50),
        "isSuspicious" boolean NOT NULL DEFAULT false,
        "riskReason" character varying(255),
        "riskScore" integer NOT NULL DEFAULT 0,
        "isComplianceRelevant" boolean NOT NULL DEFAULT false,
        "complianceCategory" character varying(100),
        "requiresRetention" boolean NOT NULL DEFAULT false,
        "retainUntil" TIMESTAMP,
        "applicationVersion" character varying(50),
        "environment" character varying(50),
        "module" character varying(100),
        "feature" character varying(100),
        "errorCode" character varying(255),
        "errorMessage" text,
        "stackTrace" text,
        "accountId" uuid,
        "businessUnitId" uuid,
        "userId" uuid,
        "performedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_audit_logs" PRIMARY KEY ("id")
      )
    `);

    // Create notifications table
    await queryRunner.query(`
      CREATE TABLE "notifications" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255) NOT NULL,
        "message" text NOT NULL,
        "description" text,
        "type" "notification_type_enum" NOT NULL,
        "category" "notification_category_enum" NOT NULL,
        "priority" "notification_priority_enum" NOT NULL DEFAULT 'normal',
        "channels" "notification_channel_enum" array NOT NULL DEFAULT '{in_app}',
        "status" "notification_status_enum" NOT NULL DEFAULT 'pending',
        "scheduledFor" TIMESTAMP,
        "sentAt" TIMESTAMP,
        "deliveredAt" TIMESTAMP,
        "readAt" TIMESTAMP,
        "isRead" boolean NOT NULL DEFAULT false,
        "isArchived" boolean NOT NULL DEFAULT false,
        "isPinned" boolean NOT NULL DEFAULT false,
        "actionUrl" character varying(255),
        "actionLabel" character varying(100),
        "actionData" text,
        "icon" character varying(100),
        "color" character varying(50),
        "metadata" text,
        "sourceSystem" character varying(100),
        "sourceModule" character varying(100),
        "sourceEntityType" character varying(255),
        "sourceEntityId" character varying(255),
        "correlationId" character varying(255),
        "threadId" character varying(255),
        "retryCount" integer NOT NULL DEFAULT 0,
        "maxRetries" integer NOT NULL DEFAULT 3,
        "nextRetryAt" TIMESTAMP,
        "deliveryErrors" text,
        "deliveryReceipts" text,
        "expiresAt" TIMESTAMP,
        "autoArchiveOnRead" boolean NOT NULL DEFAULT false,
        "autoDeleteOnExpiry" boolean NOT NULL DEFAULT false,
        "templateId" character varying(100),
        "templateVariables" text,
        "language" character varying(10) NOT NULL DEFAULT 'pt-BR',
        "accountId" uuid,
        "businessUnitId" uuid,
        "userId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_notifications" PRIMARY KEY ("id")
      )
    `);

    // Create cnpj_validations table
    await queryRunner.query(`
      CREATE TABLE "cnpj_validations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "cnpj" character varying(14) NOT NULL,
        "formattedCNPJ" character varying(18) NOT NULL,
        "isValid" boolean NOT NULL DEFAULT true,
        "status" "cnpj_status_enum",
        "companyName" character varying(255),
        "tradeName" character varying(255),
        "companyType" "company_type_enum",
        "companySize" "company_size_enum",
        "taxRegime" "tax_regime_enum",
        "legalRepresentativeName" character varying(255),
        "legalRepresentativeCPF" character varying(11),
        "registrationDate" date,
        "lastUpdateDate" date,
        "zipCode" character varying(8),
        "street" character varying(255),
        "number" character varying(50),
        "complement" character varying(100),
        "neighborhood" character varying(100),
        "city" character varying(100),
        "state" character varying(2),
        "primaryCNAE" character varying(10),
        "primaryCNAEDescription" character varying(255),
        "secondaryCNAEs" text,
        "phone" character varying(20),
        "email" character varying(255),
        "shareCapital" numeric(15,2),
        "federalTaxSituation" character varying(100),
        "federalTaxSituationDate" date,
        "federalTaxSituationReason" character varying(255),
        "isOptingSimples" boolean NOT NULL DEFAULT false,
        "simplesOptingDate" date,
        "simplesExclusionDate" date,
        "isOptingMEI" boolean NOT NULL DEFAULT false,
        "meiOptingDate" date,
        "validationSource" character varying(100),
        "validatedAt" TIMESTAMP,
        "expiresAt" TIMESTAMP,
        "needsRevalidation" boolean NOT NULL DEFAULT false,
        "validationErrors" text,
        "rawData" text,
        "validationAttempts" integer NOT NULL DEFAULT 0,
        "lastValidationAttempt" TIMESTAMP,
        "accountId" uuid,
        "businessUnitId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_cnpj_validations" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_cnpj_validations_cnpj" UNIQUE ("cnpj")
      )
    `);

    // Create cep_validations table
    await queryRunner.query(`
      CREATE TABLE "cep_validations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "cep" character varying(8) NOT NULL,
        "formattedCEP" character varying(9) NOT NULL,
        "isValid" boolean NOT NULL DEFAULT true,
        "status" "cep_status_enum" NOT NULL DEFAULT 'valid',
        "street" character varying(255),
        "complement" character varying(255),
        "neighborhood" character varying(100),
        "city" character varying(100) NOT NULL,
        "state" character varying(2) NOT NULL,
        "ibgeCode" character varying(10),
        "giaCode" character varying(4),
        "dddCode" character varying(4),
        "siafi" character varying(10),
        "latitude" numeric(10,8),
        "longitude" numeric(11,8),
        "region" character varying(50),
        "timezone" character varying(100),
        "hasPostalService" boolean NOT NULL DEFAULT true,
        "hasDeliveryService" boolean NOT NULL DEFAULT true,
        "serviceRestrictions" text,
        "validationSource" character varying(100),
        "validatedAt" TIMESTAMP,
        "expiresAt" TIMESTAMP,
        "needsRevalidation" boolean NOT NULL DEFAULT false,
        "validationErrors" text,
        "rawData" text,
        "validationAttempts" integer NOT NULL DEFAULT 0,
        "lastValidationAttempt" TIMESTAMP,
        "usageCount" integer NOT NULL DEFAULT 0,
        "lastUsedAt" TIMESTAMP,
        "confidenceScore" integer NOT NULL DEFAULT 100,
        "dataQuality" character varying(50),
        "isApproximateLocation" boolean NOT NULL DEFAULT false,
        "accountId" uuid,
        "businessUnitId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_cep_validations" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_cep_validations_cep" UNIQUE ("cep")
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_accounts_status" ON "accounts" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_accounts_cnpj" ON "accounts" ("cnpj")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_subscriptions_accountId" ON "subscriptions" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_subscriptions_status" ON "subscriptions" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_subscriptions_tier" ON "subscriptions" ("tier")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_business_units_accountId" ON "business_units" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_business_units_cnpj" ON "business_units" ("cnpj")`);
    await queryRunner.query(`CREATE INDEX "IDX_business_units_status" ON "business_units" ("status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_users_accountId" ON "users" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_status" ON "users" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_currentBusinessUnitId" ON "users" ("currentBusinessUnitId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_clients_accountId" ON "clients" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_clients_businessUnitId" ON "clients" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_clients_document" ON "clients" ("document")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_products_accountId" ON "products" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_products_businessUnitId" ON "products" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_products_sku" ON "products" ("sku")`);
    await queryRunner.query(`CREATE INDEX "IDX_products_ncm" ON "products" ("ncm")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_carriers_accountId" ON "carriers" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_carriers_businessUnitId" ON "carriers" ("businessUnitId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_payment_terms_accountId" ON "payment_terms" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_payment_terms_businessUnitId" ON "payment_terms" ("businessUnitId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_certificates_businessUnitId" ON "certificates" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_certificates_status" ON "certificates" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_certificates_validUntil" ON "certificates" ("validUntil")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_api_keys_accountId" ON "api_keys" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_api_keys_status" ON "api_keys" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_api_keys_createdByUserId" ON "api_keys" ("createdByUserId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_invoices_accountId" ON "invoices" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_invoices_businessUnitId" ON "invoices" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_invoices_clientId" ON "invoices" ("clientId")`);
    await queryRunner.query(`CREATE INDEX "IDX_invoices_status" ON "invoices" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_invoices_type" ON "invoices" ("type")`);
    await queryRunner.query(`CREATE INDEX "IDX_invoices_number_series" ON "invoices" ("number", "series")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_invoice_items_invoiceId" ON "invoice_items" ("invoiceId")`);
    await queryRunner.query(`CREATE INDEX "IDX_invoice_items_productId" ON "invoice_items" ("productId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_accountId" ON "audit_logs" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_businessUnitId" ON "audit_logs" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_userId" ON "audit_logs" ("userId")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_action" ON "audit_logs" ("action")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_entityType" ON "audit_logs" ("entityType")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_severity" ON "audit_logs" ("severity")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_category" ON "audit_logs" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_performedAt" ON "audit_logs" ("performedAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_audit_logs_entityId" ON "audit_logs" ("entityId")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_notifications_accountId" ON "notifications" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_businessUnitId" ON "notifications" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_userId" ON "notifications" ("userId")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_type" ON "notifications" ("type")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_category" ON "notifications" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_status" ON "notifications" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_priority" ON "notifications" ("priority")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_scheduledFor" ON "notifications" ("scheduledFor")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_createdAt" ON "notifications" ("createdAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_notifications_isRead" ON "notifications" ("isRead")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_cnpj_validations_accountId" ON "cnpj_validations" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_cnpj_validations_businessUnitId" ON "cnpj_validations" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_cnpj_validations_status" ON "cnpj_validations" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_cnpj_validations_validatedAt" ON "cnpj_validations" ("validatedAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_cnpj_validations_expiresAt" ON "cnpj_validations" ("expiresAt")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_accountId" ON "cep_validations" ("accountId")`);
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_businessUnitId" ON "cep_validations" ("businessUnitId")`);
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_status" ON "cep_validations" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_validatedAt" ON "cep_validations" ("validatedAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_expiresAt" ON "cep_validations" ("expiresAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_state" ON "cep_validations" ("state")`);
    await queryRunner.query(`CREATE INDEX "IDX_cep_validations_city" ON "cep_validations" ("city")`);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "business_units" ADD CONSTRAINT "FK_business_units_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "users" ADD CONSTRAINT "FK_users_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "users" ADD CONSTRAINT "FK_users_currentBusinessUnitId" 
      FOREIGN KEY ("currentBusinessUnitId") REFERENCES "business_units"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "clients" ADD CONSTRAINT "FK_clients_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "clients" ADD CONSTRAINT "FK_clients_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "products" ADD CONSTRAINT "FK_products_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "products" ADD CONSTRAINT "FK_products_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "carriers" ADD CONSTRAINT "FK_carriers_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "carriers" ADD CONSTRAINT "FK_carriers_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "payment_terms" ADD CONSTRAINT "FK_payment_terms_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "payment_terms" ADD CONSTRAINT "FK_payment_terms_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "certificates" ADD CONSTRAINT "FK_certificates_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "api_keys" ADD CONSTRAINT "FK_api_keys_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "api_keys" ADD CONSTRAINT "FK_api_keys_createdByUserId" 
      FOREIGN KEY ("createdByUserId") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_clientId" 
      FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE RESTRICT
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_carrierId" 
      FOREIGN KEY ("carrierId") REFERENCES "carriers"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_paymentTermId" 
      FOREIGN KEY ("paymentTermId") REFERENCES "payment_terms"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_createdByUserId" 
      FOREIGN KEY ("createdByUserId") REFERENCES "users"("id") ON DELETE RESTRICT
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_approvedByUserId" 
      FOREIGN KEY ("approvedByUserId") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_rejectedByUserId" 
      FOREIGN KEY ("rejectedByUserId") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_sentByUserId" 
      FOREIGN KEY ("sentByUserId") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_cancelledByUserId" 
      FOREIGN KEY ("cancelledByUserId") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "invoice_items" ADD CONSTRAINT "FK_invoice_items_invoiceId" 
      FOREIGN KEY ("invoiceId") REFERENCES "invoices"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "invoice_items" ADD CONSTRAINT "FK_invoice_items_productId" 
      FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "audit_logs" ADD CONSTRAINT "FK_audit_logs_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "audit_logs" ADD CONSTRAINT "FK_audit_logs_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "audit_logs" ADD CONSTRAINT "FK_audit_logs_userId" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "notifications" ADD CONSTRAINT "FK_notifications_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "notifications" ADD CONSTRAINT "FK_notifications_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "notifications" ADD CONSTRAINT "FK_notifications_userId" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "cnpj_validations" ADD CONSTRAINT "FK_cnpj_validations_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "cnpj_validations" ADD CONSTRAINT "FK_cnpj_validations_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "cep_validations" ADD CONSTRAINT "FK_cep_validations_accountId" 
      FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "cep_validations" ADD CONSTRAINT "FK_cep_validations_businessUnitId" 
      FOREIGN KEY ("businessUnitId") REFERENCES "business_units"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "cep_validations" DROP CONSTRAINT "FK_cep_validations_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "cep_validations" DROP CONSTRAINT "FK_cep_validations_accountId"`);
    await queryRunner.query(`ALTER TABLE "cnpj_validations" DROP CONSTRAINT "FK_cnpj_validations_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "cnpj_validations" DROP CONSTRAINT "FK_cnpj_validations_accountId"`);
    await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_notifications_userId"`);
    await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_notifications_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_notifications_accountId"`);
    await queryRunner.query(`ALTER TABLE "audit_logs" DROP CONSTRAINT "FK_audit_logs_userId"`);
    await queryRunner.query(`ALTER TABLE "audit_logs" DROP CONSTRAINT "FK_audit_logs_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "audit_logs" DROP CONSTRAINT "FK_audit_logs_accountId"`);
    await queryRunner.query(`ALTER TABLE "invoice_items" DROP CONSTRAINT "FK_invoice_items_productId"`);
    await queryRunner.query(`ALTER TABLE "invoice_items" DROP CONSTRAINT "FK_invoice_items_invoiceId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_cancelledByUserId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_sentByUserId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_rejectedByUserId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_approvedByUserId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_createdByUserId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_paymentTermId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_carrierId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_clientId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_accountId"`);
    await queryRunner.query(`ALTER TABLE "api_keys" DROP CONSTRAINT "FK_api_keys_createdByUserId"`);
    await queryRunner.query(`ALTER TABLE "api_keys" DROP CONSTRAINT "FK_api_keys_accountId"`);
    await queryRunner.query(`ALTER TABLE "certificates" DROP CONSTRAINT "FK_certificates_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "payment_terms" DROP CONSTRAINT "FK_payment_terms_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "payment_terms" DROP CONSTRAINT "FK_payment_terms_accountId"`);
    await queryRunner.query(`ALTER TABLE "carriers" DROP CONSTRAINT "FK_carriers_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "carriers" DROP CONSTRAINT "FK_carriers_accountId"`);
    await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_products_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_products_accountId"`);
    await queryRunner.query(`ALTER TABLE "clients" DROP CONSTRAINT "FK_clients_businessUnitId"`);
    await queryRunner.query(`ALTER TABLE "clients" DROP CONSTRAINT "FK_clients_accountId"`);
    await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_users_currentBusinessUnitId"`);
    await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_users_accountId"`);
    await queryRunner.query(`ALTER TABLE "business_units" DROP CONSTRAINT "FK_business_units_accountId"`);
    await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_accountId"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "cep_validations"`);
    await queryRunner.query(`DROP TABLE "cnpj_validations"`);
    await queryRunner.query(`DROP TABLE "notifications"`);
    await queryRunner.query(`DROP TABLE "audit_logs"`);
    await queryRunner.query(`DROP TABLE "invoice_items"`);
    await queryRunner.query(`DROP TABLE "invoices"`);
    await queryRunner.query(`DROP TABLE "api_keys"`);
    await queryRunner.query(`DROP TABLE "certificates"`);
    await queryRunner.query(`DROP TABLE "payment_terms"`);
    await queryRunner.query(`DROP TABLE "carriers"`);
    await queryRunner.query(`DROP TABLE "products"`);
    await queryRunner.query(`DROP TABLE "clients"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TABLE "business_units"`);
    await queryRunner.query(`DROP TABLE "subscriptions"`);
    await queryRunner.query(`DROP TABLE "accounts"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "public"."cep_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."tax_regime_enum"`);
    await queryRunner.query(`DROP TYPE "public"."company_size_enum"`);
    await queryRunner.query(`DROP TYPE "public"."cnpj_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_priority_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_channel_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_category_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."audit_category_enum"`);
    await queryRunner.query(`DROP TYPE "public"."audit_severity_enum"`);
    await queryRunner.query(`DROP TYPE "public"."audit_entity_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."audit_action_enum"`);
    await queryRunner.query(`DROP TYPE "public"."api_key_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."certificate_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."certificate_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."invoice_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."invoice_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."company_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."business_unit_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."subscription_billing_cycle_enum"`);
    await queryRunner.query(`DROP TYPE "public"."subscription_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."subscription_tier_enum"`);
    await queryRunner.query(`DROP TYPE "public"."account_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
  }
}
