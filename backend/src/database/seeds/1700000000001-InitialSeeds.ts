import { DataSource } from 'typeorm';
import { Account, AccountStatus, AccountType } from '../entities/account.entity';
import { User, UserRole, UserStatus } from '../entities/user.entity';
import { BusinessUnit, BusinessUnitStatus, TaxRegime } from '../entities/business-unit.entity';
import { CompanyType, CompanySize } from '../entities/enums';
import { Subscription, SubscriptionTier, SubscriptionStatus, BillingCycle, PaymentStatus } from '../entities/subscription.entity';
import { Client, ClientStatus } from '../entities/client.entity';
import { Product, TaxOrigin, ProductStatus } from '../entities/product.entity';
import { Carrier, CarrierStatus } from '../entities/carrier.entity';
import { PaymentTerm, PaymentTermStatus } from '../entities/payment-term.entity';
import { DocumentType } from '../shared/types/brazilian-types';

export class InitialSeeds1700000000001 {
  public async run(dataSource: DataSource): Promise<void> {
    const accountRepo = dataSource.getRepository(Account);
    const userRepo = dataSource.getRepository(User);
    const businessUnitRepo = dataSource.getRepository(BusinessUnit);
    const subscriptionRepo = dataSource.getRepository(Subscription);
    const clientRepo = dataSource.getRepository(Client);
    const productRepo = dataSource.getRepository(Product);
    const carrierRepo = dataSource.getRepository(Carrier);
    const paymentTermRepo = dataSource.getRepository(PaymentTerm);

    console.log('Creating seed data...');

    // Create Demo Account
    const demoAccount = accountRepo.create({
      name: 'Demonstração EZNFE',
      companyName: 'Demonstração EZNFE LTDA',
      cnpj: '**************',
      status: AccountStatus.ACTIVE,
      type: AccountType.BUSINESS,
      description: 'Conta de demonstração do sistema EZNFE',
      website: 'https://demo.eznfe.com',
      industry: 'Software',
      maxUsers: 10,
      maxBusinessUnits: 5,
      maxInvoicesPerMonth: 1000,
      maxApiCallsPerMonth: 10000,
    });
    await accountRepo.save(demoAccount);

    // Create Demo Subscription
    const demoSubscription = subscriptionRepo.create({
      accountId: demoAccount.id,
      tier: SubscriptionTier.PROFESSIONAL,
      status: SubscriptionStatus.ACTIVE,
      billingCycle: BillingCycle.MONTHLY,
      monthlyPrice: 49.90,
      currentPrice: 49.90,
      currency: 'BRL',
      trialDays: 0,
      isTrialUsed: true,
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      invoiceLimitPerMonth: 1000,
      apiCallLimitPerMonth: 10000,
      maxUsers: 10,
      maxBusinessUnits: 5,
      maxProducts: 5000,
      maxClients: 5000,
      allowApiAccess: true,
      allowBulkOperations: true,
      allowAdvancedReports: true,
      allowCustomBranding: false,
      allowPrioritySupport: true,
      paymentStatus: PaymentStatus.PAID,
      activatedAt: new Date(),
      autoRenew: true,
    });
    await subscriptionRepo.save(demoSubscription);

    // Create Demo Business Unit
    const demoBusinessUnit = businessUnitRepo.create({
      accountId: demoAccount.id,
      name: 'Empresa Demo LTDA',
      legalName: 'EMPRESA DEMONSTRAÇÃO LTDA',
      tradeName: 'Empresa Demo',
      cnpj: '**************',
      stateRegistration: '*********',
      municipalRegistration: '*********',
      taxRegime: TaxRegime.SIMPLES_NACIONAL,
      status: BusinessUnitStatus.ACTIVE,
      email: '<EMAIL>',
      phone: '+55 11 3333-4444',
      website: 'https://empresademo.com.br',
      street: 'RUA DAS EMPRESAS',
      number: '123',
      complement: 'SALA 45',
      district: 'CENTRO',
      city: 'SÃO PAULO',
      state: 'SP',
      zipCode: '01234-567',
      country: 'BR',
      isDefault: true,
    });
    await businessUnitRepo.save(demoBusinessUnit);

    // Create Demo User
    const demoUser = userRepo.create({
      accountId: demoAccount.id,
      currentBusinessUnitId: demoBusinessUnit.id,
      email: '<EMAIL>',
      password: '$2b$10$rQX9.oQ8y8KxVsZp8Kz8S.Kz8S.Kz8S.Kz8S.Kz8S.Kz8S.Kz8S.K', // password: 'Demo123!'
      firstName: 'Administrador',
      lastName: 'Demo',
      phone: '+55 11 9999-8888',
      role: UserRole.OWNER,
      status: UserStatus.ACTIVE,
      emailVerifiedAt: new Date(),
    });
    await userRepo.save(demoUser);

    // Create Demo Clients
    const clients = [
      {
        name: 'CLIENTE PESSOA FÍSICA',
        legalName: 'João da Silva',
        document: '***********',
        documentType: DocumentType.CPF,
        email: '<EMAIL>',
        phone: '+55 11 9876-5432',
        street: 'RUA DOS CLIENTES',
        number: '456',
        district: 'JARDIM AMÉRICA',
        city: 'SÃO PAULO',
        state: 'SP',
        zipCode: '05432-100',
        country: 'BR',
      },
      {
        name: 'CLIENTE PESSOA JURÍDICA LTDA',
        legalName: 'CLIENTE EMPRESA LTDA',
        tradeName: 'Cliente Empresa',
        document: '98765432000123',
        documentType: DocumentType.CNPJ,
        stateRegistration: '*********',
        email: '<EMAIL>',
        phone: '+55 11 5555-1234',
        street: 'AVENIDA DOS CLIENTES',
        number: '789',
        complement: 'CONJUNTO 12',
        district: 'VILA MADALENA',
        city: 'SÃO PAULO',
        state: 'SP',
        zipCode: '05432-200',
        country: 'BR',
      },
      {
        name: 'CLIENTE INTERESTADUAL LTDA',
        legalName: 'CLIENTE INTERESTADUAL LTDA',
        tradeName: 'Cliente RJ',
        document: '112***********',
        documentType: DocumentType.CNPJ,
        stateRegistration: '111222333',
        email: '<EMAIL>',
        phone: '+55 21 3333-5555',
        street: 'RUA DE COPACABANA',
        number: '321',
        district: 'COPACABANA',
        city: 'RIO DE JANEIRO',
        state: 'RJ',
        zipCode: '22070-011',
        country: 'BR',
        ibgeCode: '3304557',
      },
    ];

    for (const clientData of clients) {
      const client = clientRepo.create({
        ...clientData,
        businessUnitId: demoBusinessUnit.id,
        status: ClientStatus.ACTIVE,
      });
      await clientRepo.save(client);
    }

    // Create Demo Products
    const products = [
      {
        name: 'PRODUTO DE REVENDA',
        description: 'Produto para revenda sem industrialização',
        sku: 'PRD-001',
        ean: '789*********0',
        ncmCode: '84713012',
        price: 100.00,
        costPrice: 60.00,
        unitOfMeasure: 'UN',
        weight: 1.5,
        taxOrigin: TaxOrigin.NACIONAL,
        icmsCst: '102',
        icmsRate: 0,
        pisCst: '06',
        pisRate: 0,
        cofinsCst: '06',
        cofinsRate: 0,
        cfopIntrastate: '5102',
        cfopInterstate: '6102',
      },
      {
        name: 'PRODUTO INDUSTRIALIZADO',
        description: 'Produto com processo de industrialização próprio',
        sku: 'PRD-002',
        ean: '789*********1',
        ncmCode: '84713013',
        price: 250.00,
        costPrice: 150.00,
        unitOfMeasure: 'UN',
        weight: 2.3,
        taxOrigin: TaxOrigin.NACIONAL,
        icmsCst: '000',
        icmsRate: 18,
        pisCst: '01',
        pisRate: 1.65,
        cofinsCst: '01',
        cofinsRate: 7.60,
        cfopIntrastate: '5101',
        cfopInterstate: '6101',
      },
      {
        name: 'SERVIÇO TÉCNICO',
        description: 'Prestação de serviços técnicos especializados',
        sku: 'SRV-001',
        ncmCode: '00000000',
        price: 150.00,
        unitOfMeasure: 'HR',
        weight: 0,
        taxOrigin: TaxOrigin.NACIONAL,
        icmsCst: '400',
        icmsRate: 0,
        pisCst: '01',
        pisRate: 1.65,
        cofinsCst: '01',
        cofinsRate: 7.60,
        cfopIntrastate: '5933',
        cfopInterstate: '6933',
      },
      {
        name: 'PRODUTO IMPORTADO',
        description: 'Produto importado para revenda',
        sku: 'IMP-001',
        ean: '789*********2',
        ncmCode: '84713014',
        price: 350.00,
        costPrice: 200.00,
        unitOfMeasure: 'UN',
        weight: 3.0,
        taxOrigin: TaxOrigin.ESTRANGEIRA_IMPORTACAO_DIRETA,
        icmsCst: '000',
        icmsRate: 18,
        pisCst: '01',
        pisRate: 1.65,
        cofinsCst: '01',
        cofinsRate: 7.60,
        ipiCst: '50',
        ipiRate: 10,
        cfopIntrastate: '5401',
        cfopInterstate: '6401',
      },
    ];

    for (const productData of products) {
      const product = productRepo.create({
        ...productData,
        businessUnitId: demoBusinessUnit.id,
        status: ProductStatus.ACTIVE,
      });
      await productRepo.save(product);
    }

    // Create Demo Carrier
    const demoCarrier = carrierRepo.create({
      businessUnitId: demoBusinessUnit.id,
      name: 'TRANSPORTADORA DEMO LTDA',
      documentNumber: '55666777000188',
      documentType: DocumentType.CNPJ,
      stateRegistration: '*********',
      email: '<EMAIL>',
      phone: '+55 11 4444-5555',
      street: 'RUA DOS TRANSPORTES',
      number: '999',
      district: 'INDUSTRIAL',
      city: 'SÃO PAULO',
      state: 'SP',
      zipCode: '08888-999',
      country: 'BR',
      status: CarrierStatus.ACTIVE,
    });
    await carrierRepo.save(demoCarrier);

    // Create Demo Payment Terms
    const paymentTerms = [
      {
        name: 'À Vista',
        description: 'Pagamento à vista com desconto',
        installments: 1,
        intervalDays: 0,
        firstInstallmentDays: 0,
        discountPercentage: 2.5,
        isDefault: true,
      },
      {
        name: '30 DDL',
        description: 'Pagamento a 30 dias data limite',
        installments: 1,
        intervalDays: 30,
        firstInstallmentDays: 30,
        discountPercentage: 0,
        finePercentage: 2.0,
        interestPercentage: 1.0,
      },
      {
        name: '30/60 DDL',
        description: 'Pagamento em 2x (30/60 dias)',
        installments: 2,
        intervalDays: 30,
        firstInstallmentDays: 30,
        discountPercentage: 0,
        finePercentage: 2.0,
        interestPercentage: 1.0,
      },
      {
        name: '30/60/90 DDL',
        description: 'Pagamento em 3x (30/60/90 dias)',
        installments: 3,
        intervalDays: 30,
        firstInstallmentDays: 30,
        discountPercentage: 0,
        finePercentage: 2.0,
        interestPercentage: 1.0,
      },
    ];

    for (const termData of paymentTerms) {
      const paymentTerm = paymentTermRepo.create({
        ...termData,
        businessUnitId: demoBusinessUnit.id,
        status: PaymentTermStatus.ACTIVE,
      });
      await paymentTermRepo.save(paymentTerm);
    }

    console.log('✅ Seed data created successfully!');
    
    console.log('\n📋 Demo Account Details:');
    console.log(`Account: ${demoAccount.name}`);
    console.log(`Business Unit: ${demoBusinessUnit.name}`);
    console.log(`CNPJ: ${demoBusinessUnit.cnpj}`);
    console.log(`User: ${demoUser.email}`);
    console.log(`Password: Demo123!`);
    console.log(`Subscription: ${demoSubscription.tier} - ${demoSubscription.status}`);
    
    console.log('\n📊 Created Records:');
    console.log(`- ${clients.length} clients`);
    console.log(`- ${products.length} products`);
    console.log(`- 1 carrier`);
    console.log(`- ${paymentTerms.length} payment terms`);
  }
}

export default InitialSeeds1700000000001;
