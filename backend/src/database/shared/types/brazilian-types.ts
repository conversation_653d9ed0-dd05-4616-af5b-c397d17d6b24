// Brazilian document types
export enum DocumentType {
  CPF = 'cpf',
  CNPJ = 'cnpj',
  RG = 'rg',
  PASSPORT = 'passport',
  OTHER = 'other',
}

// Person type for Brazilian fiscal system
export enum PersonType {
  INDIVIDUAL = 'individual', // Pessoa Física
  COMPANY = 'company', // Pessoa Jurídica
  FOREIGN = 'foreign', // Pessoa E<PERSON>ngeira
}

// Payment methods accepted in Brazil
export enum PaymentMethod {
  MONEY = 'money', // Dinheiro
  CREDIT_CARD = 'credit_card', // Cartão de Crédito
  DEBIT_CARD = 'debit_card', // Cartão de Débito
  BANK_TRANSFER = 'bank_transfer', // Transferência Bancária
  PIX = 'pix', // PIX (Instant Payment System)
  BANK_SLIP = 'bank_slip', // Boleto Bancário
  FINANCING = 'financing', // Financiamento
  CHECK = 'check', // Cheque
  OTHER = 'other', // Outros
}

// Payment timing options for Brazilian context
export enum PaymentTiming {
  IMMEDIATE = 'immediate', // À vista
  INSTALLMENTS = 'installments', // Parcelado
  ADVANCE = 'advance', // Antecipado
  TERM = 'term', // A prazo
  ON_DELIVERY = 'on_delivery', // Na entrega
}

// Brazilian state codes
export enum BrazilianState {
  AC = 'AC', // Acre
  AL = 'AL', // Alagoas
  AP = 'AP', // Amapá
  AM = 'AM', // Amazonas
  BA = 'BA', // Bahia
  CE = 'CE', // Ceará
  DF = 'DF', // Distrito Federal
  ES = 'ES', // Espírito Santo
  GO = 'GO', // Goiás
  MA = 'MA', // Maranhão
  MT = 'MT', // Mato Grosso
  MS = 'MS', // Mato Grosso do Sul
  MG = 'MG', // Minas Gerais
  PA = 'PA', // Pará
  PB = 'PB', // Paraíba
  PR = 'PR', // Paraná
  PE = 'PE', // Pernambuco
  PI = 'PI', // Piauí
  RJ = 'RJ', // Rio de Janeiro
  RN = 'RN', // Rio Grande do Norte
  RS = 'RS', // Rio Grande do Sul
  RO = 'RO', // Rondônia
  RR = 'RR', // Roraima
  SC = 'SC', // Santa Catarina
  SP = 'SP', // São Paulo
  SE = 'SE', // Sergipe
  TO = 'TO', // Tocantins
}

// Tax origin types for Brazilian products
export enum TaxOrigin {
  NACIONAL = 'nacional', // Nacional, exceto as indicadas nos códigos 3, 4, 5 e 8
  ESTRANGEIRA_IMPORTACAO_DIRETA = 'estrangeira_importacao_direta', // Estrangeira - Importação direta
  ESTRANGEIRA_MERCADO_INTERNO = 'estrangeira_mercado_interno', // Estrangeira - Adquirida no mercado interno
  NACIONAL_CONTEUDO_SUPERIOR = 'nacional_conteudo_superior', // Nacional, mercadoria ou bem com Conteúdo de Importação superior a 40% e inferior ou igual a 70%
  NACIONAL_PROCESSO_BASICO = 'nacional_processo_basico', // Nacional, cuja produção tenha sido feita em conformidade com os processos produtivos básicos
  NACIONAL_CONTEUDO_INFERIOR = 'nacional_conteudo_inferior', // Nacional, mercadoria ou bem com Conteúdo de Importação inferior ou igual a 40%
  ESTRANGEIRA_SEM_SIMILAR = 'estrangeira_sem_similar', // Estrangeira - Importação direta, sem similar nacional
  ESTRANGEIRA_ZONA_FRANCA = 'estrangeira_zona_franca', // Estrangeira - Adquirida no mercado interno, sem similar nacional
  NACIONAL_CONTEUDO_SUPERIOR_70 = 'nacional_conteudo_superior_70', // Nacional, mercadoria ou bem com Conteúdo de Importação superior a 70%
}

// CFOPs (Código Fiscal de Operações e Prestações) commonly used
export enum CommonCFOP {
  // Entradas
  COMPRA_INTERNA = '1102', // Compra para comercialização
  COMPRA_INTERESTADUAL = '2102', // Compra para comercialização - interestadual
  
  // Saídas
  VENDA_INTERNA = '5102', // Venda de mercadoria adquirida ou recebida de terceiros
  VENDA_INTERESTADUAL = '6102', // Venda de mercadoria adquirida ou recebida de terceiros - interestadual
  VENDA_PRODUCAO_INTERNA = '5101', // Venda de produção do estabelecimento
  VENDA_PRODUCAO_INTERESTADUAL = '6101', // Venda de produção do estabelecimento - interestadual
  
  // Serviços
  PRESTACAO_SERVICOS_INTERNA = '5933', // Prestação de serviços de transporte
  PRESTACAO_SERVICOS_INTERESTADUAL = '6933', // Prestação de serviços de transporte - interestadual
}

// Unit of measurement types used in Brazil
export enum UnitOfMeasurement {
  // Unidades básicas
  UNIDADE = 'UN', // Unidade
  QUILOGRAMA = 'KG', // Quilograma
  GRAMA = 'G', // Grama
  TONELADA = 'TON', // Tonelada
  LITRO = 'L', // Litro
  METRO = 'M', // Metro
  METRO_QUADRADO = 'M2', // Metro quadrado
  METRO_CUBICO = 'M3', // Metro cúbico
  
  // Unidades comerciais
  CAIXA = 'CX', // Caixa
  PACOTE = 'PC', // Pacote
  FARDO = 'FD', // Fardo
  DUZIA = 'DZ', // Dúzia
  PAR = 'PR', // Par
  CONJUNTO = 'CJ', // Conjunto
  
  // Unidades de medida especiais
  METRO_LINEAR = 'ML', // Metro linear
  QUILOWATT_HORA = 'KWH', // Quilowatt-hora
  AMPOLA = 'AMPOLA', // Ampola
  BALDE = 'BALDE', // Balde
  BANDEJA = 'BAND', // Bandeja
  BISNAGA = 'BISN', // Bisnaga
  BLOCO = 'BLOCO', // Bloco
  BOBINA = 'BOB', // Bobina
  BOMBONA = 'BOMB', // Bombona
  CAPSULA = 'CAPS', // Cápsula
  CARTUCHO = 'CART', // Cartucho
  CENTIMETRO = 'CM', // Centímetro
  CENTIGRAMA = 'CG', // Centigrama
  DISPLAY = 'DISP', // Display
  EMBALAGEM = 'EMB', // Embalagem
  ENGRADADO = 'ENG', // Engradado
  ENVELOPE = 'ENV', // Envelope
  ESTOJO = 'EST', // Estojo
  FRASCO = 'FRAS', // Frasco
  GALAO = 'GAL', // Galão
  GARRAFA = 'GRF', // Garrafa
  JOGO = 'JOGO', // Jogo
  LATA = 'LATA', // Lata
  MILIGRAMO = 'MG', // Miligrama
  MILILITRO = 'ML', // Mililitro
  MILIMETRO = 'MM', // Milímetro
  PECA = 'PCA', // Peça
  PLACA = 'PLACA', // Placa
  POTE = 'POTE', // Pote
  RESMA = 'RESMA', // Resma
  ROLO = 'ROLO', // Rolo
  SACO = 'SACO', // Saco
  SACHÊ = 'SACHE', // Sachê
  TAMBOR = 'TAMB', // Tambor
  TUBO = 'TUBO', // Tubo
  VASILLAME = 'VASIL', // Vasilhame
  VIDRO = 'VID', // Vidro
}
