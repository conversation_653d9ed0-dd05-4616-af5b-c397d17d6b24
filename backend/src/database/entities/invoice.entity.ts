import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import {
  InvoiceStatus,
  InvoiceType,
  PaymentMethod,
  PaymentTiming,
} from '../../shared/types/brazilian-types';

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  NOT_REQUIRED = 'not_required',
}

@Entity('invoices')
@Index(['businessUnitId'])
@Index(['clientId'])
@Index(['status'])
@Index(['approvalStatus'])
@Index(['issueDate'])
@Index(['number', 'series', 'businessUnitId'], { unique: true })
export class Invoice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Invoice identification
  @Column({ type: 'int' })
  number: number;

  @Column({ type: 'int', default: 1 })
  series: number;

  @Column({ type: 'varchar', length: 44, nullable: true })
  accessKey: string; // NF-e access key (44 digits)

  @Column({ type: 'varchar', length: 50, nullable: true })
  authorizationProtocol: string; // SEFAZ authorization protocol

  @Column({
    type: 'enum',
    enum: InvoiceType,
    default: InvoiceType.SAIDA,
  })
  type: InvoiceType;

  @Column({
    type: 'enum',
    enum: InvoiceStatus,
    default: InvoiceStatus.DRAFT,
  })
  status: InvoiceStatus;

  @Column({
    type: 'enum',
    enum: ApprovalStatus,
    default: ApprovalStatus.NOT_REQUIRED,
  })
  approvalStatus: ApprovalStatus;

  // Dates
  @Column({ type: 'timestamp' })
  issueDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  exitDate: Date; // Date of product exit/delivery

  @Column({ type: 'timestamp', nullable: true })
  dueDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  sentToSefazAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  authorizedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancelledAt: Date;

  // Financial totals
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  subtotal: number; // Sum of all items before taxes and discounts

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  discountValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalBeforeTax: number; // Subtotal - discount

  // Tax totals
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  icmsTotal: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  pisTotal: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cofinsTotal: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  ipiTotal: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  issTotal: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalTaxValue: number; // Sum of all taxes

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalValue: number; // Final total including taxes

  // Transport information
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  freightValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  insuranceValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  additionalExpenses: number;

  @Column({ type: 'varchar', length: 20, default: 'emitente' })
  freightType: string; // emitente, destinatario, terceiros

  // Package information
  @Column({ type: 'int', default: 1 })
  packageCount: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  packageType: string;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  grossWeight: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  netWeight: number;

  // Payment information
  @Column({
    type: 'enum',
    enum: PaymentMethod,
    default: PaymentMethod.DINHEIRO,
  })
  paymentMethod: PaymentMethod;

  @Column({
    type: 'enum',
    enum: PaymentTiming,
    default: PaymentTiming.A_VISTA,
  })
  paymentTiming: PaymentTiming;

  @Column({ type: 'text', nullable: true })
  paymentInstallments: string; // JSON array of installments

  // Additional information
  @Column({ type: 'text', nullable: true })
  observations: string; // Additional observations for NF-e

  @Column({ type: 'text', nullable: true })
  fiscalObservations: string; // Fiscal observations

  @Column({ type: 'text', nullable: true })
  internalNotes: string; // Internal notes not shown in NF-e

  // SEFAZ information
  @Column({ type: 'varchar', length: 255, nullable: true })
  sefazResponse: string; // Last SEFAZ response message

  @Column({ type: 'varchar', length: 10, nullable: true })
  sefazStatusCode: string;

  @Column({ type: 'text', nullable: true })
  sefazErrors: string; // JSON array of errors

  @Column({ type: 'text', nullable: true })
  sefazWarnings: string; // JSON array of warnings

  // Document files
  @Column({ type: 'varchar', length: 255, nullable: true })
  xmlFilePath: string; // Path to generated XML file

  @Column({ type: 'varchar', length: 255, nullable: true })
  xmlSignedFilePath: string; // Path to signed XML file

  @Column({ type: 'varchar', length: 255, nullable: true })
  danfeFilePath: string; // Path to generated DANFE PDF

  @Column({ type: 'varchar', length: 255, nullable: true })
  backupFilePath: string; // Path to backup file

  // Approval workflow
  @Column({ type: 'uuid', nullable: true })
  approvedByUserId: string;

  @Column({ type: 'text', nullable: true })
  approvalComments: string;

  @Column({ type: 'uuid', nullable: true })
  rejectedByUserId: string;

  @Column({ type: 'text', nullable: true })
  rejectionReason: string;

  // Cancellation information
  @Column({ type: 'text', nullable: true })
  cancellationReason: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cancellationProtocol: string;

  @Column({ type: 'uuid', nullable: true })
  cancelledByUserId: string;

  // Correction letters
  @Column({ type: 'int', default: 0 })
  correctionLetterCount: number;

  @Column({ type: 'text', nullable: true })
  lastCorrectionLetter: string; // JSON with last correction info

  // Multi-tenant relationships
  @Column({ type: 'uuid' })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', (businessUnit: any) => businessUnit.invoices, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Client relationship
  @Column({ type: 'uuid' })
  clientId: string;

  @ManyToOne('Client', (client: any) => client.invoices, {
    onDelete: 'RESTRICT', // Prevent deletion of client with invoices
  })
  @JoinColumn({ name: 'clientId' })
  client: any;

  // Optional relationships
  @Column({ type: 'uuid', nullable: true })
  carrierId: string;

  @ManyToOne('Carrier', (carrier: any) => carrier.invoices, {
    nullable: true,
  })
  @JoinColumn({ name: 'carrierId' })
  carrier: any;

  @Column({ type: 'uuid', nullable: true })
  paymentTermId: string;

  @ManyToOne('PaymentTerm', (paymentTerm: any) => paymentTerm.invoices, {
    nullable: true,
  })
  @JoinColumn({ name: 'paymentTermId' })
  paymentTerm: any;

  // User relationships
  @Column({ type: 'uuid' })
  createdByUserId: string;

  @ManyToOne('User', {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'createdByUserId' })
  createdByUser: any;

  @ManyToOne('User', {
    nullable: true,
  })
  @JoinColumn({ name: 'approvedByUserId' })
  approvedByUser: any;

  @ManyToOne('User', {
    nullable: true,
  })
  @JoinColumn({ name: 'rejectedByUserId' })
  rejectedByUser: any;

  @ManyToOne('User', {
    nullable: true,
  })
  @JoinColumn({ name: 'cancelledByUserId' })
  cancelledByUser: any;

  // Invoice items - using string reference to break circular dependency
  @OneToMany('InvoiceItem', (invoiceItem: any) => invoiceItem.invoice, {
    cascade: true,
  })
  items: any[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isDraft(): boolean {
    return this.status === InvoiceStatus.DRAFT;
  }

  get isPendingApproval(): boolean {
    return this.approvalStatus === ApprovalStatus.PENDING;
  }

  get isApproved(): boolean {
    return this.approvalStatus === ApprovalStatus.APPROVED;
  }

  get isRejected(): boolean {
    return this.approvalStatus === ApprovalStatus.REJECTED;
  }

  get isAuthorized(): boolean {
    return this.status === InvoiceStatus.AUTHORIZED;
  }

  get isCancelled(): boolean {
    return this.status === InvoiceStatus.CANCELLED;
  }

  get canBeEdited(): boolean {
    return this.isDraft && !this.isPendingApproval;
  }

  get canBeApproved(): boolean {
    return (
      this.isPendingApproval && this.status === InvoiceStatus.PENDING_APPROVAL
    );
  }

  get canBeSentToSefaz(): boolean {
    return this.isApproved && this.status === InvoiceStatus.APPROVED;
  }

  get canBeCancelled(): boolean {
    return this.isAuthorized && !this.isCancelled;
  }

  get canHaveCorrectionLetter(): boolean {
    return (
      this.isAuthorized && !this.isCancelled && this.correctionLetterCount < 20
    );
  }

  get formattedNumber(): string {
    return this.number.toString().padStart(9, '0');
  }

  get formattedAccessKey(): string {
    if (!this.accessKey || this.accessKey.length !== 44)
      return this.accessKey || '';
    return this.accessKey.replace(
      /(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})(\d{4})/,
      '$1 $2 $3 $4 $5 $6 $7 $8 $9 $10 $11',
    );
  }

  get totalWeight(): number {
    return this.grossWeight + this.netWeight;
  }

  // Calculate totals from items
  calculateTotals(): void {
    if (!this.items?.length) return;

    this.subtotal = this.items.reduce((sum, item) => sum + item.totalValue, 0);
    this.icmsTotal = this.items.reduce((sum, item) => sum + item.icmsValue, 0);
    this.pisTotal = this.items.reduce((sum, item) => sum + item.pisValue, 0);
    this.cofinsTotal = this.items.reduce(
      (sum, item) => sum + item.cofinsValue,
      0,
    );
    this.ipiTotal = this.items.reduce((sum, item) => sum + item.ipiValue, 0);

    this.totalTaxValue =
      this.icmsTotal +
      this.pisTotal +
      this.cofinsTotal +
      this.ipiTotal +
      this.issTotal;
    this.totalBeforeTax = this.subtotal - this.discountValue;
    this.totalValue =
      this.totalBeforeTax +
      this.totalTaxValue +
      this.freightValue +
      this.insuranceValue +
      this.additionalExpenses;
  }

  // Generate access key (simplified - should be done by Hermes service)
  generateAccessKey(): void {
    if (this.accessKey) return;

    const state = this.businessUnit?.state || '35'; // Default SP
    const issueDate = new Date(this.issueDate);
    const year = issueDate.getFullYear().toString().slice(-2);
    const month = (issueDate.getMonth() + 1).toString().padStart(2, '0');
    const cnpj =
      this.businessUnit?.cnpj?.replace(/\D/g, '') || '00000000000000';
    const model = '55'; // NF-e model
    const series = this.series.toString().padStart(3, '0');
    const number = this.number.toString().padStart(9, '0');
    const emission = '1'; // Normal emission
    const random = Math.floor(Math.random() * *********)
      .toString()
      .padStart(8, '0');

    // This is a simplified version - real implementation should be in Hermes
    const preKey = `${state}${year}${month}${cnpj}${model}${series}${number}${emission}${random}`;
    const checkDigit = this.calculateAccessKeyCheckDigit(preKey);
    this.accessKey = preKey + checkDigit;
  }

  private calculateAccessKeyCheckDigit(key: string): string {
    // Simplified check digit calculation - should use proper algorithm
    const weights = [2, 3, 4, 5, 6, 7, 8, 9];
    let sum = 0;
    let weightIndex = 0;

    for (let i = key.length - 1; i >= 0; i--) {
      sum += parseInt(key[i]) * weights[weightIndex];
      weightIndex = (weightIndex + 1) % weights.length;
    }

    const remainder = sum % 11;
    return remainder < 2 ? '0' : (11 - remainder).toString();
  }

  // Update status with validation
  updateStatus(newStatus: InvoiceStatus, userId?: string): boolean {
    const validTransitions: Record<InvoiceStatus, InvoiceStatus[]> = {
      [InvoiceStatus.DRAFT]: [
        InvoiceStatus.PENDING_APPROVAL,
        InvoiceStatus.APPROVED,
      ],
      [InvoiceStatus.PENDING_APPROVAL]: [
        InvoiceStatus.APPROVED,
        InvoiceStatus.REJECTED,
        InvoiceStatus.DRAFT,
      ],
      [InvoiceStatus.APPROVED]: [InvoiceStatus.SENT_TO_SEFAZ],
      [InvoiceStatus.REJECTED]: [InvoiceStatus.DRAFT],
      [InvoiceStatus.SENT_TO_SEFAZ]: [
        InvoiceStatus.AUTHORIZED,
        InvoiceStatus.REJECTED,
        InvoiceStatus.DENIED,
      ],
      [InvoiceStatus.AUTHORIZED]: [InvoiceStatus.CANCELLED],
      [InvoiceStatus.CANCELLED]: [],
      [InvoiceStatus.DENIED]: [InvoiceStatus.DRAFT],
    };

    if (!validTransitions[this.status]?.includes(newStatus)) {
      return false;
    }

    this.status = newStatus;

    // Set timestamps
    switch (newStatus) {
      case InvoiceStatus.APPROVED:
        this.approvedAt = new Date();
        this.approvalStatus = ApprovalStatus.APPROVED;
        if (userId) this.approvedByUserId = userId;
        break;
      case InvoiceStatus.REJECTED:
        this.approvalStatus = ApprovalStatus.REJECTED;
        if (userId) this.rejectedByUserId = userId;
        break;
      case InvoiceStatus.SENT_TO_SEFAZ:
        this.sentToSefazAt = new Date();
        break;
      case InvoiceStatus.AUTHORIZED:
        this.authorizedAt = new Date();
        break;
      case InvoiceStatus.CANCELLED:
        this.cancelledAt = new Date();
        if (userId) this.cancelledByUserId = userId;
        break;
    }

    return true;
  }
}
