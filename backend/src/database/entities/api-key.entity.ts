import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';

export enum ApiKeyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  SUSPENDED = 'suspended',
}

export enum ApiKeyScope {
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
  WEBHOOK = 'webhook',
}

@Entity('api_keys')
@Index(['accountId'])
@Index(['keyHash'], { unique: true })
@Index(['status'])
@Index(['expiresAt'])
export class ApiKey {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string; // Friendly name for the API key

  @Column({ type: 'text', nullable: true })
  description?: string; // Description of what this key is used for

  @Column({ type: 'varchar', length: 255, unique: true })
  keyHash: string; // Hashed version of the API key

  @Column({ type: 'varchar', length: 50 })
  keyPrefix: string; // First few characters of the key for identification

  @Column({
    type: 'enum',
    enum: ApiKeyStatus,
    default: ApiKeyStatus.ACTIVE,
  })
  status: ApiKeyStatus;

  // Permissions and scopes
  @Column({
    type: 'enum',
    enum: ApiKeyScope,
    array: true,
    default: () => `'{${ApiKeyScope.READ}}'`,
  })
  scopes: ApiKeyScope[];

  @Column({ type: 'text', nullable: true })
  permissions?: string; // JSON array of specific permissions

  // Rate limiting
  @Column({ type: 'int', default: 1000 })
  rateLimitPerHour: number;

  @Column({ type: 'int', default: 10000 })
  rateLimitPerDay: number;

  @Column({ type: 'int', default: 100000 })
  rateLimitPerMonth: number;

  // IP restrictions
  @Column({ type: 'text', nullable: true })
  allowedIps?: string; // JSON array of allowed IP addresses/ranges

  @Column({ type: 'boolean', default: false })
  restrictByIp: boolean;

  // Expiration
  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'boolean', default: false })
  neverExpires: boolean;

  // Usage tracking
  @Column({ type: 'int', default: 0 })
  totalUsage: number; // Total number of API calls made with this key

  @Column({ type: 'int', default: 0 })
  currentHourUsage: number; // Usage in current hour

  @Column({ type: 'int', default: 0 })
  currentDayUsage: number; // Usage in current day

  @Column({ type: 'int', default: 0 })
  currentMonthUsage: number; // Usage in current month

  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt?: Date; // Last time this key was used

  @Column({ type: 'varchar', length: 45, nullable: true })
  lastUsedFromIp?: string; // Last IP address that used this key

  @Column({ type: 'varchar', length: 255, nullable: true })
  lastUsedUserAgent?: string; // Last user agent that used this key

  @Column({ type: 'varchar', length: 100, nullable: true })
  lastEndpoint?: string; // Last API endpoint accessed

  // Usage reset tracking
  @Column({ type: 'timestamp', nullable: true })
  hourlyUsageResetAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  dailyUsageResetAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  monthlyUsageResetAt?: Date;

  // Webhook specific fields
  @Column({ type: 'text', nullable: true })
  webhookUrls?: string; // JSON array of webhook URLs this key can access

  @Column({ type: 'varchar', length: 255, nullable: true })
  webhookSecret?: string; // Secret for webhook signature verification

  @Column({ type: 'boolean', default: false })
  webhookEnabled: boolean;

  // Environment and integration info
  @Column({ type: 'varchar', length: 50, default: 'production' })
  environment: string; // production, development, staging

  @Column({ type: 'varchar', length: 255, nullable: true })
  integrationName?: string; // Name of the integration using this key

  @Column({ type: 'varchar', length: 255, nullable: true })
  integrationVersion?: string; // Version of the integration

  @Column({ type: 'text', nullable: true })
  metadata?: string; // Additional metadata in JSON format

  // Security features
  @Column({ type: 'boolean', default: false })
  requiresHttps: boolean; // Only allow HTTPS requests

  @Column({ type: 'text', nullable: true })
  allowedDomains?: string; // JSON array of allowed domains for CORS

  @Column({ type: 'varchar', length: 255, nullable: true })
  apiVersion?: string; // API version this key is restricted to

  // Monitoring and alerts
  @Column({ type: 'boolean', default: false })
  alertOnUsage: boolean; // Send alerts when usage thresholds are met

  @Column({ type: 'int', nullable: true })
  usageAlertThreshold?: number; // Percentage threshold for usage alerts

  @Column({ type: 'timestamp', nullable: true })
  lastAlertSentAt?: Date;

  @Column({ type: 'boolean', default: false })
  alertOnSuspiciousActivity: boolean;

  // Revocation and suspension
  @Column({ type: 'timestamp', nullable: true })
  revokedAt?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  revocationReason?: string;

  @Column({ type: 'timestamp', nullable: true })
  suspendedAt?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  suspensionReason?: string;

  @Column({ type: 'timestamp', nullable: true })
  suspendedUntil?: Date;

  // Relationships
  @Column({ type: 'uuid' })
  accountId: string;

  @ManyToOne('Account', (account: any) => account.apiKeys, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: any;

  @Column({ type: 'uuid' })
  createdByUserId: string;

  @ManyToOne('User', {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'createdByUserId' })
  createdByUser: any;

  @Column({ type: 'uuid', nullable: true })
  lastUsedByUserId?: string;

  @ManyToOne('User', {
    nullable: true,
  })
  @JoinColumn({ name: 'lastUsedByUserId' })
  lastUsedByUser?: any;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === ApiKeyStatus.ACTIVE;
  }

  get isExpired(): boolean {
    if (this.neverExpires) return false;
    return (
      this.status === ApiKeyStatus.EXPIRED ||
      Boolean(this.expiresAt && this.expiresAt < new Date())
    );
  }

  get isRevoked(): boolean {
    return this.status === ApiKeyStatus.REVOKED;
  }

  get isSuspended(): boolean {
    return (
      this.status === ApiKeyStatus.SUSPENDED ||
      Boolean(this.suspendedUntil && this.suspendedUntil > new Date())
    );
  }

  get canBeUsed(): boolean {
    return (
      this.isActive && !this.isExpired && !this.isRevoked && !this.isSuspended
    );
  }

  get daysUntilExpiration(): number {
    if (this.neverExpires || !this.expiresAt) return -1;
    const now = new Date();
    const diffTime = this.expiresAt.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get isExpiringSoon(): boolean {
    const daysUntil = this.daysUntilExpiration;
    return daysUntil > 0 && daysUntil <= 30; // Expires in 30 days or less
  }

  get usagePercentageHourly(): number {
    if (this.rateLimitPerHour === 0) return 0;
    return (this.currentHourUsage / this.rateLimitPerHour) * 100;
  }

  get usagePercentageDaily(): number {
    if (this.rateLimitPerDay === 0) return 0;
    return (this.currentDayUsage / this.rateLimitPerDay) * 100;
  }

  get usagePercentageMonthly(): number {
    if (this.rateLimitPerMonth === 0) return 0;
    return (this.currentMonthUsage / this.rateLimitPerMonth) * 100;
  }

  get maskedKey(): string {
    return `${this.keyPrefix}${'*'.repeat(20)}`;
  }

  get statusDescription(): string {
    const statusDescriptions: Record<ApiKeyStatus, string> = {
      [ApiKeyStatus.ACTIVE]: 'Ativa',
      [ApiKeyStatus.INACTIVE]: 'Inativa',
      [ApiKeyStatus.EXPIRED]: 'Expirada',
      [ApiKeyStatus.REVOKED]: 'Revogada',
      [ApiKeyStatus.SUSPENDED]: 'Suspensa',
    };
    return statusDescriptions[this.status];
  }

  // Check if API key has specific scope
  hasScope(scope: ApiKeyScope): boolean {
    return this.scopes.includes(scope);
  }

  // Check if API key has admin privileges
  isAdmin(): boolean {
    return this.hasScope(ApiKeyScope.ADMIN);
  }

  // Check if API key can read data
  canRead(): boolean {
    return this.hasScope(ApiKeyScope.READ) || this.hasScope(ApiKeyScope.ADMIN);
  }

  // Check if API key can write data
  canWrite(): boolean {
    return this.hasScope(ApiKeyScope.WRITE) || this.hasScope(ApiKeyScope.ADMIN);
  }

  // Check if API key can access webhooks
  canUseWebhooks(): boolean {
    return (
      this.hasScope(ApiKeyScope.WEBHOOK) || this.hasScope(ApiKeyScope.ADMIN)
    );
  }

  // Validate IP address access
  isIpAllowed(clientIp: string): boolean {
    if (!this.restrictByIp) return true;

    try {
      const allowedIps = JSON.parse(this.allowedIps || '[]');
      return allowedIps.includes(clientIp);
    } catch {
      return false;
    }
  }

  // Check rate limits
  isRateLimited(): { limited: boolean; reason?: string } {
    if (this.currentHourUsage >= this.rateLimitPerHour) {
      return { limited: true, reason: 'Hourly rate limit exceeded' };
    }

    if (this.currentDayUsage >= this.rateLimitPerDay) {
      return { limited: true, reason: 'Daily rate limit exceeded' };
    }

    if (this.currentMonthUsage >= this.rateLimitPerMonth) {
      return { limited: true, reason: 'Monthly rate limit exceeded' };
    }

    return { limited: false };
  }

  // Increment usage counters
  incrementUsage(): void {
    this.totalUsage++;
    this.currentHourUsage++;
    this.currentDayUsage++;
    this.currentMonthUsage++;
    this.lastUsedAt = new Date();
  }

  // Reset usage counters based on time period
  resetUsageIfNeeded(): void {
    const now = new Date();

    // Reset hourly usage
    if (
      !this.hourlyUsageResetAt ||
      this.isNewHour(now, this.hourlyUsageResetAt)
    ) {
      this.currentHourUsage = 0;
      this.hourlyUsageResetAt = now;
    }

    // Reset daily usage
    if (!this.dailyUsageResetAt || this.isNewDay(now, this.dailyUsageResetAt)) {
      this.currentDayUsage = 0;
      this.dailyUsageResetAt = now;
    }

    // Reset monthly usage
    if (
      !this.monthlyUsageResetAt ||
      this.isNewMonth(now, this.monthlyUsageResetAt)
    ) {
      this.currentMonthUsage = 0;
      this.monthlyUsageResetAt = now;
    }
  }

  private isNewHour(current: Date, last: Date): boolean {
    return (
      current.getHours() !== last.getHours() ||
      current.getDate() !== last.getDate() ||
      current.getMonth() !== last.getMonth() ||
      current.getFullYear() !== last.getFullYear()
    );
  }

  private isNewDay(current: Date, last: Date): boolean {
    return (
      current.getDate() !== last.getDate() ||
      current.getMonth() !== last.getMonth() ||
      current.getFullYear() !== last.getFullYear()
    );
  }

  private isNewMonth(current: Date, last: Date): boolean {
    return (
      current.getMonth() !== last.getMonth() ||
      current.getFullYear() !== last.getFullYear()
    );
  }

  // Update last usage information
  updateLastUsage(ip?: string, userAgent?: string, endpoint?: string): void {
    this.lastUsedAt = new Date();
    if (ip) this.lastUsedFromIp = ip;
    if (userAgent) this.lastUsedUserAgent = userAgent;
    if (endpoint) this.lastEndpoint = endpoint;
  }

  // Revoke API key
  revoke(reason?: string): void {
    this.status = ApiKeyStatus.REVOKED;
    this.revokedAt = new Date();
    this.revocationReason = reason || 'API key revoked by user';
  }

  // Suspend API key
  suspend(reason?: string, until?: Date): void {
    this.status = ApiKeyStatus.SUSPENDED;
    this.suspendedAt = new Date();
    this.suspensionReason = reason || 'API key suspended';
    this.suspendedUntil = until;
  }

  // Reactivate API key
  reactivate(): void {
    if (this.isSuspended) {
      this.status = ApiKeyStatus.ACTIVE;
      this.suspendedAt = undefined;
      this.suspensionReason = undefined;
      this.suspendedUntil = undefined;
    }
  }

  // Extend expiration date
  extendExpiration(days: number): void {
    if (this.neverExpires) return;

    if (this.expiresAt) {
      this.expiresAt = new Date(
        this.expiresAt.getTime() + days * 24 * 60 * 60 * 1000,
      );
    } else {
      const now = new Date();
      this.expiresAt = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    }

    // Reactivate if it was expired
    if (this.status === ApiKeyStatus.EXPIRED) {
      this.status = ApiKeyStatus.ACTIVE;
    }
  }

  // Check if usage alert should be sent
  shouldSendUsageAlert(): boolean {
    if (!this.alertOnUsage || !this.usageAlertThreshold) return false;

    const maxUsage = Math.max(
      this.usagePercentageHourly,
      this.usagePercentageDaily,
      this.usagePercentageMonthly,
    );

    if (maxUsage < this.usageAlertThreshold) return false;

    // Don't send alert more than once per day
    if (this.lastAlertSentAt) {
      const daysSinceLastAlert = Math.floor(
        (Date.now() - this.lastAlertSentAt.getTime()) / (1000 * 60 * 60 * 24),
      );
      return daysSinceLastAlert >= 1;
    }

    return true;
  }

  // Mark usage alert as sent
  markAlertSent(): void {
    this.lastAlertSentAt = new Date();
  }

  // Validate request origin
  isOriginAllowed(origin: string): boolean {
    if (!this.allowedDomains) return true;

    try {
      const allowedDomains = JSON.parse(this.allowedDomains);
      return allowedDomains.some(
        (domain: string) => origin.endsWith(domain) || origin === domain,
      );
    } catch {
      return true; // If JSON parsing fails, allow by default
    }
  }

  // Generate API key hash (placeholder - use proper hashing in real implementation)
  static generateKeyHash(key: string): string {
    // In real implementation, use crypto.createHash with salt
    return Buffer.from(key).toString('base64');
  }

  // Verify API key against hash
  static verifyKey(key: string, hash: string): boolean {
    return ApiKey.generateKeyHash(key) === hash;
  }
}
