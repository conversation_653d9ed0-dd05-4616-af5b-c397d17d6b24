import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';

export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DISCONTINUED = 'discontinued',
}

export enum ProductType {
  PRODUTO = 'produto',
  SERVICO = 'servico',
  ATIVO_IMOBILIZADO = 'ativo_imobilizado',
}

export enum TaxOrigin {
  NACIONAL = '0', // National
  ESTRANGEIRA_IMPORTACAO_DIRETA = '1', // Foreign - direct import
  ESTRANGEIRA_MERCADO_INTERNO = '2', // Foreign - domestic market
  NACIONAL_CONTEUDO_IMPORTACAO_SUPERIOR_40 = '3', // National with >40% import content
  NACIONAL_PRODUCAO_CONFORMIDADE = '4', // National production in compliance
  NACIONAL_CONTEUDO_IMPORTACAO_INFERIOR_40 = '5', // National with <40% import content
  ESTRANGEIRA_IMPORTACAO_DIRETA_SEM_SIMILAR = '6', // Foreign import without national equivalent
  ESTRANGEIRA_MERCADO_INTERNO_SEM_SIMILAR = '7', // Foreign domestic market without national equivalent
  NACIONAL_CONTEUDO_IMPORTACAO_SUPERIOR_70 = '8', // National with >70% import content
}

@Entity('products')
@Index(['businessUnitId'])
@Index(['sku'])
@Index(['status'])
@Index(['businessUnitId', 'sku'], { unique: true })
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  sku: string; // Stock Keeping Unit

  @Column({ type: 'varchar', length: 100, nullable: true })
  barcode: string; // EAN/GTIN

  @Column({
    type: 'enum',
    enum: ProductType,
    default: ProductType.PRODUTO,
  })
  type: ProductType;

  @Column({
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.ACTIVE,
  })
  status: ProductStatus;

  // Basic pricing and inventory
  @Column({ type: 'decimal', precision: 15, scale: 4 })
  unitPrice: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true })
  costPrice: number;

  @Column({ type: 'varchar', length: 10 })
  unit: string; // Unit of measurement (UN, KG, L, etc.)

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  weight: number; // Weight in KG

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  grossWeight: number; // Gross weight in KG

  // Tax information
  @Column({ type: 'varchar', length: 8 })
  ncmCode: string; // NCM (Nomenclatura Comum do Mercosul)

  @Column({ type: 'varchar', length: 4, nullable: true })
  cfopIntrastate: string; // CFOP for intrastate operations

  @Column({ type: 'varchar', length: 4, nullable: true })
  cfopInterstate: string; // CFOP for interstate operations

  @Column({
    type: 'enum',
    enum: TaxOrigin,
    default: TaxOrigin.NACIONAL,
  })
  taxOrigin: TaxOrigin;

  // ICMS configuration
  @Column({ type: 'varchar', length: 3, default: '102' })
  icmsCst: string; // ICMS Código de Situação Tributária

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  icmsRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  icmsReductionBase: number; // ICMS base reduction percentage

  // PIS configuration
  @Column({ type: 'varchar', length: 2, default: '01' })
  pisCst: string; // PIS Código de Situação Tributária

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  pisRate: number;

  // COFINS configuration
  @Column({ type: 'varchar', length: 2, default: '01' })
  cofinsCst: string; // COFINS Código de Situação Tributária

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  cofinsRate: number;

  // IPI configuration (if applicable)
  @Column({ type: 'varchar', length: 2, nullable: true })
  ipiCst: string; // IPI Código de Situação Tributária

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  ipiRate: number;

  @Column({ type: 'varchar', length: 4, nullable: true })
  ipiCode: string; // IPI Code

  // Additional product information
  @Column({ type: 'varchar', length: 100, nullable: true })
  brand: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  model: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  category: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  subcategory: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  imageUrl: string;

  @Column({ type: 'text', nullable: true })
  technicalSpecifications: string;

  @Column({ type: 'text', nullable: true })
  additionalInfo: string; // Additional information for NF-e

  // Inventory management
  @Column({ type: 'boolean', default: true })
  trackStock: boolean;

  @Column({ type: 'decimal', precision: 15, scale: 4, default: 0 })
  currentStock: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true })
  minimumStock: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true })
  maximumStock: number;

  // Multi-tenant relationship
  @Column({ type: 'uuid' })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', (businessUnit: any) => businessUnit.products, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Invoice items that use this product - using string reference to break circular dependency
  @OneToMany('InvoiceItem', (invoiceItem: any) => invoiceItem.product)
  invoiceItems: any[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === ProductStatus.ACTIVE;
  }

  get isService(): boolean {
    return this.type === ProductType.SERVICO;
  }

  get isProduct(): boolean {
    return this.type === ProductType.PRODUTO;
  }

  get formattedNCM(): string {
    if (!this.ncmCode || this.ncmCode.length !== 8) return this.ncmCode;
    return this.ncmCode.replace(/^(\d{4})(\d{4})$/, '$1.$2');
  }

  get hasStock(): boolean {
    return !this.trackStock || this.currentStock > 0;
  }

  get isLowStock(): boolean {
    return Boolean(
      this.trackStock &&
        this.minimumStock &&
        this.currentStock <= this.minimumStock,
    );
  }

  get stockStatus(): string {
    if (!this.trackStock) return 'not_tracked';
    if (this.currentStock <= 0) return 'out_of_stock';
    if (this.isLowStock) return 'low_stock';
    return 'in_stock';
  }

  canSell(quantity: number = 1): boolean {
    return this.isActive && (!this.trackStock || this.currentStock >= quantity);
  }

  calculateTotalTax(baseValue: number): number {
    return (
      (baseValue *
        (this.icmsRate + this.pisRate + this.cofinsRate + this.ipiRate)) /
      100
    );
  }

  getCFOPForState(destinationState: string, emitterState: string): string {
    const isInterstate = destinationState !== emitterState;
    return isInterstate
      ? this.cfopInterstate || '6102'
      : this.cfopIntrastate || '5102';
  }

  reduceStock(quantity: number): void {
    if (this.trackStock) {
      this.currentStock = Math.max(0, this.currentStock - quantity);
    }
  }

  increaseStock(quantity: number): void {
    if (this.trackStock) {
      this.currentStock += quantity;
    }
  }
}
