import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum TaxRegime {
  SIMPLES_NACIONAL = 'simples_nacional',
  LUCRO_PRESUMIDO = 'lucro_presumido',
  LUCRO_REAL = 'lucro_real',
  LUCRO_ARBITRADO = 'lucro_arbitrado',
}

export enum BusinessUnitStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  SETUP_PENDING = 'setup_pending',
}

@Entity('business_units')
@Index(['accountId'])
@Index(['cnpj'], { unique: true })
@Index(['status'])
export class BusinessUnit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  legalName: string; // Razão Social

  @Column({ type: 'varchar', length: 255, nullable: true })
  tradeName: string; // Nome Fantasia

  @Column({ type: 'varchar', length: 18, unique: true })
  cnpj: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  stateRegistration: string; // Inscrição Estadual

  @Column({ type: 'varchar', length: 20, nullable: true })
  municipalRegistration: string; // Inscrição Municipal

  @Column({
    type: 'enum',
    enum: TaxRegime,
    default: TaxRegime.SIMPLES_NACIONAL,
  })
  taxRegime: TaxRegime;

  @Column({
    type: 'enum',
    enum: BusinessUnitStatus,
    default: BusinessUnitStatus.SETUP_PENDING,
  })
  status: BusinessUnitStatus;

  // Address information
  @Column({ type: 'varchar', length: 10 })
  zipCode: string; // CEP

  @Column({ type: 'varchar', length: 255 })
  street: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  number: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  complement: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  district: string; // Bairro

  @Column({ type: 'varchar', length: 100 })
  city: string;

  @Column({ type: 'varchar', length: 2 })
  state: string;

  @Column({ type: 'varchar', length: 2, default: 'BR' })
  country: string;

  // Contact information
  @Column({ type: 'varchar', length: 255, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website: string;

  // Tax configuration
  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  icmsRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  pisRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  cofinsRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  issRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  irRate: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  csllRate: number;

  // Invoice numbering
  @Column({ type: 'int', default: 1 })
  nextInvoiceNumber: number;

  @Column({ type: 'int', default: 1 })
  invoiceSeries: number;

  @Column({ type: 'varchar', length: 50, default: 'PROD' })
  sefazEnvironment: string; // PROD or HOM

  // Logo and branding
  @Column({ type: 'varchar', length: 255, nullable: true })
  logoUrl: string;

  @Column({ type: 'varchar', length: 7, nullable: true })
  primaryColor: string; // Hex color

  @Column({ type: 'text', nullable: true })
  additionalInfo: string; // Additional information for DANFE

  // Business unit flags
  @Column({ type: 'boolean', default: false })
  isDefault: boolean; // Whether this is the default business unit for the account

  // Multi-tenant relationship
  @Column({ type: 'uuid' })
  accountId: string;

  @ManyToOne('Account', (account: any) => account.businessUnits, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: any;

  // Users who can access this business unit
  @OneToMany(() => User, (user) => user.currentBusinessUnit)
  users: User[];

  // Certificate for digital signing - using string reference to break circular dependency
  @OneToOne('Certificate', (certificate: any) => certificate.businessUnit, {
    cascade: true,
  })
  certificate: any; // Using any to avoid circular type reference

  // Entities managed by this business unit - using string references
  @OneToMany('Client', (client: any) => client.businessUnit, {
    cascade: true,
  })
  clients: any[];

  @OneToMany('Product', (product: any) => product.businessUnit, {
    cascade: true,
  })
  products: any[];

  @OneToMany('Carrier', (carrier: any) => carrier.businessUnit, {
    cascade: true,
  })
  carriers: any[];

  @OneToMany('PaymentTerm', (paymentTerm: any) => paymentTerm.businessUnit, {
    cascade: true,
  })
  paymentTerms: any[];

  @OneToMany('Invoice', (invoice: any) => invoice.businessUnit, {
    cascade: true,
  })
  invoices: any[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === BusinessUnitStatus.ACTIVE;
  }

  get isSetupComplete(): boolean {
    return this.status !== BusinessUnitStatus.SETUP_PENDING;
  }

  get hasCertificate(): boolean {
    return !!this.certificate && !this.certificate.isExpired;
  }

  get fullAddress(): string {
    const parts = [
      this.street,
      this.number,
      this.complement,
      this.district,
      this.city,
      this.state,
      this.zipCode,
    ].filter(Boolean);
    return parts.join(', ');
  }

  get formattedCnpj(): string {
    if (!this.cnpj) return '';
    return this.cnpj.replace(
      /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
      '$1.$2.$3/$4-$5',
    );
  }

  get canIssueInvoice(): boolean {
    return this.isActive && this.hasCertificate && this.isSetupComplete;
  }

  getNextInvoiceNumber(): number {
    return this.nextInvoiceNumber++;
  }
}
