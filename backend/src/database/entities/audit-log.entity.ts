import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';

export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  LOGIN = 'login',
  LOGOUT = 'logout',
  EXPORT = 'export',
  IMPORT = 'import',
  APPROVE = 'approve',
  REJECT = 'reject',
  CANCEL = 'cancel',
  SEND = 'send',
  DOWNLOAD = 'download',
  UPLOAD = 'upload',
  GENERATE = 'generate',
  REVOKE = 'revoke',
  SUSPEND = 'suspend',
  REACTIVATE = 'reactivate',
}

export enum AuditEntityType {
  USER = 'user',
  ACCOUNT = 'account',
  BUSINESS_UNIT = 'business_unit',
  CLIENT = 'client',
  PRODUCT = 'product',
  CARRIER = 'carrier',
  PAYMENT_TERM = 'payment_term',
  INVOICE = 'invoice',
  INVOICE_ITEM = 'invoice_item',
  CERTIFICATE = 'certificate',
  SUBSCRIPTION = 'subscription',
  API_KEY = 'api_key',
  SYSTEM = 'system',
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AuditCategory {
  SECURITY = 'security',
  DATA = 'data',
  FINANCIAL = 'financial',
  COMPLIANCE = 'compliance',
  SYSTEM = 'system',
  USER_ACTION = 'user_action',
  API = 'api',
  INTEGRATION = 'integration',
}

@Entity('audit_logs')
@Index(['accountId'])
@Index(['businessUnitId'])
@Index(['userId'])
@Index(['action'])
@Index(['entityType'])
@Index(['severity'])
@Index(['category'])
@Index(['performedAt'])
@Index(['entityId'])
export class AuditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Action information
  @Column({
    type: 'enum',
    enum: AuditAction,
  })
  action: AuditAction;

  @Column({
    type: 'enum',
    enum: AuditEntityType,
  })
  entityType: AuditEntityType;

  @Column({ type: 'uuid' })
  entityId: string; // ID of the entity that was affected

  @Column({ type: 'varchar', length: 255, nullable: true })
  entityName: string; // Human-readable name of the affected entity

  @Column({
    type: 'enum',
    enum: AuditSeverity,
    default: AuditSeverity.LOW,
  })
  severity: AuditSeverity;

  @Column({
    type: 'enum',
    enum: AuditCategory,
    default: AuditCategory.USER_ACTION,
  })
  category: AuditCategory;

  // Description and details
  @Column({ type: 'varchar', length: 500 })
  description: string; // Human-readable description of what happened

  @Column({ type: 'text', nullable: true })
  details: string; // Additional details in JSON format

  @Column({ type: 'text', nullable: true })
  oldValues: string; // Previous values (JSON) - for UPDATE actions

  @Column({ type: 'text', nullable: true })
  newValues: string; // New values (JSON) - for CREATE/UPDATE actions

  // Context information
  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string; // IP address from where the action was performed

  @Column({ type: 'varchar', length: 255, nullable: true })
  userAgent: string; // User agent of the client

  @Column({ type: 'varchar', length: 100, nullable: true })
  sessionId: string; // Session ID if available

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestId: string; // Request ID for tracing

  @Column({ type: 'varchar', length: 100, nullable: true })
  apiEndpoint: string; // API endpoint accessed (for API calls)

  @Column({ type: 'varchar', length: 10, nullable: true })
  httpMethod: string; // HTTP method used (GET, POST, etc.)

  @Column({ type: 'int', nullable: true })
  httpStatusCode: number; // HTTP response status code

  // Geolocation (optional)
  @Column({ type: 'varchar', length: 100, nullable: true })
  country: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  timezone: string;

  // Risk assessment
  @Column({ type: 'boolean', default: false })
  isSuspicious: boolean; // Whether this action was flagged as suspicious

  @Column({ type: 'varchar', length: 255, nullable: true })
  riskReason: string; // Reason why action was flagged as risky

  @Column({ type: 'int', default: 0 })
  riskScore: number; // Computed risk score (0-100)

  // Compliance and retention
  @Column({ type: 'boolean', default: false })
  isComplianceRelevant: boolean; // Whether this log is relevant for compliance

  @Column({ type: 'varchar', length: 100, nullable: true })
  complianceCategory: string; // Category of compliance (LGPD, SOX, etc.)

  @Column({ type: 'boolean', default: false })
  requiresRetention: boolean; // Whether this log must be retained longer

  @Column({ type: 'timestamp', nullable: true })
  retainUntil: Date; // Date until which this log must be retained

  // System information
  @Column({ type: 'varchar', length: 50, nullable: true })
  applicationVersion: string; // Version of the application when action occurred

  @Column({ type: 'varchar', length: 50, nullable: true })
  environment: string; // Environment (production, staging, development)

  @Column({ type: 'varchar', length: 100, nullable: true })
  module: string; // Application module where action occurred

  @Column({ type: 'varchar', length: 100, nullable: true })
  feature: string; // Specific feature or functionality used

  // Error information (for failed actions)
  @Column({ type: 'varchar', length: 255, nullable: true })
  errorCode: string; // Error code if action failed

  @Column({ type: 'text', nullable: true })
  errorMessage: string; // Error message if action failed

  @Column({ type: 'text', nullable: true })
  stackTrace: string; // Stack trace for debugging (sanitized)

  // Multi-tenant relationships
  @Column({ type: 'uuid', nullable: true })
  accountId: string;

  @ManyToOne('Account', {
    onDelete: 'SET NULL', // Keep audit logs even if account is deleted
  })
  @JoinColumn({ name: 'accountId' })
  account: any;

  @Column({ type: 'uuid', nullable: true })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', {
    onDelete: 'SET NULL', // Keep audit logs even if business unit is deleted
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // User who performed the action
  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @ManyToOne('User', (user: any) => user.auditLogs, {
    onDelete: 'SET NULL', // Keep audit logs even if user is deleted
  })
  @JoinColumn({ name: 'userId' })
  user: any;

  // When the action was performed
  @CreateDateColumn({ type: 'timestamp' })
  performedAt: Date;

  // Helper methods
  get isHighRisk(): boolean {
    return (
      this.severity === AuditSeverity.HIGH ||
      this.severity === AuditSeverity.CRITICAL
    );
  }

  get isSecurityRelevant(): boolean {
    return this.category === AuditCategory.SECURITY || this.isSuspicious;
  }

  get isDataChange(): boolean {
    return [
      AuditAction.CREATE,
      AuditAction.UPDATE,
      AuditAction.DELETE,
    ].includes(this.action);
  }

  get wasSuccessful(): boolean {
    return (
      !this.errorCode && (!this.httpStatusCode || this.httpStatusCode < 400)
    );
  }

  get severityDisplayName(): string {
    const severityNames: Record<AuditSeverity, string> = {
      [AuditSeverity.LOW]: 'Baixo',
      [AuditSeverity.MEDIUM]: 'Médio',
      [AuditSeverity.HIGH]: 'Alto',
      [AuditSeverity.CRITICAL]: 'Crítico',
    };
    return severityNames[this.severity];
  }

  get categoryDisplayName(): string {
    const categoryNames: Record<AuditCategory, string> = {
      [AuditCategory.SECURITY]: 'Segurança',
      [AuditCategory.DATA]: 'Dados',
      [AuditCategory.FINANCIAL]: 'Financeiro',
      [AuditCategory.COMPLIANCE]: 'Conformidade',
      [AuditCategory.SYSTEM]: 'Sistema',
      [AuditCategory.USER_ACTION]: 'Ação do Usuário',
      [AuditCategory.API]: 'API',
      [AuditCategory.INTEGRATION]: 'Integração',
    };
    return categoryNames[this.category];
  }

  get actionDisplayName(): string {
    const actionNames: Record<AuditAction, string> = {
      [AuditAction.CREATE]: 'Criar',
      [AuditAction.UPDATE]: 'Atualizar',
      [AuditAction.DELETE]: 'Excluir',
      [AuditAction.VIEW]: 'Visualizar',
      [AuditAction.LOGIN]: 'Login',
      [AuditAction.LOGOUT]: 'Logout',
      [AuditAction.EXPORT]: 'Exportar',
      [AuditAction.IMPORT]: 'Importar',
      [AuditAction.APPROVE]: 'Aprovar',
      [AuditAction.REJECT]: 'Rejeitar',
      [AuditAction.CANCEL]: 'Cancelar',
      [AuditAction.SEND]: 'Enviar',
      [AuditAction.DOWNLOAD]: 'Baixar',
      [AuditAction.UPLOAD]: 'Enviar Arquivo',
      [AuditAction.GENERATE]: 'Gerar',
      [AuditAction.REVOKE]: 'Revogar',
      [AuditAction.SUSPEND]: 'Suspender',
      [AuditAction.REACTIVATE]: 'Reativar',
    };
    return actionNames[this.action];
  }

  // Parse JSON fields safely
  getDetails(): Record<string, any> | null {
    try {
      return this.details ? JSON.parse(this.details) : null;
    } catch {
      return null;
    }
  }

  getOldValues(): Record<string, any> | null {
    try {
      return this.oldValues ? JSON.parse(this.oldValues) : null;
    } catch {
      return null;
    }
  }

  getNewValues(): Record<string, any> | null {
    try {
      return this.newValues ? JSON.parse(this.newValues) : null;
    } catch {
      return null;
    }
  }

  // Set JSON fields safely
  setDetails(data: Record<string, any>): void {
    this.details = JSON.stringify(data);
  }

  setOldValues(data: Record<string, any>): void {
    this.oldValues = JSON.stringify(data);
  }

  setNewValues(data: Record<string, any>): void {
    this.newValues = JSON.stringify(data);
  }

  // Check if retention period is still active
  isRetentionActive(): boolean {
    return (
      this.requiresRetention &&
      this.retainUntil &&
      this.retainUntil > new Date()
    );
  }

  // Flag as suspicious
  flagAsSuspicious(reason: string, score: number = 50): void {
    this.isSuspicious = true;
    this.riskReason = reason;
    this.riskScore = Math.min(Math.max(score, 0), 100); // Clamp between 0-100

    // Automatically increase severity for suspicious actions
    if (this.severity === AuditSeverity.LOW) {
      this.severity = AuditSeverity.MEDIUM;
    }
  }

  // Create audit log entry (static factory method)
  static createEntry(params: {
    action: AuditAction;
    entityType: AuditEntityType;
    entityId: string;
    entityName?: string;
    description: string;
    userId?: string;
    accountId?: string;
    businessUnitId?: string;
    ipAddress?: string;
    userAgent?: string;
    details?: Record<string, any>;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    severity?: AuditSeverity;
    category?: AuditCategory;
  }): AuditLog {
    const log = new AuditLog();

    log.action = params.action;
    log.entityType = params.entityType;
    log.entityId = params.entityId;
    if (params.entityName) {
      log.entityName = params.entityName;
    }
    log.description = params.description;
    log.severity = params.severity || AuditSeverity.LOW;
    log.category = params.category || AuditCategory.USER_ACTION;

    if (params.userId) log.userId = params.userId;
    if (params.accountId) log.accountId = params.accountId;
    if (params.businessUnitId) log.businessUnitId = params.businessUnitId;
    if (params.ipAddress) log.ipAddress = params.ipAddress;
    if (params.userAgent) log.userAgent = params.userAgent;

    if (params.details) log.setDetails(params.details);
    if (params.oldValues) log.setOldValues(params.oldValues);
    if (params.newValues) log.setNewValues(params.newValues);

    return log;
  }

  // Create security event log
  static createSecurityEvent(params: {
    action: AuditAction;
    description: string;
    userId?: string;
    accountId?: string;
    ipAddress?: string;
    userAgent?: string;
    severity?: AuditSeverity;
    details?: Record<string, any>;
  }): AuditLog {
    return AuditLog.createEntry({
      ...params,
      entityType: AuditEntityType.SYSTEM,
      entityId: 'security-system',
      entityName: 'Security System',
      category: AuditCategory.SECURITY,
      severity: params.severity || AuditSeverity.MEDIUM,
    });
  }

  // Create compliance event log
  static createComplianceEvent(params: {
    action: AuditAction;
    entityType: AuditEntityType;
    entityId: string;
    description: string;
    userId?: string;
    accountId?: string;
    complianceCategory?: string;
    details?: Record<string, any>;
  }): AuditLog {
    const log = AuditLog.createEntry({
      ...params,
      category: AuditCategory.COMPLIANCE,
      severity: AuditSeverity.MEDIUM,
    });

    log.isComplianceRelevant = true;
    log.requiresRetention = true;
    if (params.complianceCategory) {
      log.complianceCategory = params.complianceCategory;
    }

    // Set retention period (7 years for compliance)
    const retentionDate = new Date();
    retentionDate.setFullYear(retentionDate.getFullYear() + 7);
    log.retainUntil = retentionDate;

    return log;
  }
}
