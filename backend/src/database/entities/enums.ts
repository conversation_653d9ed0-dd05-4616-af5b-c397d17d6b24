// User related enums
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  OPERATOR = 'operator',
  VIEWER = 'viewer',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification',
}

// Account related enums
export enum AccountStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  TRIAL = 'trial',
}

// Subscription related enums
export enum SubscriptionTier {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  TRIALING = 'trialing',
  TRIAL = 'trial',
}

export enum SubscriptionBillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

// Business Unit related enums
export enum BusinessUnitStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  SETUP_PENDING = 'setup_pending',
}

export enum CompanyType {
  MEI = 'mei', // Microempreendedor Individual
  LTDA = 'ltda', // Sociedade Limitada
  SA = 'sa', // Sociedade Anônima
  EIRELI = 'eireli', // Empresa Individual de Responsabilidade Limitada
  EI = 'ei', // Empresário Individual
  OTHER = 'other',
}

// Invoice related enums
export enum InvoiceStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  ISSUED = 'issued',
  SENT = 'sent',
  RECEIVED = 'received',
  CANCELLED = 'cancelled',
  ERROR = 'error',
}

export enum InvoiceType {
  NFE = 'nfe', // Nota Fiscal Eletrônica
  NFCE = 'nfce', // Nota Fiscal de Consumidor Eletrônica
  NFSE = 'nfse', // Nota Fiscal de Serviços Eletrônica
}

// Certificate related enums
export enum CertificateStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

export enum CertificateType {
  A1 = 'a1', // Software certificate
  A3 = 'a3', // Hardware certificate (smart card/USB token)
}

// API Key related enums
export enum ApiKeyStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  REVOKED = 'revoked',
}

// Audit Log related enums
export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  LOGIN = 'login',
  LOGOUT = 'logout',
  EXPORT = 'export',
  IMPORT = 'import',
  APPROVE = 'approve',
  REJECT = 'reject',
  CANCEL = 'cancel',
  SEND = 'send',
  DOWNLOAD = 'download',
  UPLOAD = 'upload',
  GENERATE = 'generate',
  REVOKE = 'revoke',
  SUSPEND = 'suspend',
  REACTIVATE = 'reactivate',
}

export enum AuditEntityType {
  USER = 'user',
  ACCOUNT = 'account',
  BUSINESS_UNIT = 'business_unit',
  CLIENT = 'client',
  PRODUCT = 'product',
  CARRIER = 'carrier',
  PAYMENT_TERM = 'payment_term',
  INVOICE = 'invoice',
  INVOICE_ITEM = 'invoice_item',
  CERTIFICATE = 'certificate',
  SUBSCRIPTION = 'subscription',
  API_KEY = 'api_key',
  SYSTEM = 'system',
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AuditCategory {
  SECURITY = 'security',
  DATA = 'data',
  FINANCIAL = 'financial',
  COMPLIANCE = 'compliance',
  SYSTEM = 'system',
  USER_ACTION = 'user_action',
  API = 'api',
  INTEGRATION = 'integration',
}

// Notification related enums
export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export enum NotificationCategory {
  SYSTEM = 'system',
  INVOICE = 'invoice',
  CERTIFICATE = 'certificate',
  SUBSCRIPTION = 'subscription',
  PAYMENT = 'payment',
  COMPLIANCE = 'compliance',
  SECURITY = 'security',
  INTEGRATION = 'integration',
  USER_ACTION = 'user_action',
  MAINTENANCE = 'maintenance',
}

export enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  WEBHOOK = 'webhook',
  SLACK = 'slack',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// Brazilian Validation related enums
export enum CNPJStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  NULL = 'null',
  INVALID = 'invalid',
}

export enum CompanySize {
  MEI = 'mei', // Microempreendedor Individual
  ME = 'me', // Microempresa
  EPP = 'epp', // Empresa de Pequeno Porte
  MEDIUM = 'medium', // Empresa de Médio Porte
  LARGE = 'large', // Empresa de Grande Porte
}

export enum TaxRegime {
  SIMPLES_NACIONAL = 'simples_nacional',
  LUCRO_PRESUMIDO = 'lucro_presumido',
  LUCRO_REAL = 'lucro_real',
  LUCRO_ARBITRADO = 'lucro_arbitrado',
  ISENTO = 'isento',
}

export enum CEPStatus {
  VALID = 'valid',
  INVALID = 'invalid',
  NOT_FOUND = 'not_found',
  ERROR = 'error',
}
