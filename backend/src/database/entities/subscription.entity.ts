import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';

export enum SubscriptionTier {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  TRIAL = 'trial',
  PAST_DUE = 'past_due',
  CANCELLED = 'cancelled',
  SUSPENDED = 'suspended',
  EXPIRED = 'expired',
}

export enum BillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUALLY = 'annually',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  OVERDUE = 'overdue',
  REFUNDED = 'refunded',
}

@Entity('subscriptions')
@Index(['accountId'], { unique: true })
@Index(['status'])
@Index(['tier'])
@Index(['currentPeriodEnd'])
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Subscription configuration
  @Column({
    type: 'enum',
    enum: SubscriptionTier,
    default: SubscriptionTier.FREE,
  })
  tier: SubscriptionTier;

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.TRIAL,
  })
  status: SubscriptionStatus;

  @Column({
    type: 'enum',
    enum: BillingCycle,
    default: BillingCycle.MONTHLY,
  })
  billingCycle: BillingCycle;

  // Pricing and billing
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  monthlyPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  currentPrice: number; // Current price (may differ due to promotions)

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  discountPercentage: number; // Discount applied to current subscription

  @Column({ type: 'varchar', length: 100, nullable: true })
  promoCode: string; // Applied promo code

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  setupFee: number; // One-time setup fee

  // Billing periods
  @Column({ type: 'timestamp' })
  currentPeriodStart: Date;

  @Column({ type: 'timestamp' })
  currentPeriodEnd: Date;

  @Column({ type: 'timestamp', nullable: true })
  nextBillingDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastBillingDate: Date;

  // Trial information
  @Column({ type: 'boolean', default: false })
  isTrialUsed: boolean; // Whether trial period has been used

  @Column({ type: 'timestamp', nullable: true })
  trialStart: Date;

  @Column({ type: 'timestamp', nullable: true })
  trialEnd: Date;

  @Column({ type: 'int', default: 14 })
  trialDays: number; // Trial period length in days

  // Usage limits and tracking
  @Column({ type: 'int', default: 100 })
  invoiceLimitPerMonth: number;

  @Column({ type: 'int', default: 1000 })
  apiCallLimitPerMonth: number;

  @Column({ type: 'int', default: 1 })
  maxUsers: number;

  @Column({ type: 'int', default: 1 })
  maxBusinessUnits: number;

  @Column({ type: 'int', default: 1000 })
  maxProducts: number;

  @Column({ type: 'int', default: 1000 })
  maxClients: number;

  @Column({ type: 'boolean', default: false })
  allowApiAccess: boolean;

  @Column({ type: 'boolean', default: false })
  allowBulkOperations: boolean;

  @Column({ type: 'boolean', default: false })
  allowAdvancedReports: boolean;

  @Column({ type: 'boolean', default: false })
  allowCustomBranding: boolean;

  @Column({ type: 'boolean', default: false })
  allowPrioritySupport: boolean;

  // Current usage (reset monthly)
  @Column({ type: 'int', default: 0 })
  currentInvoiceUsage: number;

  @Column({ type: 'int', default: 0 })
  currentApiCallUsage: number;

  @Column({ type: 'date', nullable: true })
  usageResetDate: Date; // Last date when usage counters were reset

  // Payment information
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  paymentStatus: PaymentStatus;

  @Column({ type: 'varchar', length: 255, nullable: true })
  paymentMethodId: string; // External payment method ID (Stripe, etc.)

  @Column({ type: 'varchar', length: 100, nullable: true })
  paymentProvider: string; // Stripe, PayPal, etc.

  @Column({ type: 'varchar', length: 255, nullable: true })
  lastPaymentId: string; // Last payment transaction ID

  @Column({ type: 'timestamp', nullable: true })
  lastPaymentAt: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  lastPaymentAmount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastFailedPaymentAt: Date;

  @Column({ type: 'text', nullable: true })
  lastPaymentError: string;

  @Column({ type: 'int', default: 0 })
  failedPaymentAttempts: number;

  // Subscription lifecycle
  @Column({ type: 'timestamp', nullable: true })
  activatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancelledAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  suspendedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiredAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cancellationReason?: string;

  @Column({ type: 'boolean', default: false })
  cancelAtPeriodEnd: boolean; // Cancel at the end of current period

  // Upgrade/downgrade tracking
  @Column({ type: 'varchar', length: 50, nullable: true })
  previousTier: string;

  @Column({ type: 'timestamp', nullable: true })
  lastTierChangeAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  pendingTierChange?: string; // Tier to change to at next billing period

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  prorationAmount: number; // Proration amount for mid-cycle changes

  // Invoice and billing
  @Column({ type: 'varchar', length: 255, nullable: true })
  billingEmail: string;

  @Column({ type: 'boolean', default: true })
  autoRenew: boolean;

  @Column({ type: 'int', default: 3 })
  gracePeriodDays: number; // Days before suspension after failed payment

  @Column({ type: 'boolean', default: true })
  emailNotifications: boolean; // Send billing/usage notifications

  // Add-ons and extras
  @Column({ type: 'text', nullable: true })
  addOns: string; // JSON array of active add-ons

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  addOnsCost: number; // Monthly cost of add-ons

  // External system integration
  @Column({ type: 'varchar', length: 255, nullable: true })
  externalSubscriptionId: string; // ID in external billing system

  @Column({ type: 'text', nullable: true })
  externalMetadata: string; // Additional metadata from external system

  // Account relationship (one-to-one)
  @Column({ type: 'uuid' })
  accountId: string;

  @OneToOne('Account', (account: any) => account.subscription, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: any;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === SubscriptionStatus.ACTIVE;
  }

  get isOnTrial(): boolean {
    return this.status === SubscriptionStatus.TRIAL && !this.isTrialExpired;
  }

  get isCancelled(): boolean {
    return this.status === SubscriptionStatus.CANCELLED;
  }

  get isSuspended(): boolean {
    return this.status === SubscriptionStatus.SUSPENDED;
  }

  get isExpired(): boolean {
    return (
      this.status === SubscriptionStatus.EXPIRED ||
      this.currentPeriodEnd < new Date()
    );
  }

  get isTrialExpired(): boolean {
    return this.trialEnd ? this.trialEnd < new Date() : false;
  }

  get isPastDue(): boolean {
    return this.status === SubscriptionStatus.PAST_DUE;
  }

  get canUseService(): boolean {
    return (
      (this.isActive || this.isOnTrial) && !this.isSuspended && !this.isExpired
    );
  }

  get daysUntilExpiration(): number {
    if (this.isExpired) return 0;
    const now = new Date();
    const diffTime = this.currentPeriodEnd.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get trialDaysRemaining(): number {
    if (!this.isOnTrial || !this.trialEnd) return 0;
    const now = new Date();
    const diffTime = this.trialEnd.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }

  get tierDisplayName(): string {
    const tierNames: Record<SubscriptionTier, string> = {
      [SubscriptionTier.FREE]: 'Gratuito',
      [SubscriptionTier.BASIC]: 'Básico',
      [SubscriptionTier.PROFESSIONAL]: 'Profissional',
      [SubscriptionTier.ENTERPRISE]: 'Empresarial',
    };
    return tierNames[this.tier];
  }

  get billingCycleDisplayName(): string {
    const cycleNames: Record<BillingCycle, string> = {
      [BillingCycle.MONTHLY]: 'Mensal',
      [BillingCycle.QUARTERLY]: 'Trimestral',
      [BillingCycle.ANNUALLY]: 'Anual',
    };
    return cycleNames[this.billingCycle];
  }

  // Usage validation
  get isOverInvoiceLimit(): boolean {
    return this.currentInvoiceUsage >= this.invoiceLimitPerMonth;
  }

  get isOverApiCallLimit(): boolean {
    return this.currentApiCallUsage >= this.apiCallLimitPerMonth;
  }

  get invoiceUsagePercentage(): number {
    return (this.currentInvoiceUsage / this.invoiceLimitPerMonth) * 100;
  }

  get apiCallUsagePercentage(): number {
    return (this.currentApiCallUsage / this.apiCallLimitPerMonth) * 100;
  }

  // Check if feature is available for current tier
  hasFeature(feature: string): boolean {
    const tierFeatures: Record<SubscriptionTier, string[]> = {
      [SubscriptionTier.FREE]: ['basic_invoicing'],
      [SubscriptionTier.BASIC]: [
        'basic_invoicing',
        'api_access',
        'multiple_users',
      ],
      [SubscriptionTier.PROFESSIONAL]: [
        'basic_invoicing',
        'api_access',
        'multiple_users',
        'bulk_operations',
        'advanced_reports',
        'custom_branding',
      ],
      [SubscriptionTier.ENTERPRISE]: [
        'basic_invoicing',
        'api_access',
        'multiple_users',
        'bulk_operations',
        'advanced_reports',
        'custom_branding',
        'priority_support',
        'unlimited_usage',
      ],
    };

    return tierFeatures[this.tier]?.includes(feature) || false;
  }

  // Calculate next billing amount
  calculateNextBillingAmount(): number {
    const baseAmount = this.currentPrice;
    const discount = baseAmount * (this.discountPercentage / 100);
    return baseAmount - discount + this.addOnsCost;
  }

  // Update usage counters
  incrementInvoiceUsage(count: number = 1): boolean {
    if (this.hasFeature('unlimited_usage')) {
      this.currentInvoiceUsage += count;
      return true;
    }

    if (this.currentInvoiceUsage + count > this.invoiceLimitPerMonth) {
      return false; // Would exceed limit
    }

    this.currentInvoiceUsage += count;
    return true;
  }

  incrementApiCallUsage(count: number = 1): boolean {
    if (this.hasFeature('unlimited_usage')) {
      this.currentApiCallUsage += count;
      return true;
    }

    if (this.currentApiCallUsage + count > this.apiCallLimitPerMonth) {
      return false; // Would exceed limit
    }

    this.currentApiCallUsage += count;
    return true;
  }

  // Reset monthly usage counters
  resetUsageCounters(): void {
    this.currentInvoiceUsage = 0;
    this.currentApiCallUsage = 0;
    this.usageResetDate = new Date();
  }

  // Upgrade/downgrade subscription
  changeTier(newTier: SubscriptionTier, immediate: boolean = false): void {
    if (immediate) {
      this.previousTier = this.tier;
      this.tier = newTier;
      this.lastTierChangeAt = new Date();
      this.pendingTierChange = undefined;
      this.updateLimitsForTier(newTier);
    } else {
      this.pendingTierChange = newTier;
    }
  }

  // Apply tier-specific limits
  private updateLimitsForTier(tier: SubscriptionTier): void {
    const tierLimits: Record<SubscriptionTier, any> = {
      [SubscriptionTier.FREE]: {
        invoiceLimitPerMonth: 10,
        apiCallLimitPerMonth: 100,
        maxUsers: 1,
        maxBusinessUnits: 1,
        maxProducts: 50,
        maxClients: 50,
      },
      [SubscriptionTier.BASIC]: {
        invoiceLimitPerMonth: 100,
        apiCallLimitPerMonth: 1000,
        maxUsers: 3,
        maxBusinessUnits: 2,
        maxProducts: 500,
        maxClients: 500,
      },
      [SubscriptionTier.PROFESSIONAL]: {
        invoiceLimitPerMonth: 1000,
        apiCallLimitPerMonth: 10000,
        maxUsers: 10,
        maxBusinessUnits: 5,
        maxProducts: 5000,
        maxClients: 5000,
      },
      [SubscriptionTier.ENTERPRISE]: {
        invoiceLimitPerMonth: -1, // Unlimited
        apiCallLimitPerMonth: -1, // Unlimited
        maxUsers: -1, // Unlimited
        maxBusinessUnits: -1, // Unlimited
        maxProducts: -1, // Unlimited
        maxClients: -1, // Unlimited
      },
    };

    const limits = tierLimits[tier];
    if (limits) {
      this.invoiceLimitPerMonth = limits.invoiceLimitPerMonth;
      this.apiCallLimitPerMonth = limits.apiCallLimitPerMonth;
      this.maxUsers = limits.maxUsers;
      this.maxBusinessUnits = limits.maxBusinessUnits;
      this.maxProducts = limits.maxProducts;
      this.maxClients = limits.maxClients;
    }
  }

  // Cancel subscription
  cancel(reason?: string, immediate: boolean = false): void {
    this.cancellationReason = reason || 'User requested cancellation';
    this.cancelledAt = new Date();

    if (immediate) {
      this.status = SubscriptionStatus.CANCELLED;
    } else {
      this.cancelAtPeriodEnd = true;
    }
  }

  // Reactivate subscription
  reactivate(): void {
    if (this.isCancelled) {
      this.status = SubscriptionStatus.ACTIVE;
      this.cancelledAt = undefined;
      this.cancellationReason = undefined;
      this.cancelAtPeriodEnd = false;
      this.activatedAt = new Date();
    }
  }

  // Extend subscription period
  extendPeriod(days: number): void {
    this.currentPeriodEnd = new Date(
      this.currentPeriodEnd.getTime() + days * 24 * 60 * 60 * 1000,
    );
    if (this.nextBillingDate) {
      this.nextBillingDate = new Date(
        this.nextBillingDate.getTime() + days * 24 * 60 * 60 * 1000,
      );
    }
  }

  // Check if subscription needs renewal
  needsRenewal(): boolean {
    if (!this.autoRenew) return false;
    const daysUntilExpiration = this.daysUntilExpiration;
    return daysUntilExpiration <= 7 && daysUntilExpiration > 0;
  }
}
