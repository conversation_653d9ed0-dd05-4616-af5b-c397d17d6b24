import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { TaxOrigin } from './product.entity';

@Entity('invoice_items')
@Index(['invoiceId'])
@Index(['productId'])
@Index(['itemSequence'])
export class InvoiceItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int' })
  itemSequence: number; // Sequential item number in the invoice

  // Product information (can be from Product entity or directly entered)
  @Column({ type: 'uuid', nullable: true })
  productId: string;

  @Column({ type: 'varchar', length: 100 })
  productName: string;

  @Column({ type: 'text', nullable: true })
  productDescription: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  productSku: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  productBarcode: string; // EAN/GTIN

  // Quantities and measurements
  @Column({ type: 'decimal', precision: 15, scale: 4 })
  quantity: number;

  @Column({ type: 'varchar', length: 10 })
  unit: string; // Unit of measurement

  @Column({ type: 'decimal', precision: 15, scale: 4 })
  unitPrice: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, default: 0 })
  discountValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 4 })
  totalValue: number; // (quantity * unitPrice) - discountValue

  // Weight information
  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  unitWeight: number; // Weight per unit in KG

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  totalWeight: number; // Total weight for this item

  // Tax information
  @Column({ type: 'varchar', length: 8 })
  ncmCode: string; // NCM code

  @Column({ type: 'varchar', length: 4 })
  cfopCode: string; // CFOP code used for this item

  @Column({
    type: 'enum',
    enum: TaxOrigin,
    default: TaxOrigin.NACIONAL,
  })
  taxOrigin: TaxOrigin;

  // ICMS calculation
  @Column({ type: 'varchar', length: 3 })
  icmsCst: string; // ICMS CST

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  icmsRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  icmsBaseValue: number; // Base value for ICMS calculation

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  icmsValue: number; // Calculated ICMS value

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  icmsReductionBase: number; // Base reduction percentage

  // PIS calculation
  @Column({ type: 'varchar', length: 2 })
  pisCst: string; // PIS CST

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  pisRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  pisBaseValue: number; // Base value for PIS calculation

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  pisValue: number; // Calculated PIS value

  // COFINS calculation
  @Column({ type: 'varchar', length: 2 })
  cofinsCst: string; // COFINS CST

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  cofinsRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cofinsBaseValue: number; // Base value for COFINS calculation

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cofinsValue: number; // Calculated COFINS value

  // IPI calculation (if applicable)
  @Column({ type: 'varchar', length: 2, nullable: true })
  ipiCst: string; // IPI CST

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  ipiRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  ipiBaseValue: number; // Base value for IPI calculation

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  ipiValue: number; // Calculated IPI value

  @Column({ type: 'varchar', length: 4, nullable: true })
  ipiCode: string; // IPI code

  // ISS calculation (for services)
  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  issRate: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  issBaseValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  issValue: number;

  // Additional information
  @Column({ type: 'text', nullable: true })
  additionalInfo: string; // Additional information for this item

  @Column({ type: 'text', nullable: true })
  technicalSpecifications: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  batch: string; // Batch/lot number

  @Column({ type: 'date', nullable: true })
  expirationDate: Date; // Expiration date for perishable items

  @Column({ type: 'date', nullable: true })
  manufacturingDate: Date; // Manufacturing date

  // Import information (if applicable)
  @Column({ type: 'varchar', length: 50, nullable: true })
  importDeclaration: string; // DI number

  @Column({ type: 'date', nullable: true })
  importDate: Date;

  @Column({ type: 'varchar', length: 100, nullable: true })
  importSupplier: string;

  // Fuel information (if applicable to fuel products)
  @Column({ type: 'varchar', length: 50, nullable: true })
  fuelCode: string; // ANP fuel code

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true })
  fuelMixPercentage: number; // Mix percentage for biofuels

  // Relationships
  @Column({ type: 'uuid' })
  invoiceId: string;

  @ManyToOne('Invoice', (invoice: any) => invoice.items, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'invoiceId' })
  invoice: any;

  @ManyToOne('Product', (product: any) => product.invoiceItems, {
    nullable: true,
  })
  @JoinColumn({ name: 'productId' })
  product: any;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get formattedNCM(): string {
    if (!this.ncmCode || this.ncmCode.length !== 8) return this.ncmCode;
    return this.ncmCode.replace(/^(\d{4})(\d{4})$/, '$1.$2');
  }

  get totalTaxValue(): number {
    return (
      this.icmsValue +
      this.pisValue +
      this.cofinsValue +
      this.ipiValue +
      this.issValue
    );
  }

  get totalValueWithTax(): number {
    return this.totalValue + this.totalTaxValue;
  }

  get netUnitPrice(): number {
    if (this.quantity === 0) return 0;
    return (this.totalValue - this.discountValue) / this.quantity;
  }

  get discountPercentage(): number {
    if (this.totalValue === 0) return 0;
    return (this.discountValue / (this.totalValue + this.discountValue)) * 100;
  }

  get effectiveTaxRate(): number {
    if (this.totalValue === 0) return 0;
    return (this.totalTaxValue / this.totalValue) * 100;
  }

  // Calculate taxes based on tax configuration
  calculateTaxes(): void {
    const baseValue = this.totalValue; // Use total value as base for most taxes

    // ICMS calculation
    if (this.icmsRate > 0) {
      this.icmsBaseValue = this.icmsReductionBase
        ? baseValue * (1 - this.icmsReductionBase / 100)
        : baseValue;
      this.icmsValue = (this.icmsBaseValue * this.icmsRate) / 100;
    } else {
      this.icmsBaseValue = 0;
      this.icmsValue = 0;
    }

    // PIS calculation
    if (this.pisRate > 0) {
      this.pisBaseValue = baseValue;
      this.pisValue = (this.pisBaseValue * this.pisRate) / 100;
    } else {
      this.pisBaseValue = 0;
      this.pisValue = 0;
    }

    // COFINS calculation
    if (this.cofinsRate > 0) {
      this.cofinsBaseValue = baseValue;
      this.cofinsValue = (this.cofinsBaseValue * this.cofinsRate) / 100;
    } else {
      this.cofinsBaseValue = 0;
      this.cofinsValue = 0;
    }

    // IPI calculation
    if (this.ipiRate > 0) {
      this.ipiBaseValue = baseValue;
      this.ipiValue = (this.ipiBaseValue * this.ipiRate) / 100;
    } else {
      this.ipiBaseValue = 0;
      this.ipiValue = 0;
    }

    // ISS calculation (for services)
    if (this.issRate > 0) {
      this.issBaseValue = baseValue;
      this.issValue = (this.issBaseValue * this.issRate) / 100;
    } else {
      this.issBaseValue = 0;
      this.issValue = 0;
    }

    // Round all values to 2 decimal places
    this.icmsValue = Math.round(this.icmsValue * 100) / 100;
    this.pisValue = Math.round(this.pisValue * 100) / 100;
    this.cofinsValue = Math.round(this.cofinsValue * 100) / 100;
    this.ipiValue = Math.round(this.ipiValue * 100) / 100;
    this.issValue = Math.round(this.issValue * 100) / 100;
  }

  // Update total value based on quantity and unit price
  updateTotalValue(): void {
    this.totalValue = this.quantity * this.unitPrice - this.discountValue;
    this.totalWeight = this.quantity * this.unitWeight;

    // Recalculate taxes after updating total value
    this.calculateTaxes();
  }

  // Copy tax configuration from product
  copyTaxConfigFromProduct(): void {
    if (!this.product) return;

    this.ncmCode = this.product.ncmCode;
    this.taxOrigin = this.product.taxOrigin;

    // ICMS
    this.icmsCst = this.product.icmsCst;
    this.icmsRate = this.product.icmsRate;
    this.icmsReductionBase = this.product.icmsReductionBase;

    // PIS
    this.pisCst = this.product.pisCst;
    this.pisRate = this.product.pisRate;

    // COFINS
    this.cofinsCst = this.product.cofinsCst;
    this.cofinsRate = this.product.cofinsRate;

    // IPI
    this.ipiCst = this.product.ipiCst;
    this.ipiRate = this.product.ipiRate;
    this.ipiCode = this.product.ipiCode;
  }

  // Set CFOP based on operation type and client location
  setCFOP(clientState: string, emitterState: string): void {
    if (!this.product) {
      // Default CFOPs for manual entry
      this.cfopCode = clientState === emitterState ? '5102' : '6102';
      return;
    }

    this.cfopCode = this.product.getCFOPForState(clientState, emitterState);
  }

  // Validate item data
  validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.quantity <= 0) {
      errors.push('Quantity must be greater than 0');
    }

    if (this.unitPrice < 0) {
      errors.push('Unit price cannot be negative');
    }

    if (!this.productName?.trim()) {
      errors.push('Product name is required');
    }

    if (!this.unit?.trim()) {
      errors.push('Unit of measurement is required');
    }

    if (!this.ncmCode || this.ncmCode.length !== 8) {
      errors.push('NCM code must have 8 digits');
    }

    if (!this.cfopCode || this.cfopCode.length !== 4) {
      errors.push('CFOP code must have 4 digits');
    }

    if (this.discountValue > this.quantity * this.unitPrice) {
      errors.push('Discount cannot be greater than gross value');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
