import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { DocumentType, PersonType } from '../shared/types/brazilian-types';

export enum ClientStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BLOCKED = 'blocked',
}

export enum ClientType {
  PESSOA_FISICA = 'pessoa_fisica',
  PESSOA_JURIDICA = 'pessoa_juridica',
  ESTRANGEIRA = 'estrangeira',
}

@Entity('clients')
@Index(['businessUnitId'])
@Index(['documentNumber'])
@Index(['status'])
@Index(['businessUnitId', 'documentNumber'], { unique: true })
export class Client {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  legalName: string; // Razão Social (for companies)

  @Column({
    type: 'enum',
    enum: ClientType,
    default: ClientType.PESSOA_JURIDICA,
  })
  type: ClientType;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.CNPJ,
  })
  documentType: DocumentType;

  @Column({ type: 'varchar', length: 20 })
  documentNumber: string; // CNPJ or CPF (numbers only)

  @Column({ type: 'varchar', length: 20, nullable: true })
  stateRegistration: string; // Inscrição Estadual

  @Column({ type: 'varchar', length: 20, nullable: true })
  municipalRegistration: string; // Inscrição Municipal

  @Column({
    type: 'enum',
    enum: ClientStatus,
    default: ClientStatus.ACTIVE,
  })
  status: ClientStatus;

  // Contact information
  @Column({ type: 'varchar', length: 255, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  mobile: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  contactPerson: string; // Main contact person

  // Primary address
  @Column({ type: 'varchar', length: 10 })
  zipCode: string; // CEP

  @Column({ type: 'varchar', length: 255 })
  street: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  number: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  complement: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  district: string; // Bairro

  @Column({ type: 'varchar', length: 100 })
  city: string;

  @Column({ type: 'varchar', length: 2 })
  state: string;

  @Column({ type: 'varchar', length: 2, default: 'BR' })
  country: string;

  @Column({ type: 'varchar', length: 7, nullable: true })
  ibgeCode: string; // IBGE municipality code

  // Billing address (if different from primary)
  @Column({ type: 'boolean', default: false })
  hasDifferentBillingAddress: boolean;

  @Column({ type: 'varchar', length: 10, nullable: true })
  billingZipCode: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  billingStreet: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  billingNumber: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  billingComplement: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  billingDistrict: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  billingCity: string;

  @Column({ type: 'varchar', length: 2, nullable: true })
  billingState: string;

  @Column({ type: 'varchar', length: 2, nullable: true })
  billingCountry: string;

  @Column({ type: 'varchar', length: 7, nullable: true })
  billingIbgeCode: string;

  // Tax classification
  @Column({ type: 'boolean', default: false })
  isIcmsContributor: boolean; // ICMS taxpayer

  @Column({ type: 'boolean', default: false })
  isSimplestNacional: boolean; // Simples Nacional

  @Column({ type: 'boolean', default: false })
  isIssContributor: boolean; // ISS taxpayer

  @Column({ type: 'varchar', length: 50, nullable: true })
  taxRegime: string;

  // Credit information
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  creditLimit: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  currentBalance: number;

  @Column({ type: 'int', default: 30 })
  defaultPaymentTermDays: number;

  // Additional information
  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  observations: string; // For NF-e observations

  // Multi-tenant relationship
  @Column({ type: 'uuid' })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', (businessUnit: any) => businessUnit.clients, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Invoices issued to this client - using string reference to break circular dependency
  @OneToMany('Invoice', (invoice: any) => invoice.client)
  invoices: any[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === ClientStatus.ACTIVE;
  }

  get isPerson(): boolean {
    return this.type === ClientType.PESSOA_FISICA;
  }

  get isCompany(): boolean {
    return this.type === ClientType.PESSOA_JURIDICA;
  }

  get isForeign(): boolean {
    return this.type === ClientType.ESTRANGEIRA;
  }

  get formattedDocument(): string {
    if (
      this.documentType === DocumentType.CNPJ &&
      this.documentNumber?.length === 14
    ) {
      return this.documentNumber.replace(
        /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
        '$1.$2.$3/$4-$5',
      );
    }
    if (
      this.documentType === DocumentType.CPF &&
      this.documentNumber?.length === 11
    ) {
      return this.documentNumber.replace(
        /^(\d{3})(\d{3})(\d{3})(\d{2})$/,
        '$1.$2.$3-$4',
      );
    }
    return this.documentNumber || '';
  }

  get fullAddress(): string {
    const parts = [
      this.street,
      this.number,
      this.complement,
      this.district,
      this.city,
      this.state,
      this.zipCode,
    ].filter(Boolean);
    return parts.join(', ');
  }

  get billingAddress(): string {
    if (!this.hasDifferentBillingAddress) {
      return this.fullAddress;
    }

    const parts = [
      this.billingStreet,
      this.billingNumber,
      this.billingComplement,
      this.billingDistrict,
      this.billingCity,
      this.billingState,
      this.billingZipCode,
    ].filter(Boolean);
    return parts.join(', ');
  }

  get availableCredit(): number {
    return this.creditLimit - this.currentBalance;
  }

  get hasAvailableCredit(): boolean {
    return this.availableCredit > 0;
  }

  canIssueInvoice(): boolean {
    return this.isActive;
  }
}
