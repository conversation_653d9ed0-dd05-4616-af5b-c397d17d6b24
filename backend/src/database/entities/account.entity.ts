import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Subscription } from './subscription.entity';
import { ApiKey } from './api-key.entity';

export enum AccountStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  TRIAL = 'trial',
}

export enum AccountType {
  INDIVIDUAL = 'individual',
  BUSINESS = 'business',
  ENTERPRISE = 'enterprise',
}

@Entity('accounts')
@Index(['status'])
@Index(['type'])
export class Account {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({
    type: 'enum',
    enum: AccountType,
    default: AccountType.BUSINESS,
  })
  type: AccountType;

  @Column({
    type: 'enum',
    enum: AccountStatus,
    default: AccountStatus.TRIAL,
  })
  status: AccountStatus;

  @Column({ type: 'varchar', length: 255, nullable: true })
  companyName: string;

  @Column({ type: 'varchar', length: 18, nullable: true })
  cnpj: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  industry: string;

  @Column({ type: 'int', default: 1 })
  maxUsers: number;

  @Column({ type: 'int', default: 1 })
  maxBusinessUnits: number;

  @Column({ type: 'int', default: 100 })
  maxInvoicesPerMonth: number;

  @Column({ type: 'int', default: 1000 })
  maxApiCallsPerMonth: number;

  @Column({ type: 'int', default: 0 })
  currentUserCount: number;

  @Column({ type: 'int', default: 0 })
  currentBusinessUnitCount: number;

  @Column({ type: 'int', default: 0 })
  currentMonthInvoiceCount: number;

  @Column({ type: 'int', default: 0 })
  currentMonthApiCallCount: number;

  @Column({ type: 'date', nullable: true })
  trialEndsAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  suspendedAt: Date;

  @Column({ type: 'text', nullable: true })
  suspensionReason: string;

  // Billing information
  @Column({ type: 'varchar', length: 100, nullable: true })
  billingEmail: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  billingAddress: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  billingCity: string;

  @Column({ type: 'varchar', length: 2, nullable: true })
  billingState: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  billingZipCode: string;

  @Column({ type: 'varchar', length: 2, default: 'BR' })
  billingCountry: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  taxId: string; // CNPJ or CPF for billing

  // Relationships
  @OneToMany(() => User, (user) => user.account, {
    cascade: true,
  })
  users: User[];

  @OneToMany('BusinessUnit', (businessUnit: any) => businessUnit.account, {
    cascade: true,
  })
  businessUnits: any[];

  @OneToOne(() => Subscription, (subscription) => subscription.account, {
    cascade: true,
  })
  subscription: Subscription;

  @OneToMany(() => ApiKey, (apiKey) => apiKey.account, {
    cascade: true,
  })
  apiKeys: ApiKey[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === AccountStatus.ACTIVE;
  }

  get isOnTrial(): boolean {
    return (
      this.status === AccountStatus.TRIAL &&
      this.trialEndsAt &&
      this.trialEndsAt > new Date()
    );
  }

  get trialDaysRemaining(): number {
    if (!this.isOnTrial) return 0;
    const now = new Date();
    const diffTime = this.trialEndsAt.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get isOverUserLimit(): boolean {
    return this.currentUserCount >= this.maxUsers;
  }

  get isOverBusinessUnitLimit(): boolean {
    return this.currentBusinessUnitCount >= this.maxBusinessUnits;
  }

  get isOverInvoiceLimit(): boolean {
    return this.currentMonthInvoiceCount >= this.maxInvoicesPerMonth;
  }

  get isOverApiCallLimit(): boolean {
    return this.currentMonthApiCallCount >= this.maxApiCallsPerMonth;
  }

  get canCreateInvoice(): boolean {
    return this.isActive && !this.isOverInvoiceLimit;
  }

  get canMakeApiCall(): boolean {
    return this.isActive && !this.isOverApiCallLimit;
  }
}
