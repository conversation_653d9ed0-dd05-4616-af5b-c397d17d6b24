import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { PaymentMethod, PaymentTiming } from '../shared/types/brazilian-types';

export enum PaymentTermStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('payment_terms')
@Index(['businessUnitId'])
@Index(['code'])
@Index(['status'])
@Index(['businessUnitId', 'code'], { unique: true })
export class PaymentTerm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  code: string; // Internal code for the payment term

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    default: PaymentMethod.MONEY,
  })
  method: PaymentMethod;

  @Column({
    type: 'enum',
    enum: PaymentTiming,
    default: PaymentTiming.IMMEDIATE,
  })
  timing: PaymentTiming;

  @Column({
    type: 'enum',
    enum: PaymentTermStatus,
    default: PaymentTermStatus.ACTIVE,
  })
  status: PaymentTermStatus;

  // Payment schedule configuration
  @Column({ type: 'int', default: 1 })
  installmentCount: number;

  @Column({ type: 'int', default: 0 })
  firstInstallmentDays: number; // Days until first installment

  @Column({ type: 'int', default: 30 })
  installmentInterval: number; // Days between installments

  // Discount and interest configuration
  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  cashDiscountPercentage: number; // Discount for cash payment

  @Column({ type: 'int', default: 0 })
  cashDiscountDays: number; // Days to get cash discount

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  latePaymentInterest: number; // Monthly interest rate for late payment

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  latePaymentFine: number; // Fine percentage for late payment

  // Credit analysis
  @Column({ type: 'boolean', default: false })
  requiresCreditAnalysis: boolean;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  minimumCreditLimit: number; // Minimum credit required

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  maximumInvoiceValue: number; // Maximum invoice value for this term

  // Additional configuration
  @Column({ type: 'boolean', default: false })
  isDefault: boolean; // Default payment term for new invoices

  @Column({ type: 'boolean', default: true })
  allowPartialPayment: boolean;

  @Column({ type: 'boolean', default: false })
  generateBankSlip: boolean; // Generate boleto bancário

  @Column({ type: 'boolean', default: false })
  allowCardPayment: boolean;

  @Column({ type: 'boolean', default: false })
  allowPixPayment: boolean;

  // Bank slip configuration (if applicable)
  @Column({ type: 'varchar', length: 255, nullable: true })
  bankSlipInstructions: string;

  @Column({ type: 'int', nullable: true })
  bankSlipExpirationDays: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  bankSlipProcessingFee: number;

  // Card payment configuration (if applicable)
  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  cardProcessingFee: number; // Percentage

  @Column({ type: 'int', nullable: true })
  maxCardInstallments: number;

  // PIX configuration (if applicable)
  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  pixDiscountPercentage: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  pixKey: string;

  // Additional information
  @Column({ type: 'text', nullable: true })
  terms: string; // Terms and conditions text

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  internalNotes: string; // Internal notes not shown to customer

  // Multi-tenant relationship
  @Column({ type: 'uuid' })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', (businessUnit: any) => businessUnit.paymentTerms, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Invoices that use this payment term - using string reference to break circular dependency
  @OneToMany('Invoice', (invoice: any) => invoice.paymentTerm)
  invoices: any[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === PaymentTermStatus.ACTIVE;
  }

  get isCashPayment(): boolean {
    return this.timing === PaymentTiming.IMMEDIATE;
  }

  get isInstallmentPayment(): boolean {
    return (
      this.timing === PaymentTiming.INSTALLMENTS && this.installmentCount > 1
    );
  }

  get hasDiscount(): boolean {
    return this.cashDiscountPercentage > 0;
  }

  get hasLatePaymentPenalties(): boolean {
    return this.latePaymentInterest > 0 || this.latePaymentFine > 0;
  }

  // Calculate installment dates
  generateInstallmentDates(invoiceDate: Date): Date[] {
    const dates: Date[] = [];
    const baseDate = new Date(invoiceDate);

    for (let i = 0; i < this.installmentCount; i++) {
      const installmentDate = new Date(baseDate);
      if (i === 0) {
        installmentDate.setDate(
          installmentDate.getDate() + this.firstInstallmentDays,
        );
      } else {
        installmentDate.setDate(
          installmentDate.getDate() +
            this.firstInstallmentDays +
            i * this.installmentInterval,
        );
      }
      dates.push(installmentDate);
    }

    return dates;
  }

  // Calculate installment values
  calculateInstallmentValues(totalValue: number): number[] {
    const values: number[] = [];
    const baseValue =
      Math.floor((totalValue * 100) / this.installmentCount) / 100;
    const remainder =
      Math.round((totalValue - baseValue * this.installmentCount) * 100) / 100;

    for (let i = 0; i < this.installmentCount; i++) {
      if (i === 0) {
        // Add remainder to first installment
        values.push(baseValue + remainder);
      } else {
        values.push(baseValue);
      }
    }

    return values;
  }

  // Calculate cash discount
  calculateCashDiscount(totalValue: number): number {
    if (!this.hasDiscount) return 0;
    return (
      Math.round(((totalValue * this.cashDiscountPercentage) / 100) * 100) / 100
    );
  }

  // Calculate late payment penalties
  calculateLatePenalties(
    totalValue: number,
    daysLate: number,
  ): { interest: number; fine: number; total: number } {
    let interest = 0;
    let fine = 0;

    if (daysLate > 0) {
      // Calculate fine (applied once)
      if (this.latePaymentFine > 0) {
        fine =
          Math.round(((totalValue * this.latePaymentFine) / 100) * 100) / 100;
      }

      // Calculate interest (monthly rate, pro-rated for days)
      if (this.latePaymentInterest > 0) {
        const monthlyRate = this.latePaymentInterest / 100;
        const dailyRate = monthlyRate / 30;
        interest = Math.round(totalValue * dailyRate * daysLate * 100) / 100;
      }
    }

    return {
      interest,
      fine,
      total: interest + fine,
    };
  }

  // Validate if this payment term can be used for an invoice
  canBeUsedForInvoice(
    invoiceValue: number,
    clientCreditLimit?: number,
  ): { valid: boolean; reason?: string } {
    if (!this.isActive) {
      return { valid: false, reason: 'Payment term is inactive' };
    }

    if (this.maximumInvoiceValue && invoiceValue > this.maximumInvoiceValue) {
      return {
        valid: false,
        reason: 'Invoice value exceeds maximum allowed for this payment term',
      };
    }

    if (this.requiresCreditAnalysis && this.minimumCreditLimit) {
      if (!clientCreditLimit || clientCreditLimit < this.minimumCreditLimit) {
        return {
          valid: false,
          reason: 'Client does not meet minimum credit requirements',
        };
      }
    }

    return { valid: true };
  }

  // Get payment method description
  getPaymentMethodDescription(): string {
    const methodDescriptions: Record<PaymentMethod, string> = {
      [PaymentMethod.MONEY]: 'Dinheiro',
      [PaymentMethod.CREDIT_CARD]: 'Cartão de Crédito',
      [PaymentMethod.DEBIT_CARD]: 'Cartão de Débito',
      [PaymentMethod.BANK_TRANSFER]: 'Transferência Bancária',
      [PaymentMethod.PIX]: 'PIX',
      [PaymentMethod.BANK_SLIP]: 'Boleto Bancário',
      [PaymentMethod.FINANCING]: 'Financiamento',
      [PaymentMethod.CHECK]: 'Cheque',
      [PaymentMethod.OTHER]: 'Outros',
    };

    return methodDescriptions[this.method] || this.method;
  }
}
