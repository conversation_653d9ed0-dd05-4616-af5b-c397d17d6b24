import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { DocumentType } from '../shared/types/brazilian-types';

export enum CarrierStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

export enum CarrierType {
  PESSOA_FISICA = 'pessoa_fisica',
  PESSOA_JURIDICA = 'pessoa_juridica',
  AUTONOMOUS = 'autonomous',
}

export enum VehicleType {
  TRUCK = 'truck',
  VAN = 'van',
  MOTORCYCLE = 'motorcycle',
  CAR = 'car',
  SHIP = 'ship',
  AIRPLANE = 'airplane',
  TRAIN = 'train',
  OTHER = 'other',
}

@Entity('carriers')
@Index(['businessUnitId'])
@Index(['documentNumber'])
@Index(['status'])
@Index(['businessUnitId', 'documentNumber'], { unique: true })
export class Carrier {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  legalName: string; // Razão Social

  @Column({
    type: 'enum',
    enum: CarrierType,
    default: CarrierType.PESSOA_JURIDICA,
  })
  type: CarrierType;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.CNPJ,
  })
  documentType: DocumentType;

  @Column({ type: 'varchar', length: 20 })
  documentNumber: string; // CNPJ or CPF (numbers only)

  @Column({ type: 'varchar', length: 20, nullable: true })
  stateRegistration: string; // Inscrição Estadual

  @Column({
    type: 'enum',
    enum: CarrierStatus,
    default: CarrierStatus.ACTIVE,
  })
  status: CarrierStatus;

  // Contact information
  @Column({ type: 'varchar', length: 255, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  mobile: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  contactPerson: string;

  // Address information
  @Column({ type: 'varchar', length: 10, nullable: true })
  zipCode: string; // CEP

  @Column({ type: 'varchar', length: 255, nullable: true })
  street: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  number: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  complement: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  district: string; // Bairro

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'varchar', length: 2, nullable: true })
  state: string;

  @Column({ type: 'varchar', length: 2, default: 'BR' })
  country: string;

  // Vehicle information
  @Column({ type: 'varchar', length: 10, nullable: true })
  vehiclePlate: string;

  @Column({ type: 'varchar', length: 2, nullable: true })
  vehiclePlateState: string;

  @Column({
    type: 'enum',
    enum: VehicleType,
    nullable: true,
  })
  vehicleType: VehicleType;

  @Column({ type: 'varchar', length: 100, nullable: true })
  vehicleBrand: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  vehicleModel: string;

  @Column({ type: 'int', nullable: true })
  vehicleYear: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  vehicleColor: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  rntrcCode: string; // ANTT registration

  // Driver information (for person physical or autonomous)
  @Column({ type: 'varchar', length: 255, nullable: true })
  driverName: string;

  @Column({ type: 'varchar', length: 11, nullable: true })
  driverCpf: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  driverLicense: string; // CNH

  // Service information
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  pricePerKm: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  minimumPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, nullable: true })
  maxWeight: number; // Maximum weight capacity in KG

  @Column({ type: 'decimal', precision: 10, scale: 4, nullable: true })
  maxVolume: number; // Maximum volume capacity in m³

  @Column({ type: 'text', nullable: true })
  serviceAreas: string; // JSON array of service areas/cities

  @Column({ type: 'text', nullable: true })
  specialServices: string; // Special services offered

  @Column({ type: 'boolean', default: false })
  hasInsurance: boolean;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  insuranceValue: number;

  // Rating and performance
  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  rating: number; // 0-5 stars

  @Column({ type: 'int', default: 0 })
  completedDeliveries: number;

  @Column({ type: 'int', default: 0 })
  onTimeDeliveries: number;

  @Column({ type: 'timestamp', nullable: true })
  lastDeliveryDate: Date;

  // Additional information
  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  observations: string; // For transport observations in NF-e

  // Multi-tenant relationship
  @Column({ type: 'uuid' })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', (businessUnit: any) => businessUnit.carriers, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Invoices that used this carrier - using string reference to break circular dependency
  @OneToMany('Invoice', (invoice: any) => invoice.carrier)
  invoices: any[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === CarrierStatus.ACTIVE;
  }

  get isPerson(): boolean {
    return this.type === CarrierType.PESSOA_FISICA;
  }

  get isCompany(): boolean {
    return this.type === CarrierType.PESSOA_JURIDICA;
  }

  get isAutonomous(): boolean {
    return this.type === CarrierType.AUTONOMOUS;
  }

  get formattedDocument(): string {
    if (
      this.documentType === DocumentType.CNPJ &&
      this.documentNumber?.length === 14
    ) {
      return this.documentNumber.replace(
        /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
        '$1.$2.$3/$4-$5',
      );
    }
    if (
      this.documentType === DocumentType.CPF &&
      this.documentNumber?.length === 11
    ) {
      return this.documentNumber.replace(
        /^(\d{3})(\d{3})(\d{3})(\d{2})$/,
        '$1.$2.$3-$4',
      );
    }
    return this.documentNumber || '';
  }

  get formattedVehiclePlate(): string {
    if (!this.vehiclePlate) return '';
    const plate = this.vehiclePlate.replace(/[^A-Z0-9]/gi, '').toUpperCase();

    // New format: ABC1D23
    if (/^[A-Z]{3}\d[A-Z]\d{2}$/.test(plate)) {
      return plate.replace(/^([A-Z]{3})(\d)([A-Z])(\d{2})$/, '$1-$2$3$4');
    }

    // Old format: ABC1234
    if (/^[A-Z]{3}\d{4}$/.test(plate)) {
      return plate.replace(/^([A-Z]{3})(\d{4})$/, '$1-$2');
    }

    return this.vehiclePlate;
  }

  get fullAddress(): string {
    const parts = [
      this.street,
      this.number,
      this.complement,
      this.district,
      this.city,
      this.state,
      this.zipCode,
    ].filter(Boolean);
    return parts.join(', ');
  }

  get onTimePercentage(): number {
    if (this.completedDeliveries === 0) return 0;
    return (this.onTimeDeliveries / this.completedDeliveries) * 100;
  }

  get performanceScore(): string {
    const percentage = this.onTimePercentage;
    if (percentage >= 95) return 'excellent';
    if (percentage >= 85) return 'good';
    if (percentage >= 70) return 'average';
    return 'poor';
  }

  canCarry(weight: number, volume?: number): boolean {
    if (!this.isActive) return false;
    if (this.maxWeight && weight > this.maxWeight) return false;
    if (volume && this.maxVolume && volume > this.maxVolume) return false;
    return true;
  }

  calculateShippingCost(distance: number): number {
    if (!this.pricePerKm) return 0;
    const cost = distance * this.pricePerKm;
    return this.minimumPrice ? Math.max(cost, this.minimumPrice) : cost;
  }

  updateDeliveryStats(onTime: boolean): void {
    this.completedDeliveries++;
    if (onTime) {
      this.onTimeDeliveries++;
    }
    this.lastDeliveryDate = new Date();
  }
}
