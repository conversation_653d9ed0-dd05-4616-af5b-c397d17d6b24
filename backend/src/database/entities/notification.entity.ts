import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export enum NotificationCategory {
  SYSTEM = 'system',
  INVOICE = 'invoice',
  CERTIFICATE = 'certificate',
  SUBSCRIPTION = 'subscription',
  PAYMENT = 'payment',
  COMPLIANCE = 'compliance',
  SECURITY = 'security',
  INTEGRATION = 'integration',
  USER_ACTION = 'user_action',
  MAINTENANCE = 'maintenance',
}

export enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  WEBHOOK = 'webhook',
  SLACK = 'slack',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('notifications')
@Index(['accountId'])
@Index(['businessUnitId'])
@Index(['userId'])
@Index(['type'])
@Index(['category'])
@Index(['status'])
@Index(['priority'])
@Index(['scheduledFor'])
@Index(['createdAt'])
@Index(['isRead'])
@Index(['channels'])
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Notification content
  @Column({ type: 'varchar', length: 255 })
  title: string; // Short notification title

  @Column({ type: 'text' })
  message: string; // Main notification message

  @Column({ type: 'text', nullable: true })
  description: string; // Additional details or longer description

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationCategory,
  })
  category: NotificationCategory;

  @Column({
    type: 'enum',
    enum: NotificationPriority,
    default: NotificationPriority.NORMAL,
  })
  priority: NotificationPriority;

  // Delivery configuration
  @Column({
    type: 'enum',
    enum: NotificationChannel,
    array: true,
    default: [NotificationChannel.IN_APP],
  })
  channels: NotificationChannel[]; // Multiple channels supported

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  status: NotificationStatus;

  @Column({ type: 'timestamp', nullable: true })
  scheduledFor: Date; // When notification should be sent (null = immediately)

  @Column({ type: 'timestamp', nullable: true })
  sentAt: Date; // When notification was actually sent

  @Column({ type: 'timestamp', nullable: true })
  deliveredAt: Date; // When notification was delivered to the channel

  @Column({ type: 'timestamp', nullable: true })
  readAt: Date; // When notification was read by user

  @Column({ type: 'boolean', default: false })
  isRead: boolean; // Whether notification has been read

  @Column({ type: 'boolean', default: false })
  isArchived: boolean; // Whether notification is archived

  @Column({ type: 'boolean', default: false })
  isPinned: boolean; // Whether notification is pinned in UI

  // Action and navigation
  @Column({ type: 'varchar', length: 255, nullable: true })
  actionUrl: string; // URL to navigate when notification is clicked

  @Column({ type: 'varchar', length: 100, nullable: true })
  actionLabel: string; // Label for action button (e.g., "View Invoice")

  @Column({ type: 'text', nullable: true })
  actionData: string; // JSON data for the action

  @Column({ type: 'varchar', length: 100, nullable: true })
  icon: string; // Icon identifier for UI

  @Column({ type: 'varchar', length: 50, nullable: true })
  color: string; // Color theme for notification

  // Context and metadata
  @Column({ type: 'text', nullable: true })
  metadata: string; // Additional metadata in JSON format

  @Column({ type: 'varchar', length: 100, nullable: true })
  sourceSystem: string; // System that generated the notification

  @Column({ type: 'varchar', length: 100, nullable: true })
  sourceModule: string; // Module that generated the notification

  @Column({ type: 'varchar', length: 255, nullable: true })
  sourceEntityType: string; // Type of entity that triggered notification

  @Column({ type: 'varchar', length: 255, nullable: true })
  sourceEntityId: string; // ID of entity that triggered notification

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string; // For grouping related notifications

  @Column({ type: 'varchar', length: 255, nullable: true })
  threadId: string; // For threading conversations

  // Delivery tracking
  @Column({ type: 'int', default: 0 })
  retryCount: number; // Number of delivery attempts

  @Column({ type: 'int', default: 3 })
  maxRetries: number; // Maximum retry attempts

  @Column({ type: 'timestamp', nullable: true })
  nextRetryAt: Date; // When next retry should happen

  @Column({ type: 'text', nullable: true })
  deliveryErrors: string; // JSON array of delivery errors

  @Column({ type: 'text', nullable: true })
  deliveryReceipts: string; // JSON array of delivery confirmations

  // Expiration and cleanup
  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date; // When notification should be considered expired

  @Column({ type: 'boolean', default: false })
  autoArchiveOnRead: boolean; // Automatically archive when read

  @Column({ type: 'boolean', default: false })
  autoDeleteOnExpiry: boolean; // Automatically delete when expired

  // Template and localization
  @Column({ type: 'varchar', length: 100, nullable: true })
  templateId: string; // Template used to generate notification

  @Column({ type: 'text', nullable: true })
  templateVariables: string; // Variables used in template (JSON)

  @Column({ type: 'varchar', length: 10, default: 'pt-BR' })
  language: string; // Language/locale for the notification

  // Multi-tenant relationships
  @Column({ type: 'uuid', nullable: true })
  accountId: string;

  @ManyToOne('Account', {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: any;

  @Column({ type: 'uuid', nullable: true })
  businessUnitId: string;

  @ManyToOne('BusinessUnit', {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Target user
  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @ManyToOne('User', (user: any) => user.notifications, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: any;

  // Timestamps
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  get isPending(): boolean {
    return this.status === NotificationStatus.PENDING;
  }

  get isSent(): boolean {
    return [
      NotificationStatus.SENT,
      NotificationStatus.DELIVERED,
      NotificationStatus.READ,
    ].includes(this.status);
  }

  get isDelivered(): boolean {
    return [NotificationStatus.DELIVERED, NotificationStatus.READ].includes(
      this.status,
    );
  }

  get isFailed(): boolean {
    return this.status === NotificationStatus.FAILED;
  }

  get isHighPriority(): boolean {
    return [NotificationPriority.HIGH, NotificationPriority.URGENT].includes(
      this.priority,
    );
  }

  get shouldRetry(): boolean {
    return (
      this.status === NotificationStatus.FAILED &&
      this.retryCount < this.maxRetries &&
      (!this.nextRetryAt || this.nextRetryAt <= new Date()) &&
      !this.isExpired
    );
  }

  get priorityDisplayName(): string {
    const priorityNames: Record<NotificationPriority, string> = {
      [NotificationPriority.LOW]: 'Baixa',
      [NotificationPriority.NORMAL]: 'Normal',
      [NotificationPriority.HIGH]: 'Alta',
      [NotificationPriority.URGENT]: 'Urgente',
    };
    return priorityNames[this.priority];
  }

  get typeDisplayName(): string {
    const typeNames: Record<NotificationType, string> = {
      [NotificationType.INFO]: 'Informação',
      [NotificationType.SUCCESS]: 'Sucesso',
      [NotificationType.WARNING]: 'Aviso',
      [NotificationType.ERROR]: 'Erro',
      [NotificationType.CRITICAL]: 'Crítico',
    };
    return typeNames[this.type];
  }

  get categoryDisplayName(): string {
    const categoryNames: Record<NotificationCategory, string> = {
      [NotificationCategory.SYSTEM]: 'Sistema',
      [NotificationCategory.INVOICE]: 'Nota Fiscal',
      [NotificationCategory.CERTIFICATE]: 'Certificado',
      [NotificationCategory.SUBSCRIPTION]: 'Assinatura',
      [NotificationCategory.PAYMENT]: 'Pagamento',
      [NotificationCategory.COMPLIANCE]: 'Conformidade',
      [NotificationCategory.SECURITY]: 'Segurança',
      [NotificationCategory.INTEGRATION]: 'Integração',
      [NotificationCategory.USER_ACTION]: 'Ação do Usuário',
      [NotificationCategory.MAINTENANCE]: 'Manutenção',
    };
    return categoryNames[this.category];
  }

  get statusDisplayName(): string {
    const statusNames: Record<NotificationStatus, string> = {
      [NotificationStatus.PENDING]: 'Pendente',
      [NotificationStatus.SENT]: 'Enviada',
      [NotificationStatus.DELIVERED]: 'Entregue',
      [NotificationStatus.READ]: 'Lida',
      [NotificationStatus.FAILED]: 'Falhou',
      [NotificationStatus.CANCELLED]: 'Cancelada',
    };
    return statusNames[this.status];
  }

  // Parse JSON fields safely
  getMetadata(): Record<string, any> | null {
    try {
      return this.metadata ? JSON.parse(this.metadata) : null;
    } catch {
      return null;
    }
  }

  getActionData(): Record<string, any> | null {
    try {
      return this.actionData ? JSON.parse(this.actionData) : null;
    } catch {
      return null;
    }
  }

  getTemplateVariables(): Record<string, any> | null {
    try {
      return this.templateVariables ? JSON.parse(this.templateVariables) : null;
    } catch {
      return null;
    }
  }

  getDeliveryErrors(): any[] {
    try {
      return this.deliveryErrors ? JSON.parse(this.deliveryErrors) : [];
    } catch {
      return [];
    }
  }

  getDeliveryReceipts(): any[] {
    try {
      return this.deliveryReceipts ? JSON.parse(this.deliveryReceipts) : [];
    } catch {
      return [];
    }
  }

  // Set JSON fields safely
  setMetadata(data: Record<string, any>): void {
    this.metadata = JSON.stringify(data);
  }

  setActionData(data: Record<string, any>): void {
    this.actionData = JSON.stringify(data);
  }

  setTemplateVariables(data: Record<string, any>): void {
    this.templateVariables = JSON.stringify(data);
  }

  setDeliveryErrors(errors: any[]): void {
    this.deliveryErrors = JSON.stringify(errors);
  }

  setDeliveryReceipts(receipts: any[]): void {
    this.deliveryReceipts = JSON.stringify(receipts);
  }

  // Add delivery error
  addDeliveryError(error: any): void {
    const errors = this.getDeliveryErrors();
    errors.push({
      ...error,
      timestamp: new Date().toISOString(),
    });
    this.setDeliveryErrors(errors);
  }

  // Add delivery receipt
  addDeliveryReceipt(receipt: any): void {
    const receipts = this.getDeliveryReceipts();
    receipts.push({
      ...receipt,
      timestamp: new Date().toISOString(),
    });
    this.setDeliveryReceipts(receipts);
  }

  // Mark as read
  markAsRead(): void {
    if (!this.isRead) {
      this.isRead = true;
      this.readAt = new Date();
      this.status = NotificationStatus.READ;

      if (this.autoArchiveOnRead) {
        this.isArchived = true;
      }
    }
  }

  // Mark as delivered
  markAsDelivered(): void {
    if (!this.isDelivered) {
      this.deliveredAt = new Date();
      this.status = NotificationStatus.DELIVERED;
    }
  }

  // Mark as sent
  markAsSent(): void {
    if (!this.isSent) {
      this.sentAt = new Date();
      this.status = NotificationStatus.SENT;
    }
  }

  // Mark as failed
  markAsFailed(error?: any): void {
    this.status = NotificationStatus.FAILED;
    if (error) {
      this.addDeliveryError(error);
    }

    // Schedule retry if within limits
    if (this.retryCount < this.maxRetries && !this.isExpired) {
      this.retryCount++;
      this.scheduleNextRetry();
    }
  }

  // Schedule next retry with exponential backoff
  scheduleNextRetry(): void {
    const backoffMinutes = Math.pow(2, this.retryCount) * 5; // 5, 10, 20, 40 minutes
    this.nextRetryAt = new Date(Date.now() + backoffMinutes * 60 * 1000);
  }

  // Cancel notification
  cancel(): void {
    if (this.isPending) {
      this.status = NotificationStatus.CANCELLED;
    }
  }

  // Archive notification
  archive(): void {
    this.isArchived = true;
  }

  // Pin notification
  pin(): void {
    this.isPinned = true;
  }

  // Unpin notification
  unpin(): void {
    this.isPinned = false;
  }

  // Check if notification should be cleaned up
  shouldBeDeleted(): boolean {
    return this.autoDeleteOnExpiry && this.isExpired && this.isRead;
  }

  // Static factory methods
  static create(params: {
    title: string;
    message: string;
    type: NotificationType;
    category: NotificationCategory;
    userId?: string;
    accountId?: string;
    businessUnitId?: string;
    channels?: NotificationChannel[];
    priority?: NotificationPriority;
    scheduledFor?: Date;
    expiresAt?: Date;
    actionUrl?: string;
    actionLabel?: string;
    metadata?: Record<string, any>;
  }): Notification {
    const notification = new Notification();

    notification.title = params.title;
    notification.message = params.message;
    notification.type = params.type;
    notification.category = params.category;
    notification.priority = params.priority || NotificationPriority.NORMAL;
    notification.channels = params.channels || [NotificationChannel.IN_APP];

    if (params.userId) notification.userId = params.userId;
    if (params.accountId) notification.accountId = params.accountId;
    if (params.businessUnitId)
      notification.businessUnitId = params.businessUnitId;
    if (params.scheduledFor) notification.scheduledFor = params.scheduledFor;
    if (params.expiresAt) notification.expiresAt = params.expiresAt;
    if (params.actionUrl) notification.actionUrl = params.actionUrl;
    if (params.actionLabel) notification.actionLabel = params.actionLabel;
    if (params.metadata) notification.setMetadata(params.metadata);

    return notification;
  }

  // Create system notification
  static createSystemNotification(params: {
    title: string;
    message: string;
    type?: NotificationType;
    userId?: string;
    accountId?: string;
    priority?: NotificationPriority;
    metadata?: Record<string, any>;
  }): Notification {
    return Notification.create({
      ...params,
      type: params.type || NotificationType.INFO,
      category: NotificationCategory.SYSTEM,
      priority: params.priority || NotificationPriority.NORMAL,
    });
  }

  // Create invoice notification
  static createInvoiceNotification(params: {
    title: string;
    message: string;
    type: NotificationType;
    invoiceId: string;
    userId?: string;
    accountId?: string;
    businessUnitId?: string;
    actionUrl?: string;
  }): Notification {
    return Notification.create({
      ...params,
      category: NotificationCategory.INVOICE,
      metadata: {
        invoiceId: params.invoiceId,
      },
      actionUrl: params.actionUrl || `/invoices/${params.invoiceId}`,
      actionLabel: 'Ver Nota Fiscal',
    });
  }

  // Create certificate notification
  static createCertificateNotification(params: {
    title: string;
    message: string;
    type: NotificationType;
    certificateId: string;
    userId?: string;
    accountId?: string;
    businessUnitId?: string;
    expiresAt?: Date;
  }): Notification {
    return Notification.create({
      ...params,
      category: NotificationCategory.CERTIFICATE,
      priority: NotificationPriority.HIGH,
      metadata: {
        certificateId: params.certificateId,
      },
      actionUrl: `/certificates/${params.certificateId}`,
      actionLabel: 'Ver Certificado',
    });
  }

  // Create compliance notification
  static createComplianceNotification(params: {
    title: string;
    message: string;
    userId?: string;
    accountId?: string;
    businessUnitId?: string;
    complianceType: string;
    metadata?: Record<string, any>;
  }): Notification {
    return Notification.create({
      ...params,
      type: NotificationType.WARNING,
      category: NotificationCategory.COMPLIANCE,
      priority: NotificationPriority.HIGH,
      channels: [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
      metadata: {
        complianceType: params.complianceType,
        ...params.metadata,
      },
    });
  }
}
