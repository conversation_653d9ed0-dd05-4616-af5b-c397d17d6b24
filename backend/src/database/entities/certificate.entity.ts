import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BusinessUnit } from './business-unit.entity';
import { CertificateType } from '../../shared/types/brazilian-types';

export enum CertificateStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  SUSPENDED = 'suspended',
  PENDING_VALIDATION = 'pending_validation',
}

@Entity('certificates')
@Index(['businessUnitId'], { unique: true })
@Index(['status'])
@Index(['validTo'])
@Index(['serialNumber'])
export class Certificate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string; // Friendly name for the certificate

  @Column({
    type: 'enum',
    enum: CertificateType,
    default: CertificateType.A1,
  })
  type: CertificateType;

  @Column({
    type: 'enum',
    enum: CertificateStatus,
    default: CertificateStatus.PENDING_VALIDATION,
  })
  status: CertificateStatus;

  // Certificate identification
  @Column({ type: 'varchar', length: 255 })
  subject: string; // Certificate subject (CN, O, etc.)

  @Column({ type: 'varchar', length: 255 })
  issuer: string; // Certificate issuer

  @Column({ type: 'varchar', length: 100 })
  serialNumber: string; // Certificate serial number

  @Column({ type: 'varchar', length: 100, nullable: true })
  thumbprint: string; // Certificate thumbprint/fingerprint

  // Validity period
  @Column({ type: 'timestamp' })
  validFrom: Date;

  @Column({ type: 'timestamp' })
  validTo: Date;

  // Associated CNPJ
  @Column({ type: 'varchar', length: 18 })
  cnpj: string; // CNPJ associated with this certificate

  // Certificate storage
  @Column({ type: 'text' })
  encryptedData: string; // Encrypted certificate data (PFX/P12 for A1)

  @Column({ type: 'varchar', length: 255 })
  encryptionKey: string; // Key used for encryption (encrypted itself)

  @Column({ type: 'varchar', length: 255, nullable: true })
  originalFileName: string; // Original file name when uploaded

  @Column({ type: 'varchar', length: 50, nullable: true })
  fileHash: string; // Hash of the original file for integrity

  @Column({ type: 'int', nullable: true })
  fileSize: number; // Original file size in bytes

  // A3 Certificate specific (smart card/token)
  @Column({ type: 'varchar', length: 255, nullable: true })
  tokenSerial: string; // Smart card/token serial number

  @Column({ type: 'varchar', length: 100, nullable: true })
  tokenLabel: string; // Smart card/token label

  @Column({ type: 'varchar', length: 100, nullable: true })
  cspProvider: string; // Cryptographic Service Provider

  // Password management
  @Column({ type: 'varchar', length: 255, nullable: true })
  encryptedPassword: string; // Encrypted certificate password

  @Column({ type: 'boolean', default: false })
  passwordRequired: boolean; // Whether password is required to use certificate

  // Usage tracking
  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt: Date; // Last time certificate was used for signing

  @Column({ type: 'int', default: 0 })
  usageCount: number; // Number of times certificate was used

  @Column({ type: 'timestamp', nullable: true })
  lastValidationAt: Date; // Last time certificate was validated

  // Monitoring and alerts
  @Column({ type: 'boolean', default: true })
  monitorExpiration: boolean; // Whether to monitor expiration

  @Column({ type: 'int', default: 30 })
  expirationWarningDays: number; // Days before expiration to send warning

  @Column({ type: 'timestamp', nullable: true })
  lastExpirationWarningAt: Date; // Last time expiration warning was sent

  @Column({ type: 'boolean', default: true })
  alertEnabled: boolean; // Whether alerts are enabled for this certificate

  // Installation and configuration
  @Column({ type: 'timestamp', nullable: true })
  installedAt: Date; // When certificate was installed/configured

  @Column({ type: 'uuid', nullable: true })
  installedByUserId: string; // User who installed the certificate

  @Column({ type: 'text', nullable: true })
  installationNotes: string; // Notes about installation process

  @Column({ type: 'varchar', length: 255, nullable: true })
  configurationData: string; // Additional configuration data (JSON)

  // Revocation information
  @Column({ type: 'timestamp', nullable: true })
  revokedAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  revocationReason: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  revocationUrl: string; // CRL or OCSP URL

  // Backup and recovery
  @Column({ type: 'text', nullable: true })
  backupData: string; // Encrypted backup of certificate

  @Column({ type: 'timestamp', nullable: true })
  lastBackupAt: Date;

  @Column({ type: 'boolean', default: false })
  hasBackup: boolean;

  // Business unit relationship (one-to-one)
  @Column({ type: 'uuid' })
  businessUnitId: string;

  @OneToOne(() => BusinessUnit, (businessUnit) => businessUnit.certificate, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: BusinessUnit;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === CertificateStatus.ACTIVE;
  }

  get isExpired(): boolean {
    return this.status === CertificateStatus.EXPIRED || this.validTo < new Date();
  }

  get isExpiringSoon(): boolean {
    if (this.isExpired) return false;
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() + this.expirationWarningDays);
    return this.validTo <= warningDate;
  }

  get isRevoked(): boolean {
    return this.status === CertificateStatus.REVOKED;
  }

  get canBeUsed(): boolean {
    return this.isActive && !this.isExpired && !this.isRevoked;
  }

  get daysUntilExpiration(): number {
    if (this.isExpired) return 0;
    const now = new Date();
    const diffTime = this.validTo.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get isA1Certificate(): boolean {
    return this.type === CertificateType.A1;
  }

  get isA3Certificate(): boolean {
    return this.type === CertificateType.A3;
  }

  get formattedValidityPeriod(): string {
    const validFrom = this.validFrom.toLocaleDateString('pt-BR');
    const validTo = this.validTo.toLocaleDateString('pt-BR');
    return `${validFrom} - ${validTo}`;
  }

  get statusDescription(): string {
    const statusDescriptions: Record<CertificateStatus, string> = {
      [CertificateStatus.ACTIVE]: 'Ativo',
      [CertificateStatus.EXPIRED]: 'Expirado',
      [CertificateStatus.REVOKED]: 'Revogado',
      [CertificateStatus.SUSPENDED]: 'Suspenso',
      [CertificateStatus.PENDING_VALIDATION]: 'Pendente Validação',
    };
    return statusDescriptions[this.status];
  }

  // Update status based on validity and other factors
  updateStatus(): void {
    if (this.isRevoked) {
      this.status = CertificateStatus.REVOKED;
      return;
    }

    if (this.validTo < new Date()) {
      this.status = CertificateStatus.EXPIRED;
      return;
    }

    if (this.validFrom <= new Date() && this.validTo > new Date()) {
      this.status = CertificateStatus.ACTIVE;
      return;
    }

    // If we can't determine status, keep pending validation
    if (this.status === CertificateStatus.PENDING_VALIDATION) {
      return;
    }
  }

  // Mark certificate as used
  markAsUsed(): void {
    this.lastUsedAt = new Date();
    this.usageCount++;
  }

  // Create backup of certificate data
  createBackup(): void {
    if (this.encryptedData) {
      this.backupData = this.encryptedData; // In real implementation, use different encryption
      this.lastBackupAt = new Date();
      this.hasBackup = true;
    }
  }

  // Validate certificate against business unit
  validateForBusinessUnit(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.canBeUsed) {
      errors.push('Certificate is not active or has expired');
    }

    if (this.cnpj !== this.businessUnit?.cnpj?.replace(/\D/g, '')) {
      errors.push('Certificate CNPJ does not match business unit CNPJ');
    }

    if (this.isExpiringSoon) {
      errors.push(`Certificate will expire in ${this.daysUntilExpiration} days`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Check if expiration warning should be sent
  shouldSendExpirationWarning(): boolean {
    if (!this.alertEnabled || !this.monitorExpiration) return false;
    if (this.isExpired || this.isRevoked) return false;
    if (!this.isExpiringSoon) return false;

    // Check if warning was already sent recently (within last 24 hours)
    if (this.lastExpirationWarningAt) {
      const daysSinceLastWarning = Math.floor(
        (Date.now() - this.lastExpirationWarningAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      return daysSinceLastWarning >= 1; // Send at most once per day
    }

    return true;
  }

  // Mark expiration warning as sent
  markExpirationWarningSent(): void {
    this.lastExpirationWarningAt = new Date();
  }

  // Encrypt certificate data (placeholder - implement proper encryption)
  encryptCertificateData(data: Buffer, password?: string): void {
    // In real implementation, use proper encryption with secure key management
    this.encryptedData = Buffer.from(data).toString('base64');
    this.fileSize = data.length;
    this.fileHash = this.generateHash(data);

    if (password) {
      this.encryptedPassword = Buffer.from(password).toString('base64'); // Use proper encryption
      this.passwordRequired = true;
    }
  }

  // Decrypt certificate data (placeholder)
  decryptCertificateData(): Buffer | null {
    try {
      return Buffer.from(this.encryptedData, 'base64');
    } catch (error) {
      return null;
    }
  }

  // Generate file hash for integrity checking
  private generateHash(data: Buffer): string {
    // In real implementation, use crypto.createHash
    return data.toString('hex').slice(0, 50);
  }

  // Revoke certificate
  revoke(reason: string): void {
    this.status = CertificateStatus.REVOKED;
    this.revokedAt = new Date();
    this.revocationReason = reason;
  }

  // Validate certificate integrity
  validateIntegrity(): boolean {
    try {
      const data = this.decryptCertificateData();
      if (!data) return false;

      const currentHash = this.generateHash(data);
      return currentHash === this.fileHash;
    } catch {
      return false;
    }
  }
}
