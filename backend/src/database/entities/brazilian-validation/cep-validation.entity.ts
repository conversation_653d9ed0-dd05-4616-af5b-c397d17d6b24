import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Account } from '../account.entity';
import { BusinessUnit } from '../business-unit.entity';

export enum CEPStatus {
  VALID = 'valid',
  INVALID = 'invalid',
  NOT_FOUND = 'not_found',
  ERROR = 'error',
}

@Entity('cep_validations')
@Index(['cep'], { unique: true })
@Index(['accountId'])
@Index(['businessUnitId'])
@Index(['status'])
@Index(['validatedAt'])
@Index(['expiresAt'])
@Index(['state'])
@Index(['city'])
export class CEPValidation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // CEP information
  @Column({ type: 'varchar', length: 8 })
  cep: string; // Raw CEP numbers only

  @Column({ type: 'varchar', length: 9 })
  formattedCEP: string; // Formatted CEP (XXXXX-XXX)

  @Column({ type: 'boolean', default: true })
  isValid: boolean; // Whether CEP format is valid

  @Column({
    type: 'enum',
    enum: CEPStatus,
    default: CEPStatus.VALID,
  })
  status: CEPStatus;

  // Address information
  @Column({ type: 'varchar', length: 255, nullable: true })
  street: string; // Logradouro

  @Column({ type: 'varchar', length: 255, nullable: true })
  complement: string; // Complemento

  @Column({ type: 'varchar', length: 100, nullable: true })
  neighborhood: string; // Bairro

  @Column({ type: 'varchar', length: 100 })
  city: string; // Cidade/Município

  @Column({ type: 'varchar', length: 2 })
  state: string; // Estado (UF)

  @Column({ type: 'varchar', length: 10, nullable: true })
  ibgeCode: string; // Código IBGE do município

  @Column({ type: 'varchar', length: 4, nullable: true })
  giaCode: string; // Código GIA

  @Column({ type: 'varchar', length: 4, nullable: true })
  dddCode: string; // Código DDD

  @Column({ type: 'varchar', length: 10, nullable: true })
  siafi: string; // Código SIAFI

  // Geographic coordinates (if available)
  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude: number;

  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude: number;

  // Regional information
  @Column({ type: 'varchar', length: 50, nullable: true })
  region: string; // Região (Norte, Nordeste, etc.)

  @Column({ type: 'varchar', length: 100, nullable: true })
  timezone: string; // Fuso horário

  // Service area information
  @Column({ type: 'boolean', default: true })
  hasPostalService: boolean; // Se tem serviço postal

  @Column({ type: 'boolean', default: true })
  hasDeliveryService: boolean; // Se tem serviço de entrega

  @Column({ type: 'text', nullable: true })
  serviceRestrictions: string; // Restrições de serviço (JSON)

  // Validation metadata
  @Column({ type: 'varchar', length: 100, nullable: true })
  validationSource: string; // Source of validation (ViaCEP, Correios, etc.)

  @Column({ type: 'timestamp', nullable: true })
  validatedAt: Date; // When validation was performed

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date; // When validation expires

  @Column({ type: 'boolean', default: false })
  needsRevalidation: boolean; // Whether revalidation is needed

  @Column({ type: 'text', nullable: true })
  validationErrors?: string; // JSON array of validation errors

  @Column({ type: 'text', nullable: true })
  rawData: string; // Raw validation response (JSON)

  @Column({ type: 'int', default: 0 })
  validationAttempts: number; // Number of validation attempts

  @Column({ type: 'timestamp', nullable: true })
  lastValidationAttempt?: Date; // Last validation attempt

  // Usage statistics
  @Column({ type: 'int', default: 0 })
  usageCount: number; // How many times this CEP was used

  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt: Date; // When this CEP was last used

  // Quality and confidence
  @Column({ type: 'int', default: 100 })
  confidenceScore: number; // Confidence in data quality (0-100)

  @Column({ type: 'varchar', length: 50, nullable: true })
  dataQuality: string; // Quality indicator (high, medium, low)

  @Column({ type: 'boolean', default: false })
  isApproximateLocation: boolean; // Whether location is approximate

  // Multi-tenant relationships
  @Column({ type: 'uuid', nullable: true })
  accountId: string;

  @ManyToOne(() => Account, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: Account;

  @Column({ type: 'uuid', nullable: true })
  businessUnitId: string;

  @ManyToOne(() => BusinessUnit, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: BusinessUnit;

  // Timestamps
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  get isActive(): boolean {
    return this.status === CEPStatus.VALID && this.isValid && !this.isExpired;
  }

  get statusDisplayName(): string {
    const statusNames: Record<CEPStatus, string> = {
      [CEPStatus.VALID]: 'Válido',
      [CEPStatus.INVALID]: 'Inválido',
      [CEPStatus.NOT_FOUND]: 'Não encontrado',
      [CEPStatus.ERROR]: 'Erro',
    };
    return statusNames[this.status] || 'Desconhecido';
  }

  get fullAddress(): string {
    const parts: string[] = [];
    
    if (this.street) parts.push(this.street);
    if (this.complement) parts.push(this.complement);
    if (this.neighborhood) parts.push(this.neighborhood);
    if (this.city) parts.push(this.city);
    if (this.state) parts.push(this.state);
    parts.push(this.formattedCEP);
    
    return parts.join(', ');
  }

  get regionDisplayName(): string {
    const regionNames: Record<string, string> = {
      'norte': 'Norte',
      'nordeste': 'Nordeste',
      'centro-oeste': 'Centro-Oeste',
      'sudeste': 'Sudeste',
      'sul': 'Sul',
    };
    return regionNames[this.region?.toLowerCase()] || this.region || 'Não informado';
  }

  get stateDisplayName(): string {
    const stateNames: Record<string, string> = {
      'AC': 'Acre',
      'AL': 'Alagoas',
      'AP': 'Amapá',
      'AM': 'Amazonas',
      'BA': 'Bahia',
      'CE': 'Ceará',
      'DF': 'Distrito Federal',
      'ES': 'Espírito Santo',
      'GO': 'Goiás',
      'MA': 'Maranhão',
      'MT': 'Mato Grosso',
      'MS': 'Mato Grosso do Sul',
      'MG': 'Minas Gerais',
      'PA': 'Pará',
      'PB': 'Paraíba',
      'PR': 'Paraná',
      'PE': 'Pernambuco',
      'PI': 'Piauí',
      'RJ': 'Rio de Janeiro',
      'RN': 'Rio Grande do Norte',
      'RS': 'Rio Grande do Sul',
      'RO': 'Rondônia',
      'RR': 'Roraima',
      'SC': 'Santa Catarina',
      'SP': 'São Paulo',
      'SE': 'Sergipe',
      'TO': 'Tocantins',
    };
    return stateNames[this.state] || this.state;
  }

  // Parse JSON fields safely
  getServiceRestrictions(): string[] {
    try {
      return this.serviceRestrictions ? JSON.parse(this.serviceRestrictions) : [];
    } catch {
      return [];
    }
  }

  getValidationErrors(): string[] {
    try {
      return this.validationErrors ? JSON.parse(this.validationErrors) : [];
    } catch {
      return [];
    }
  }

  getRawData(): Record<string, any> | null {
    try {
      return this.rawData ? JSON.parse(this.rawData) : null;
    } catch {
      return null;
    }
  }

  // Set JSON fields safely
  setServiceRestrictions(restrictions: string[]): void {
    this.serviceRestrictions = JSON.stringify(restrictions);
  }

  setValidationErrors(errors: string[]): void {
    this.validationErrors = JSON.stringify(errors);
  }

  setRawData(data: Record<string, any>): void {
    this.rawData = JSON.stringify(data);
  }

  // CEP validation methods
  static isValidCEP(cep: string): boolean {
    // Remove non-numeric characters
    const cleanCEP = cep.replace(/\D/g, '');
    
    // Check if has 8 digits
    if (cleanCEP.length !== 8) return false;
    
    // Check if all digits are the same (invalid pattern)
    if (/^(\d)\1+$/.test(cleanCEP)) return false;
    
    return true;
  }

  static formatCEP(cep: string): string {
    const cleanCEP = cep.replace(/\D/g, '');
    return cleanCEP.replace(/^(\d{5})(\d{3})$/, '$1-$2');
  }

  static cleanCEP(cep: string): string {
    return cep.replace(/\D/g, '');
  }

  // Get distance to another CEP (requires coordinates)
  getDistanceTo(other: CEPValidation): number | null {
    if (!this.latitude || !this.longitude || !other.latitude || !other.longitude) {
      return null;
    }

    const R = 6371; // Earth's radius in km
    const dLat = this.toRad(other.latitude - this.latitude);
    const dLng = this.toRad(other.longitude - this.longitude);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRad(this.latitude)) *
        Math.cos(this.toRad(other.latitude)) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRad(value: number): number {
    return (value * Math.PI) / 180;
  }

  // Update validation data
  updateValidation(data: Partial<CEPValidation>): void {
    Object.assign(this, data);
    this.validatedAt = new Date();
    this.needsRevalidation = false;
    
    // Set expiration date (90 days from now - CEP data is more stable)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 90);
    this.expiresAt = expirationDate;
  }

  // Mark as needing revalidation
  markForRevalidation(): void {
    this.needsRevalidation = true;
  }

  // Increment validation attempts
  incrementValidationAttempts(): void {
    this.validationAttempts++;
    this.lastValidationAttempt = new Date();
  }

  // Increment usage count
  incrementUsage(): void {
    this.usageCount++;
    this.lastUsedAt = new Date();
  }

  // Add validation error
  addValidationError(error: string): void {
    const errors = this.getValidationErrors();
    errors.push(error);
    this.setValidationErrors(errors);
  }

  // Clear validation errors
  clearValidationErrors(): void {
    this.validationErrors = undefined;
  }

  // Add service restriction
  addServiceRestriction(restriction: string): void {
    const restrictions = this.getServiceRestrictions();
    if (!restrictions.includes(restriction)) {
      restrictions.push(restriction);
      this.setServiceRestrictions(restrictions);
    }
  }

  // Remove service restriction
  removeServiceRestriction(restriction: string): void {
    const restrictions = this.getServiceRestrictions();
    const index = restrictions.indexOf(restriction);
    if (index > -1) {
      restrictions.splice(index, 1);
      this.setServiceRestrictions(restrictions);
    }
  }

  // Check if can attempt validation
  canAttemptValidation(): boolean {
    const maxAttempts = 3;
    const cooldownMinutes = 30;
    
    if (this.validationAttempts >= maxAttempts && this.lastValidationAttempt) {
      const cooldownTime = new Date(this.lastValidationAttempt.getTime() + cooldownMinutes * 60 * 1000);
      return new Date() > cooldownTime;
    }
    
    return true;
  }

  // Reset validation attempts
  resetValidationAttempts(): void {
    this.validationAttempts = 0;
    this.lastValidationAttempt = undefined;
  }

  // Calculate confidence based on data quality and age
  calculateConfidence(): number {
    let confidence = this.confidenceScore || 100;
    
    // Reduce confidence based on age
    if (this.validatedAt) {
      const daysSinceValidation = Math.floor(
        (Date.now() - this.validatedAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceValidation > 30) {
        confidence -= Math.min(daysSinceValidation - 30, 30);
      }
    }
    
    // Reduce confidence if approximate location
    if (this.isApproximateLocation) {
      confidence -= 10;
    }
    
    // Reduce confidence based on validation errors
    const errors = this.getValidationErrors();
    if (errors.length > 0) {
      confidence -= errors.length * 5;
    }
    
    return Math.max(0, Math.min(100, confidence));
  }

  // Check if CEP is in same city as another
  isSameCity(other: CEPValidation): boolean {
    return (
      this.city.toLowerCase() === other.city.toLowerCase() &&
      this.state === other.state
    );
  }

  // Check if CEP is in same state as another
  isSameState(other: CEPValidation): boolean {
    return this.state === other.state;
  }

  // Check if delivery is available
  isDeliveryAvailable(): boolean {
    return this.hasDeliveryService && this.status === CEPStatus.VALID;
  }

  // Static factory method
  static create(params: {
    cep: string;
    accountId?: string;
    businessUnitId?: string;
  }): CEPValidation {
    const validation = new CEPValidation();
    
    validation.cep = CEPValidation.cleanCEP(params.cep);
    validation.formattedCEP = CEPValidation.formatCEP(params.cep);
    validation.isValid = CEPValidation.isValidCEP(params.cep);
    
    if (params.accountId) validation.accountId = params.accountId;
    if (params.businessUnitId) validation.businessUnitId = params.businessUnitId;
    
    // Set initial status based on format validation
    if (!validation.isValid) {
      validation.status = CEPStatus.INVALID;
    }
    
    // Set initial expiration (immediate validation needed)
    validation.expiresAt = new Date();
    validation.needsRevalidation = true;
    
    return validation;
  }

  // Create from ViaCEP response
  static createFromViaCEP(data: any, params: { accountId?: string; businessUnitId?: string } = {}): CEPValidation {
    const validation = CEPValidation.create({
      cep: data.cep,
      ...params,
    });

    if (data.erro) {
      validation.status = CEPStatus.NOT_FOUND;
      return validation;
    }

    validation.street = data.logradouro || null;
    validation.complement = data.complemento || null;
    validation.neighborhood = data.bairro || null;
    validation.city = data.localidade;
    validation.state = data.uf;
    validation.ibgeCode = data.ibge || null;
    validation.giaCode = data.gia || null;
    validation.dddCode = data.ddd || null;
    validation.siafi = data.siafi || null;

    validation.validationSource = 'ViaCEP';
    validation.setRawData(data);
    validation.updateValidation({});

    return validation;
  }

  // Get basic address info for display
  getBasicAddress(): {
    street?: string;
    neighborhood?: string;
    city: string;
    state: string;
    cep: string;
  } {
    return {
      street: this.street || undefined,
      neighborhood: this.neighborhood || undefined,
      city: this.city,
      state: this.state,
      cep: this.formattedCEP,
    };
  }

  // Check if CEP represents a PO Box or special delivery area
  isPOBox(): boolean {
    const street = this.street?.toLowerCase() || '';
    return (
      street.includes('caixa postal') ||
      street.includes('cp ') ||
      street.includes('c.p.') ||
      street.includes('apartado') ||
      street.includes('a.c.') ||
      this.getServiceRestrictions().includes('po_box')
    );
  }

  // Check if CEP is in a rural area
  isRuralArea(): boolean {
    const street = this.street?.toLowerCase() || '';
    const neighborhood = this.neighborhood?.toLowerCase() || '';
    
    return (
      street.includes('zona rural') ||
      street.includes('área rural') ||
      street.includes('sítio') ||
      street.includes('fazenda') ||
      street.includes('chácara') ||
      neighborhood.includes('rural') ||
      this.getServiceRestrictions().includes('rural_area')
    );
  }
}
