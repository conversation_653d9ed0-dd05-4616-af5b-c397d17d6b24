import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Account } from '../account.entity';
import { BusinessUnit } from '../business-unit.entity';

export enum CNPJStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  NULL = 'null',
  INVALID = 'invalid',
}

export enum CompanyType {
  MEI = 'mei', // Microempreendedor Individual
  LTDA = 'ltda', // Sociedade Limitada
  SA = 'sa', // Sociedade Anônima
  EIRELI = 'eireli', // Empresa Individual de Responsabilidade Limitada
  EI = 'ei', // Empresário Individual
  OTHER = 'other',
}

export enum CompanySize {
  MEI = 'mei', // Microempreendedor Individual
  ME = 'me', // Microempresa
  EPP = 'epp', // Empresa de Pequeno Porte
  MEDIUM = 'medium', // Empresa de Médio Porte
  LARGE = 'large', // Empresa de Grande Porte
}

export enum TaxRegime {
  SIMPLES_NACIONAL = 'simples_nacional',
  LUCRO_PRESUMIDO = 'lucro_presumido',
  LUCRO_REAL = 'lucro_real',
  LUCRO_ARBITRADO = 'lucro_arbitrado',
  ISENTO = 'isento',
}

@Entity('cnpj_validations')
@Index(['cnpj'], { unique: true })
@Index(['accountId'])
@Index(['businessUnitId'])
@Index(['status'])
@Index(['validatedAt'])
@Index(['expiresAt'])
export class CNPJValidation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // CNPJ information
  @Column({ type: 'varchar', length: 14 })
  cnpj: string; // Raw CNPJ numbers only

  @Column({ type: 'varchar', length: 18 })
  formattedCNPJ: string; // Formatted CNPJ (XX.XXX.XXX/XXXX-XX)

  @Column({ type: 'boolean', default: true })
  isValid: boolean; // Whether CNPJ format and check digits are valid

  @Column({
    type: 'enum',
    enum: CNPJStatus,
    nullable: true,
  })
  status: CNPJStatus; // Status from Receita Federal

  // Company basic information
  @Column({ type: 'varchar', length: 255, nullable: true })
  companyName: string; // Razão Social

  @Column({ type: 'varchar', length: 255, nullable: true })
  tradeName: string; // Nome Fantasia

  @Column({
    type: 'enum',
    enum: CompanyType,
    nullable: true,
  })
  companyType: CompanyType;

  @Column({
    type: 'enum',
    enum: CompanySize,
    nullable: true,
  })
  companySize: CompanySize;

  @Column({
    type: 'enum',
    enum: TaxRegime,
    nullable: true,
  })
  taxRegime: TaxRegime;

  // Legal representative
  @Column({ type: 'varchar', length: 255, nullable: true })
  legalRepresentativeName: string;

  @Column({ type: 'varchar', length: 11, nullable: true })
  legalRepresentativeCPF: string;

  // Registration dates
  @Column({ type: 'date', nullable: true })
  registrationDate: Date; // Data de Abertura

  @Column({ type: 'date', nullable: true })
  lastUpdateDate: Date; // Data da Última Atualização

  // Address information
  @Column({ type: 'varchar', length: 8, nullable: true })
  zipCode: string; // CEP

  @Column({ type: 'varchar', length: 255, nullable: true })
  street: string; // Logradouro

  @Column({ type: 'varchar', length: 50, nullable: true })
  number: string; // Número

  @Column({ type: 'varchar', length: 100, nullable: true })
  complement: string; // Complemento

  @Column({ type: 'varchar', length: 100, nullable: true })
  neighborhood: string; // Bairro

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string; // Município

  @Column({ type: 'varchar', length: 2, nullable: true })
  state: string; // UF

  // Economic activity
  @Column({ type: 'varchar', length: 10, nullable: true })
  primaryCNAE: string; // CNAE Principal

  @Column({ type: 'varchar', length: 255, nullable: true })
  primaryCNAEDescription: string;

  @Column({ type: 'text', nullable: true })
  secondaryCNAEs: string; // JSON array of secondary CNAEs

  // Contact information
  @Column({ type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  email: string;

  // Share capital
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  shareCapital: number; // Capital Social

  // Federal tax situation
  @Column({ type: 'varchar', length: 100, nullable: true })
  federalTaxSituation: string; // Situação Cadastral RFB

  @Column({ type: 'date', nullable: true })
  federalTaxSituationDate: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  federalTaxSituationReason: string;

  // Special tax situations
  @Column({ type: 'boolean', default: false })
  isOptingSimples: boolean; // Optante pelo Simples Nacional

  @Column({ type: 'date', nullable: true })
  simplesOptingDate: Date;

  @Column({ type: 'date', nullable: true })
  simplesExclusionDate: Date;

  @Column({ type: 'boolean', default: false })
  isOptingMEI: boolean; // Optante pelo MEI

  @Column({ type: 'date', nullable: true })
  meiOptingDate: Date;

  // Validation metadata
  @Column({ type: 'varchar', length: 100, nullable: true })
  validationSource: string; // Source of validation (RFB, SINTEGRA, etc.)

  @Column({ type: 'timestamp', nullable: true })
  validatedAt: Date; // When validation was performed

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date; // When validation expires

  @Column({ type: 'boolean', default: false })
  needsRevalidation: boolean; // Whether revalidation is needed

  @Column({ type: 'text', nullable: true })
  validationErrors?: string; // JSON array of validation errors

  @Column({ type: 'text', nullable: true })
  rawData: string; // Raw validation response (JSON)

  @Column({ type: 'int', default: 0 })
  validationAttempts: number; // Number of validation attempts

  @Column({ type: 'timestamp', nullable: true })
  lastValidationAttempt?: Date; // Last validation attempt

  // Multi-tenant relationships
  @Column({ type: 'uuid', nullable: true })
  accountId: string;

  @ManyToOne(() => Account, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: Account;

  @Column({ type: 'uuid', nullable: true })
  businessUnitId: string;

  @ManyToOne(() => BusinessUnit, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: BusinessUnit;

  // Timestamps
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  get isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  get isActive(): boolean {
    return this.status === CNPJStatus.ACTIVE && this.isValid && !this.isExpired;
  }

  get isInactive(): boolean {
    return [CNPJStatus.SUSPENDED, CNPJStatus.CANCELLED, CNPJStatus.NULL].includes(this.status);
  }

  get isMEI(): boolean {
    return this.isOptingMEI || this.companySize === CompanySize.MEI;
  }

  get isSimples(): boolean {
    return this.isOptingSimples || this.taxRegime === TaxRegime.SIMPLES_NACIONAL;
  }

  get statusDisplayName(): string {
    const statusNames: Record<CNPJStatus, string> = {
      [CNPJStatus.ACTIVE]: 'Ativa',
      [CNPJStatus.SUSPENDED]: 'Suspensa',
      [CNPJStatus.CANCELLED]: 'Cancelada',
      [CNPJStatus.NULL]: 'Nula',
      [CNPJStatus.INVALID]: 'Inválida',
    };
    return statusNames[this.status] || 'Desconhecido';
  }

  get companyTypeDisplayName(): string {
    const typeNames: Record<CompanyType, string> = {
      [CompanyType.MEI]: 'Microempreendedor Individual',
      [CompanyType.LTDA]: 'Sociedade Limitada',
      [CompanyType.SA]: 'Sociedade Anônima',
      [CompanyType.EIRELI]: 'Empresa Individual de Responsabilidade Limitada',
      [CompanyType.EI]: 'Empresário Individual',
      [CompanyType.OTHER]: 'Outros',
    };
    return typeNames[this.companyType] || 'Não informado';
  }

  get companySizeDisplayName(): string {
    const sizeNames: Record<CompanySize, string> = {
      [CompanySize.MEI]: 'Microempreendedor Individual',
      [CompanySize.ME]: 'Microempresa',
      [CompanySize.EPP]: 'Empresa de Pequeno Porte',
      [CompanySize.MEDIUM]: 'Empresa de Médio Porte',
      [CompanySize.LARGE]: 'Empresa de Grande Porte',
    };
    return sizeNames[this.companySize] || 'Não informado';
  }

  get taxRegimeDisplayName(): string {
    const regimeNames: Record<TaxRegime, string> = {
      [TaxRegime.SIMPLES_NACIONAL]: 'Simples Nacional',
      [TaxRegime.LUCRO_PRESUMIDO]: 'Lucro Presumido',
      [TaxRegime.LUCRO_REAL]: 'Lucro Real',
      [TaxRegime.LUCRO_ARBITRADO]: 'Lucro Arbitrado',
      [TaxRegime.ISENTO]: 'Isento',
    };
    return regimeNames[this.taxRegime] || 'Não informado';
  }

  get fullAddress(): string {
    const parts: string[] = [];
    
    if (this.street) parts.push(this.street);
    if (this.number) parts.push(this.number);
    if (this.complement) parts.push(this.complement);
    if (this.neighborhood) parts.push(this.neighborhood);
    if (this.city) parts.push(this.city);
    if (this.state) parts.push(this.state);
    if (this.zipCode) parts.push(this.zipCode);
    
    return parts.join(', ');
  }

  // Parse JSON fields safely
  getSecondaryCNAEs(): Array<{code: string, description: string}> {
    try {
      return this.secondaryCNAEs ? JSON.parse(this.secondaryCNAEs) : [];
    } catch {
      return [];
    }
  }

  getValidationErrors(): string[] {
    try {
      return this.validationErrors ? JSON.parse(this.validationErrors) : [];
    } catch {
      return [];
    }
  }

  getRawData(): Record<string, any> | null {
    try {
      return this.rawData ? JSON.parse(this.rawData) : null;
    } catch {
      return null;
    }
  }

  // Set JSON fields safely
  setSecondaryCNAEs(cnaes: Array<{code: string, description: string}>): void {
    this.secondaryCNAEs = JSON.stringify(cnaes);
  }

  setValidationErrors(errors: string[]): void {
    this.validationErrors = JSON.stringify(errors);
  }

  setRawData(data: Record<string, any>): void {
    this.rawData = JSON.stringify(data);
  }

  // Validation methods
  static isValidCNPJ(cnpj: string): boolean {
    // Remove non-numeric characters
    const cleanCNPJ = cnpj.replace(/\D/g, '');
    
    // Check if has 14 digits
    if (cleanCNPJ.length !== 14) return false;
    
    // Check if all digits are the same
    if (/^(\d)\1+$/.test(cleanCNPJ)) return false;
    
    // Validate check digits
    return CNPJValidation.validateCheckDigits(cleanCNPJ);
  }

  private static validateCheckDigits(cnpj: string): boolean {
    // First check digit
    let sum = 0;
    const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    
    for (let i = 0; i < 12; i++) {
      sum += parseInt(cnpj[i]) * weights1[i];
    }
    
    let remainder = sum % 11;
    const firstDigit = remainder < 2 ? 0 : 11 - remainder;
    
    if (parseInt(cnpj[12]) !== firstDigit) return false;
    
    // Second check digit
    sum = 0;
    const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    
    for (let i = 0; i < 13; i++) {
      sum += parseInt(cnpj[i]) * weights2[i];
    }
    
    remainder = sum % 11;
    const secondDigit = remainder < 2 ? 0 : 11 - remainder;
    
    return parseInt(cnpj[13]) === secondDigit;
  }

  static formatCNPJ(cnpj: string): string {
    const cleanCNPJ = cnpj.replace(/\D/g, '');
    return cleanCNPJ.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
  }

  static cleanCNPJ(cnpj: string): string {
    return cnpj.replace(/\D/g, '');
  }

  // Update validation data
  updateValidation(data: Partial<CNPJValidation>): void {
    Object.assign(this, data);
    this.validatedAt = new Date();
    this.needsRevalidation = false;
    
    // Set expiration date (30 days from now)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 30);
    this.expiresAt = expirationDate;
  }

  // Mark as needing revalidation
  markForRevalidation(): void {
    this.needsRevalidation = true;
  }

  // Increment validation attempts
  incrementValidationAttempts(): void {
    this.validationAttempts++;
    this.lastValidationAttempt = new Date();
  }

  // Add validation error
  addValidationError(error: string): void {
    const errors = this.getValidationErrors();
    errors.push(error);
    this.setValidationErrors(errors);
  }

  // Clear validation errors
  clearValidationErrors(): void {
    this.validationErrors = undefined;
  }

  // Check if can attempt validation
  canAttemptValidation(): boolean {
    const maxAttempts = 5;
    const cooldownHours = 1;
    
    if (this.validationAttempts >= maxAttempts && this.lastValidationAttempt) {
      const cooldownTime = new Date(this.lastValidationAttempt.getTime() + cooldownHours * 60 * 60 * 1000);
      return new Date() > cooldownTime;
    }
    
    return true;
  }

  // Reset validation attempts
  resetValidationAttempts(): void {
    this.validationAttempts = 0;
    this.lastValidationAttempt = undefined;
  }

  // Static factory method
  static create(params: {
    cnpj: string;
    accountId?: string;
    businessUnitId?: string;
  }): CNPJValidation {
    const validation = new CNPJValidation();
    
    validation.cnpj = CNPJValidation.cleanCNPJ(params.cnpj);
    validation.formattedCNPJ = CNPJValidation.formatCNPJ(params.cnpj);
    validation.isValid = CNPJValidation.isValidCNPJ(params.cnpj);
    
    if (params.accountId) validation.accountId = params.accountId;
    if (params.businessUnitId) validation.businessUnitId = params.businessUnitId;
    
    // Set initial expiration (immediate validation needed)
    validation.expiresAt = new Date();
    validation.needsRevalidation = true;
    
    return validation;
  }
}
