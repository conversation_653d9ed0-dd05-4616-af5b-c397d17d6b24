import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { IntegrationService } from '../integration.service';
import axios from 'axios';

@Processor('webhook:dispatch')
export class WebhookProcessor {
  private readonly logger = new Logger(WebhookProcessor.name);

  constructor(private readonly integrationService: IntegrationService) {}

  @Process('dispatch')
  async handleDispatch(
    job: Job<{ url: string; payload: any; secret: string }>,
  ) {
    this.logger.debug(
      `Processing webhook dispatch job ${job.id} to ${job.data.url}`,
    );

    try {
      const { url, payload, secret } = job.data;

      // Generate signature if secret is provided
      const signature = secret
        ? this.integrationService.generateSignature(payload, secret)
        : undefined;

      // Send the webhook
      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          ...(signature && { 'X-Signature': signature }),
        },
        timeout: 5000, // 5 seconds timeout
      });

      this.logger.debug(`Webhook sent successfully: ${response.status}`);
      job.updateProgress(100);
    } catch (error) {
      this.logger.error(`Error sending webhook: ${error.message}`, error.stack);

      // Determine if we should retry
      const attemptsMade = job.attemptsMade || 0;
      if (attemptsMade < 5) {
        // Will be retried automatically by Bull
        throw error;
      } else {
        // Max retries reached, log and move on
        this.logger.warn(
          `Max retries reached for webhook job ${job.id}, giving up`,
        );
      }
    }
  }
}
