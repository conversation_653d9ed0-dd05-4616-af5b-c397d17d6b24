import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { Account } from '../../accounts/entities/account.entity';

@Entity('api_keys')
export class ApiKey {
  @ApiProperty({
    description: 'Unique identifier for the API key',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiHideProperty()
  @Column()
  accountId: string;

  @ApiProperty({
    description: 'The API key value (only shown once upon creation)',
    example: 'sk_test_51JU4DjLkdIwDlEn3XiPWeXmZkRvV6rJkpZBHZfwEjJU4DjLkdIwDlEn3X',
  })
  @Column({ unique: true })
  key: string;

  @ApiProperty({
    description: 'Name of the API key for identification purposes',
    example: 'Production Integration',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Whether the API key is active',
    example: true,
  })
  @Column({ default: true })
  active: boolean;

  @ApiProperty({
    description: 'When the API key was created',
    example: '2023-01-15T12:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the API key was last updated',
    example: '2023-01-15T12:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiHideProperty()
  @ManyToOne(() => Account, (account) => account.apiKeys, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'accountId' })
  account: Account;
}
