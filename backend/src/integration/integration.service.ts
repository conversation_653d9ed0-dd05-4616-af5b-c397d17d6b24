import { InjectQueue } from '@nestjs/bull';
import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bullmq';
import { Repository } from 'typeorm';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';
import { Account } from '../accounts/entities/account.entity';
import { Emitter } from '../emitters/entities/emitter.entity';
import { CreateNfeDto } from '../nfe/dto/create-nfe.dto';
import { NfeService } from '../nfe/nfe.service';
import { ApiKey } from './entities/api-key.entity';
// Import crypto as a Node.js module
import * as crypto from 'crypto';

@Injectable()
export class IntegrationService {
  constructor(
    @InjectRepository(ApiKey)
    private apiKeysRepository: Repository<ApiKey>,
    @InjectRepository(Account)
    private accountsRepository: Repository<Account>,
    @InjectRepository(Emitter)
    private emittersRepository: Repository<Emitter>,
    @InjectQueue('webhook:dispatch') private webhookQueue: Queue,
    private nfeService: NfeService,
  ) {}

  /**
   * List API keys for the current account
   */
  async listApiKeys(accountId: string): Promise<ApiKey[]> {
    return this.apiKeysRepository.find({ where: { accountId } });
  }

  /**
   * Create a new API key with given name for the account
   */
  async createApiKey(
    accountId: string,
    createApiKeyDto: CreateApiKeyDto,
  ): Promise<ApiKey> {
    const { name } = createApiKeyDto;
    // Generate a random secure key
    const key = crypto.randomBytes(32).toString('hex');
    const apiKey = this.apiKeysRepository.create({
      accountId,
      name,
      key,
      active: true,
    });
    return this.apiKeysRepository.save(apiKey);
  }

  /**
   * Update an existing API key's active status
   */
  async updateApiKey(
    accountId: string,
    id: string,
    updateApiKeyDto: UpdateApiKeyDto,
  ): Promise<ApiKey> {
    const apiKey = await this.apiKeysRepository.findOne({ where: { id } });
    if (!apiKey || apiKey.accountId !== accountId) {
      throw new NotFoundException('API key not found');
    }
    apiKey.active = updateApiKeyDto.active;
    return this.apiKeysRepository.save(apiKey);
  }

  /**
   * Delete (revoke) an API key
   */
  async deleteApiKey(accountId: string, id: string): Promise<void> {
    const apiKey = await this.apiKeysRepository.findOne({ where: { id } });
    if (!apiKey || apiKey.accountId !== accountId) {
      throw new NotFoundException('API key not found');
    }
    await this.apiKeysRepository.delete(id);
  }

  async validateApiKey(apiKey: string): Promise<ApiKey> {
    const key = await this.apiKeysRepository.findOne({
      where: { key: apiKey, active: true },
      relations: ['account'],
    });

    if (!key) {
      throw new UnauthorizedException('Invalid API key');
    }

    return key;
  }

  async createNfe(apiKey: string, createNfeDto: CreateNfeDto) {
    const key = await this.validateApiKey(apiKey);

    // Check if the emitter belongs to the account
    const emitter = await this.emittersRepository.findOne({
      where: { id: createNfeDto.emitterId, accountId: key.accountId },
    });

    if (!emitter) {
      throw new NotFoundException(
        `Emitter with ID ${createNfeDto.emitterId} not found`,
      );
    }

    return this.nfeService.create(key.accountId, createNfeDto);
  }

  async getNfeStatus(apiKey: string, nfeId: string) {
    const key = await this.validateApiKey(apiKey);
    return this.nfeService.findOne(nfeId, key.accountId);
  }

  async dispatchWebhook(
    emitterId: string,
    nfeId: string,
    status: string,
    protocol: string,
  ) {
    const emitter = await this.emittersRepository.findOne({
      where: { id: emitterId },
    });

    if (!emitter || !emitter.webhookUrl) {
      return;
    }

    const payload = {
      nfeId,
      status,
      protocol,
      timestamp: new Date().toISOString(),
    };

    // Add to webhook dispatch queue
    await this.webhookQueue.add('dispatch', {
      url: emitter.webhookUrl,
      payload,
      secret: emitter.webhookSecret,
    });
  }

  generateSignature(payload: any, secret: string): string {
    try {
      return crypto
        .createHmac('sha256', secret)
        .update(JSON.stringify(payload))
        .digest('hex');
    } catch (error) {
      console.error('Error generating signature:', error);
      // Fallback implementation (not secure, just for development)
      const data = JSON.stringify(payload) + secret;
      let hash = 0;
      for (let i = 0; i < data.length; i++) {
        hash = (hash << 5) - hash + data.charCodeAt(i);
        hash |= 0; // Convert to 32bit integer
      }
      return Math.abs(hash).toString(16).padStart(64, '0');
    }
  }
}
