import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Account } from '../accounts/entities/account.entity';
import { EmittersModule } from '../emitters/emitters.module';
import { Emitter } from '../emitters/entities/emitter.entity';
import { NfeModule } from '../nfe/nfe.module';
import { ApiKey } from './entities/api-key.entity';
import { IntegrationController } from './integration.controller';
import { ApiKeysController } from './api-keys.controller';
import { IntegrationService } from './integration.service';
import { WebhookProcessor } from './processors/webhook.processor';

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiKey, Account, Emitter]),
    BullModule.registerQueue({ name: 'webhook:dispatch' }),
    NfeModule,
    EmittersModule,
  ],
  controllers: [IntegrationController, ApiKeysController],
  providers: [IntegrationService, WebhookProcessor],
  exports: [IntegrationService],
})
export class IntegrationModule {}
