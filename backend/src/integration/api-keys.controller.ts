import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { IntegrationService } from './integration.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';
import { ApiKey } from './entities/api-key.entity';

/**
 * Controller for managing API keys for integrations
 */
@ApiTags('API Keys')
@ApiBearerAuth()
@Controller('api-keys')
@UseGuards(JwtAuthGuard)
export class ApiKeysController {
  constructor(private readonly integrationService: IntegrationService) {}

  /** List all API keys for the current account */
  @ApiOperation({ summary: 'List all API keys for the current account' })
  @ApiResponse({
    status: 200,
    description: 'List of API keys',
    type: [ApiKey],
  })
  @Get()
  async list(@CurrentUser() user: any) {
    return this.integrationService.listApiKeys(user.accountId);
  }

  /** Create a new API key */
  @ApiOperation({ summary: 'Create a new API key' })
  @ApiBody({ type: CreateApiKeyDto })
  @ApiResponse({
    status: 201,
    description: 'The API key has been successfully created',
    type: ApiKey,
  })
  @Post()
  async create(@Body() createDto: CreateApiKeyDto, @CurrentUser() user: any) {
    return this.integrationService.createApiKey(user.accountId, createDto);
  }

  /** Update active status of an API key */
  @ApiOperation({ summary: 'Update active status of an API key' })
  @ApiParam({ name: 'id', description: 'API key ID' })
  @ApiBody({ type: UpdateApiKeyDto })
  @ApiResponse({
    status: 200,
    description: 'The API key has been successfully updated',
    type: ApiKey,
  })
  @ApiResponse({
    status: 404,
    description: 'API key not found',
  })
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateApiKeyDto,
    @CurrentUser() user: any,
  ) {
    return this.integrationService.updateApiKey(user.accountId, id, updateDto);
  }

  /** Delete (revoke) an API key */
  @ApiOperation({ summary: 'Delete (revoke) an API key' })
  @ApiParam({ name: 'id', description: 'API key ID' })
  @ApiResponse({
    status: 200,
    description: 'The API key has been successfully deleted',
  })
  @ApiResponse({
    status: 404,
    description: 'API key not found',
  })
  @Delete(':id')
  async delete(@Param('id') id: string, @CurrentUser() user: any) {
    await this.integrationService.deleteApiKey(user.accountId, id);
    return { success: true };
  }
}
