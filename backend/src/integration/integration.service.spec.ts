import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IntegrationService } from './integration.service';
import { ApiKey } from './entities/api-key.entity';
import { Account } from '../accounts/entities/account.entity';
import { Emitter } from '../emitters/entities/emitter.entity';
import { NfeService } from '../nfe/nfe.service';
import * as crypto from 'crypto';

const mockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  delete: jest.fn(),
});

const mockQueue = {
  add: jest.fn(),
};

const mockNfeService = () => ({
  create: jest.fn(),
  findOne: jest.fn(),
});

describe('IntegrationService', () => {
  let service: IntegrationService;
  let apiKeysRepository: Repository<ApiKey>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationService,
        {
          provide: getRepositoryToken(ApiKey),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(Account),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(Emitter),
          useFactory: mockRepository,
        },
        {
          provide: 'BullQueue_webhook:dispatch',
          useValue: mockQueue,
        },
        {
          provide: NfeService,
          useFactory: mockNfeService,
        },
      ],
    }).compile();

    service = module.get<IntegrationService>(IntegrationService);
    apiKeysRepository = module.get<Repository<ApiKey>>(
      getRepositoryToken(ApiKey),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateSignature', () => {
    it('should generate a valid HMAC SHA-256 signature', () => {
      const payload = { id: 'test-id', status: 'success' };
      const secret = 'test-secret';

      // Create a real signature for comparison
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(JSON.stringify(payload))
        .digest('hex');

      const result = service.generateSignature(payload, secret);

      expect(result).toEqual(expectedSignature);
    });

    it('should handle errors gracefully', () => {
      // Mock crypto.createHmac to throw an error
      jest.spyOn(crypto, 'createHmac').mockImplementation(() => {
        throw new Error('Mocked error');
      });

      const payload = { id: 'test-id', status: 'success' };
      const secret = 'test-secret';

      const result = service.generateSignature(payload, secret);

      // Should use fallback implementation
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('dispatchWebhook', () => {
    it('should add a job to the webhook queue with the correct payload and signature', async () => {
      const emitterId = 'test-emitter-id';
      const nfeId = 'test-nfe-id';
      const status = 'issued';
      const protocol = 'test-protocol';
      
      const mockEmitter = {
        id: emitterId,
        webhookUrl: 'https://example.com/webhook',
        webhookSecret: 'test-secret',
      };

      jest.spyOn(service['emittersRepository'], 'findOne').mockResolvedValue(mockEmitter as any);
      
      await service.dispatchWebhook(emitterId, nfeId, status, protocol);

      const expectedPayload = {
        nfeId,
        status,
        protocol,
        timestamp: expect.any(String),
      };

      expect(mockQueue.add).toHaveBeenCalledWith('dispatch', {
        url: mockEmitter.webhookUrl,
        payload: expectedPayload,
        secret: mockEmitter.webhookSecret,
      });
    });

    it('should not dispatch a webhook if emitter is not found', async () => {
      jest.spyOn(service['emittersRepository'], 'findOne').mockResolvedValue(null);
      
      await service.dispatchWebhook('test-emitter-id', 'test-nfe-id', 'issued', 'test-protocol');

      expect(mockQueue.add).not.toHaveBeenCalled();
    });

    it('should not dispatch a webhook if webhookUrl is not set', async () => {
      const mockEmitter = {
        id: 'test-emitter-id',
        webhookUrl: null,
        webhookSecret: 'test-secret',
      };

      jest.spyOn(service['emittersRepository'], 'findOne').mockResolvedValue(mockEmitter as any);
      
      await service.dispatchWebhook('test-emitter-id', 'test-nfe-id', 'issued', 'test-protocol');

      expect(mockQueue.add).not.toHaveBeenCalled();
    });
  });
});
