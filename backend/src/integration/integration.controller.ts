import {
  <PERSON>,
  Post,
  Get,
  Body,
  Param,
  Headers,
  UnauthorizedException,
} from '@nestjs/common';
import { IntegrationService } from './integration.service';
import { CreateNfeDto } from '../nfe/dto/create-nfe.dto';

@Controller('external')
export class IntegrationController {
  constructor(private readonly integrationService: IntegrationService) {}

  @Post('nfe')
  async createNfe(
    @Headers('x-api-key') apiKey: string,
    @Body() createNfeDto: CreateNfeDto,
  ) {
    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    return this.integrationService.createNfe(apiKey, createNfeDto);
  }

  @Get('nfe/:id')
  async getNfeStatus(
    @Headers('x-api-key') apiKey: string,
    @Param('id') id: string,
  ) {
    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    return this.integrationService.getNfeStatus(apiKey, id);
  }
}
