import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { CurrentUser } from './decorators/current-user.decorator';
import { LogoutDto } from './dto/logout.dto';
import { SignInDto } from './dto/sign-in.dto';
import { SignUpDto } from './dto/sign-up.dto';
import { SwitchEmitterDto } from './dto/switch-emitter.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  async signUp(@Body() signUpDto: SignUpDto) {
    return this.authService.signUp(signUpDto);
  }

  @Post('signin')
  async signIn(@Body() signInDto: SignInDto) {
    return this.authService.signIn(signInDto);
  }

  /**
   * Exchange a refresh token for a new access token
   */
  @Post('refresh')
  async refresh(@Body('refreshToken') refreshToken: string) {
    return this.authService.refresh(refreshToken);
  }

  /**
   * Logout a user by revoking their refresh token
   */
  @Post('logout')
  async logout(@Body() logoutDto: LogoutDto) {
    return this.authService.logout(logoutDto.refreshToken);
  }

  /**
   * Switch the emitter context for a user
   */
  @UseGuards(JwtAuthGuard)
  @Post('switch-emitter')
  async switchEmitter(
    @CurrentUser() user,
    @Body() switchEmitterDto: SwitchEmitterDto,
    @Body('refreshToken') refreshToken?: string,
  ) {
    return this.authService.switchEmitter(
      user.id,
      user.accountId,
      switchEmitterDto.emitterId,
      refreshToken,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getProfile(@CurrentUser() user) {
    // Get available emitters for the user's account
    const emitters = await this.authService.getEmittersForAccount(user.accountId);
    
    return {
      ...user,
      availableEmitters: emitters,
    };
  }
}
