import { NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmittersService } from '../emitters/emitters.service';
import { UsersService } from '../users/users.service';
import { AuthService } from './auth.service';
import { RefreshToken } from './entities/refresh-token.entity';

const mockRefreshTokenRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  delete: jest.fn(),
});

const mockUsersService = () => ({
  create: jest.fn(),
  validateUser: jest.fn(),
  findById: jest.fn(),
});

const mockJwtService = () => ({
  sign: jest.fn(() => 'test-token'),
});

const mockEmittersService = () => ({
  findAll: jest.fn(),
  findOne: jest.fn(),
});

describe('AuthService', () => {
  let service: AuthService;
  let refreshTokenRepository: Repository<RefreshToken>;
  let usersService: UsersService;
  let emittersService: EmittersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(RefreshToken),
          useFactory: mockRefreshTokenRepository,
        },
        {
          provide: UsersService,
          useFactory: mockUsersService,
        },
        {
          provide: JwtService,
          useFactory: mockJwtService,
        },
        {
          provide: EmittersService,
          useFactory: mockEmittersService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    refreshTokenRepository = module.get<Repository<RefreshToken>>(
      getRepositoryToken(RefreshToken),
    );
    usersService = module.get<UsersService>(UsersService);
    emittersService = module.get<EmittersService>(EmittersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('logout', () => {
    it('should delete the refresh token and return success', async () => {
      const refreshToken = 'test-refresh-token';
      const mockTokenEntity = {
        id: 'token-id',
        token: refreshToken,
      };

      jest
        .spyOn(refreshTokenRepository, 'findOne')
        .mockResolvedValue(mockTokenEntity as any);
      jest
        .spyOn(refreshTokenRepository, 'delete')
        .mockResolvedValue({ affected: 1 } as any);

      const result = await service.logout(refreshToken);

      expect(refreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: refreshToken },
      });
      expect(refreshTokenRepository.delete).toHaveBeenCalledWith(
        mockTokenEntity.id,
      );
      expect(result).toEqual({ success: true });
    });

    it('should throw NotFoundException if token is not found', async () => {
      const refreshToken = 'non-existent-token';

      jest.spyOn(refreshTokenRepository, 'findOne').mockResolvedValue(null);

      await expect(service.logout(refreshToken)).rejects.toThrow(
        NotFoundException,
      );
      expect(refreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: refreshToken },
      });
    });
  });

  describe('refresh', () => {
    it('should throw UnauthorizedException if token is not found', async () => {
      const refreshToken = 'non-existent-token';

      jest.spyOn(refreshTokenRepository, 'findOne').mockResolvedValue(null);

      await expect(service.refresh(refreshToken)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(refreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: refreshToken },
      });
    });

    it('should throw UnauthorizedException if token is expired', async () => {
      const refreshToken = 'expired-token';
      const mockTokenEntity = {
        id: 'token-id',
        token: refreshToken,
        expiresAt: new Date(Date.now() - 1000), // Expired
      };

      jest
        .spyOn(refreshTokenRepository, 'findOne')
        .mockResolvedValue(mockTokenEntity as any);
      jest
        .spyOn(refreshTokenRepository, 'delete')
        .mockResolvedValue({ affected: 1 } as any);

      await expect(service.refresh(refreshToken)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(refreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: refreshToken },
      });
      expect(refreshTokenRepository.delete).toHaveBeenCalledWith(
        mockTokenEntity.id,
      );
    });
  });

  describe('switchEmitter', () => {
    const userId = 'user-id';
    const accountId = 'account-id';
    const emitterId = 'emitter-id';
    const refreshToken = 'refresh-token';

    it('should switch emitter context and return new access token', async () => {
      // Mock emitter
      const mockEmitter = { id: emitterId, name: 'Test Emitter' };
      jest
        .spyOn(emittersService, 'findOne')
        .mockResolvedValue(mockEmitter as any);

      // Mock user
      const mockUser = {
        id: userId,
        email: '<EMAIL>',
        userAccounts: [
          {
            accountId,
            role: 'owner',
            account: { id: accountId, name: 'Test Account' },
          },
        ],
      };
      jest.spyOn(usersService, 'findById').mockResolvedValue(mockUser as any);

      const result = await service.switchEmitter(userId, accountId, emitterId);

      expect(emittersService.findOne).toHaveBeenCalledWith(
        emitterId,
        accountId,
      );
      expect(usersService.findById).toHaveBeenCalledWith(userId);
      expect(result).toHaveProperty('accessToken');
      expect(result.user).toHaveProperty('emitterId', emitterId);
    });

    it('should throw NotFoundException if emitter is not found', async () => {
      jest.spyOn(emittersService, 'findOne').mockResolvedValue(null);

      await expect(
        service.switchEmitter(userId, accountId, emitterId),
      ).rejects.toThrow(NotFoundException);

      expect(emittersService.findOne).toHaveBeenCalledWith(
        emitterId,
        accountId,
      );
    });

    it('should rotate refresh token if provided', async () => {
      // Mock emitter
      const mockEmitter = { id: emitterId, name: 'Test Emitter' };
      jest
        .spyOn(emittersService, 'findOne')
        .mockResolvedValue(mockEmitter as any);

      // Mock user
      const mockUser = {
        id: userId,
        email: '<EMAIL>',
        userAccounts: [
          {
            accountId,
            role: 'owner',
            account: { id: accountId, name: 'Test Account' },
          },
        ],
      };
      jest.spyOn(usersService, 'findById').mockResolvedValue(mockUser as any);

      // Mock refresh token
      const mockTokenEntity = {
        id: 'token-id',
        token: refreshToken,
        expiresAt: new Date(Date.now() + 1000 * 60 * 60), // Not expired
      };
      jest
        .spyOn(refreshTokenRepository, 'findOne')
        .mockResolvedValue(mockTokenEntity as any);
      jest
        .spyOn(refreshTokenRepository, 'save')
        .mockResolvedValue(mockTokenEntity as any);

      const result = await service.switchEmitter(
        userId,
        accountId,
        emitterId,
        refreshToken,
      );

      expect(refreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: refreshToken },
      });
      expect(refreshTokenRepository.save).toHaveBeenCalled();
      expect(result).toHaveProperty('refreshToken');
    });

    it('should throw NotFoundException if refresh token is not found', async () => {
      // Mock emitter
      const mockEmitter = { id: emitterId, name: 'Test Emitter' };
      jest
        .spyOn(emittersService, 'findOne')
        .mockResolvedValue(mockEmitter as any);

      // Mock refresh token
      jest.spyOn(refreshTokenRepository, 'findOne').mockResolvedValue(null);

      await expect(
        service.switchEmitter(
          userId,
          accountId,
          emitterId,
          'non-existent-token',
        ),
      ).rejects.toThrow(NotFoundException);

      expect(refreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: 'non-existent-token' },
      });
    });
  });

  describe('getEmittersForAccount', () => {
    it('should return emitters for the account', async () => {
      const accountId = 'account-id';
      const mockEmitters = [
        { id: 'emitter-1', name: 'Emitter 1' },
        { id: 'emitter-2', name: 'Emitter 2' },
      ];

      jest
        .spyOn(emittersService, 'findAll')
        .mockResolvedValue(mockEmitters as any);

      const result = await service.getEmittersForAccount(accountId);

      expect(emittersService.findAll).toHaveBeenCalledWith(accountId);
      expect(result).toEqual(mockEmitters);
    });
  });
});
