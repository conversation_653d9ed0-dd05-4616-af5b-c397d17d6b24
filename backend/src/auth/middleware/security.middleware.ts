import { Injectable, NestMiddleware, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

export interface SecurityOptions {
  rateLimiting?: {
    windowMs: number;
    max: number;
    message?: string;
    skipSuccessfulRequests?: boolean;
  };
  helmet?: {
    contentSecurityPolicy?: boolean;
    crossOriginEmbedderPolicy?: boolean;
    crossOriginOpenerPolicy?: boolean;
    crossOriginResourcePolicy?: boolean;
    dnsPrefetchControl?: boolean;
    frameguard?: boolean;
    hidePoweredBy?: boolean;
    hsts?: boolean;
    ieNoOpen?: boolean;
    noSniff?: boolean;
    originAgentCluster?: boolean;
    permittedCrossDomainPolicies?: boolean;
    referrerPolicy?: boolean;
    xssFilter?: boolean;
  };
}

@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  private rateLimitMap = new Map<string, { count: number; resetTime: number }>();

  constructor(private readonly options: SecurityOptions = {}) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Apply security headers first
    this.applySecurityHeaders(res);

    // Apply rate limiting if configured
    if (this.options.rateLimiting) {
      if (!this.checkRateLimit(req)) {
        throw new HttpException(
          this.options.rateLimiting.message || 'Too many requests',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
    }

    next();
  }

  private applySecurityHeaders(res: Response): void {
    // Default security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
    
    // HSTS for HTTPS
    if (process.env.NODE_ENV === 'production') {
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }

    // Content Security Policy
    if (this.options.helmet?.contentSecurityPolicy !== false) {
      res.setHeader(
        'Content-Security-Policy',
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "connect-src 'self'; " +
        "font-src 'self'; " +
        "object-src 'none'; " +
        "media-src 'self'; " +
        "frame-src 'none';"
      );
    }

    // Cross-Origin headers
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
    res.setHeader('Cross-Origin-Resource-Policy', 'same-site');

    // Hide server information
    res.removeHeader('X-Powered-By');
  }

  private checkRateLimit(req: Request): boolean {
    if (!this.options.rateLimiting) return true;

    const clientId = this.getClientIdentifier(req);
    const now = Date.now();
    const windowMs = this.options.rateLimiting.windowMs;
    const maxRequests = this.options.rateLimiting.max;

    const clientData = this.rateLimitMap.get(clientId);

    if (!clientData || now > clientData.resetTime) {
      // First request or window expired, create new entry
      this.rateLimitMap.set(clientId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return true;
    }

    if (clientData.count >= maxRequests) {
      return false;
    }

    // Increment counter
    clientData.count++;
    return true;
  }

  private getClientIdentifier(req: Request): string {
    // Use IP address as client identifier
    const forwarded = req.headers['x-forwarded-for'];
    const ip = (forwarded as string)?.split(',')[0] || req.connection.remoteAddress || req.ip;
    return `ip:${ip}`;
  }

  // Cleanup expired entries periodically
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    for (const [key, data] of this.rateLimitMap.entries()) {
      if (now > data.resetTime) {
        this.rateLimitMap.delete(key);
      }
    }
  }
}

// Factory function to create middleware with options
export function createSecurityMiddleware(options?: SecurityOptions): SecurityMiddleware {
  return new SecurityMiddleware(options);
}

// Authentication-specific rate limiting
@Injectable()
export class AuthRateLimitMiddleware implements NestMiddleware {
  private attemptMap = new Map<string, { count: number; resetTime: number; blockedUntil?: number }>();

  constructor(
    private readonly windowMs: number = 15 * 60 * 1000, // 15 minutes
    private readonly maxAttempts: number = 5,
    private readonly blockDuration: number = 15 * 60 * 1000, // 15 minutes
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    const clientId = this.getClientIdentifier(req);
    const now = Date.now();

    const attemptData = this.attemptMap.get(clientId);

    // Check if client is currently blocked
    if (attemptData?.blockedUntil && now < attemptData.blockedUntil) {
      throw new HttpException(
        'Too many failed authentication attempts. Please try again later.',
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Clean up expired or unblocked entries
    if (!attemptData || now > attemptData.resetTime || (attemptData.blockedUntil && now > attemptData.blockedUntil)) {
      this.attemptMap.set(clientId, {
        count: 0,
        resetTime: now + this.windowMs,
      });
    }

    next();
  }

  // Call this method when authentication fails
  recordFailedAttempt(req: Request): void {
    const clientId = this.getClientIdentifier(req);
    const now = Date.now();
    const attemptData = this.attemptMap.get(clientId) || {
      count: 0,
      resetTime: now + this.windowMs,
    };

    attemptData.count++;

    if (attemptData.count >= this.maxAttempts) {
      attemptData.blockedUntil = now + this.blockDuration;
    }

    this.attemptMap.set(clientId, attemptData);
  }

  // Call this method when authentication succeeds
  recordSuccessfulAttempt(req: Request): void {
    const clientId = this.getClientIdentifier(req);
    this.attemptMap.delete(clientId);
  }

  private getClientIdentifier(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    const ip = (forwarded as string)?.split(',')[0] || req.connection.remoteAddress || req.ip;
    return `auth:${ip}`;
  }
}
