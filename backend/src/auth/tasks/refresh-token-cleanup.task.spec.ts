import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../database/entities/user.entity';
import { RefreshTokenCleanupTask } from './refresh-token-cleanup.task';

const mockUserRepository = () => ({
  find: jest.fn(),
  update: jest.fn(),
});

describe('RefreshTokenCleanupTask', () => {
  let task: RefreshTokenCleanupTask;
  let userRepository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RefreshTokenCleanupTask,
        {
          provide: getRepositoryToken(User),
          useFactory: mockUserRepository,
        },
      ],
    }).compile();

    task = module.get<RefreshTokenCleanupTask>(RefreshTokenCleanupTask);
    userRepository = module.get<Repository<User>>(
      getRepositoryToken(User),
    );
  });

  it('should be defined', () => {
    expect(task).toBeDefined();
  });

  describe('handleCleanup', () => {
    it('should clear expired refresh tokens from users', async () => {
      const expiredUsers = [
        { id: 'user1' },
        { id: 'user2' },
      ];
      const updateResult = { affected: 2 };
      
      jest.spyOn(userRepository, 'find').mockResolvedValue(expiredUsers as any);
      jest.spyOn(userRepository, 'update').mockResolvedValue(updateResult as any);

      await task.handleCleanup();

      expect(userRepository.find).toHaveBeenCalledWith({
        where: {
          refreshTokenExpiresAt: expect.any(Object),
        },
        select: ['id'],
      });
      
      expect(userRepository.update).toHaveBeenCalledWith(
        {
          refreshTokenExpiresAt: expect.any(Object),
        },
        {
          refreshToken: undefined,
          refreshTokenExpiresAt: undefined,
        }
      );
    });

    it('should handle case when no expired tokens exist', async () => {
      jest.spyOn(userRepository, 'find').mockResolvedValue([]);

      await task.handleCleanup();

      expect(userRepository.find).toHaveBeenCalled();
      expect(userRepository.update).not.toHaveBeenCalled();
    });
  });
});
