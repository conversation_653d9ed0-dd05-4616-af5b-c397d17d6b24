import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON>Than } from 'typeorm';
import { RefreshToken } from '../entities/refresh-token.entity';

@Injectable()
export class RefreshTokenCleanupTask {
  private readonly logger = new Logger(RefreshTokenCleanupTask.name);

  constructor(
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
  ) {}

  /**
   * Cleans up expired refresh tokens every hour.
   * Deletes all refresh tokens that have passed their expiration date.
   */
  @Cron(CronExpression.EVERY_HOUR)
  async handleCleanup() {
    this.logger.debug('Running refresh token cleanup');
    const now = new Date();
    
    // Find and delete expired refresh tokens
    const result = await this.refreshTokenRepository.delete({
      expiresAt: <PERSON><PERSON><PERSON>(now),
    });
    
    const count = result.affected || 0;
    
    if (count > 0) {
      this.logger.log(`Cleaned up ${count} expired refresh tokens`);
    } else {
      this.logger.debug('No expired refresh tokens found');
    }
  }
}
