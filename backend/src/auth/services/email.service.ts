import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailOptions {
  to: string;
  subject: string;
  html?: string;
  text?: string;
  template?: string;
  templateData?: Record<string, any>;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Send email using configured provider
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // In development, just log the email
      if (this.configService.get('NODE_ENV') === 'development') {
        this.logger.log('📧 Email would be sent in production:');
        this.logger.log(`To: ${options.to}`);
        this.logger.log(`Subject: ${options.subject}`);
        this.logger.log(`Content: ${options.text || options.html}`);
        return true;
      }

      // TODO: Implement actual email sending with SendGrid, AWS SES, or similar
      // Example with SendGrid:
      // const sgMail = require('@sendgrid/mail');
      // sgMail.setApiKey(this.configService.get('SENDGRID_API_KEY'));
      // await sgMail.send({
      //   to: options.to,
      //   from: this.configService.get('FROM_EMAIL'),
      //   subject: options.subject,
      //   html: options.html,
      //   text: options.text,
      // });

      this.logger.warn('Email service not implemented for production use');
      return false;
    } catch (error) {
      this.logger.error('Failed to send email:', error);
      return false;
    }
  }

  /**
   * Send email verification email
   */
  async sendVerificationEmail(email: string, token: string): Promise<boolean> {
    const verificationUrl = `${this.configService.get('FRONTEND_URL')}/verify-email?token=${token}`;
    
    const template = this.getVerificationEmailTemplate(verificationUrl);
    
    return this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
    const resetUrl = `${this.configService.get('FRONTEND_URL')}/reset-password?token=${token}`;
    
    const template = this.getPasswordResetEmailTemplate(resetUrl);
    
    return this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  /**
   * Send welcome email after successful registration
   */
  async sendWelcomeEmail(email: string, firstName: string): Promise<boolean> {
    const template = this.getWelcomeEmailTemplate(firstName);
    
    return this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  /**
   * Send API key creation notification
   */
  async sendApiKeyCreatedEmail(email: string, keyName: string, prefix: string): Promise<boolean> {
    const template = this.getApiKeyCreatedEmailTemplate(keyName, prefix);
    
    return this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  /**
   * Send security alert email
   */
  async sendSecurityAlertEmail(
    email: string, 
    alertType: 'login_from_new_ip' | 'password_changed' | 'api_key_used',
    details: Record<string, any>
  ): Promise<boolean> {
    const template = this.getSecurityAlertEmailTemplate(alertType, details);
    
    return this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  private getVerificationEmailTemplate(verificationUrl: string): EmailTemplate {
    return {
      subject: 'Verify your email address - EZNFE',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Welcome to EZNFE!</h2>
          <p>Thank you for registering with EZNFE. Please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p>If you can't click the button, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #6b7280;">${verificationUrl}</p>
          <p style="margin-top: 30px; font-size: 12px; color: #6b7280;">
            This verification link will expire in 24 hours for security reasons.
          </p>
        </div>
      `,
      text: `Welcome to EZNFE! Please verify your email address by visiting: ${verificationUrl}`,
    };
  }

  private getPasswordResetEmailTemplate(resetUrl: string): EmailTemplate {
    return {
      subject: 'Reset your password - EZNFE',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc2626;">Password Reset Request</h2>
          <p>You requested to reset your password. Click the button below to set a new password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>If you can't click the button, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #6b7280;">${resetUrl}</p>
          <p style="margin-top: 30px; font-size: 12px; color: #6b7280;">
            This password reset link will expire in 1 hour for security reasons.<br>
            If you didn't request this password reset, please ignore this email.
          </p>
        </div>
      `,
      text: `Password reset requested. Visit this link to reset your password: ${resetUrl}`,
    };
  }

  private getWelcomeEmailTemplate(firstName: string): EmailTemplate {
    return {
      subject: 'Welcome to EZNFE - Your Brazilian Invoice Management Platform',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #16a34a;">Welcome to EZNFE, ${firstName}!</h2>
          <p>Your account has been successfully verified and you're ready to start managing Brazilian electronic invoices.</p>
          <h3 style="color: #2563eb;">What you can do with EZNFE:</h3>
          <ul>
            <li>✅ Issue compliant NF-e invoices</li>
            <li>✅ Manage clients, products, and carriers</li>
            <li>✅ Automated tax calculations</li>
            <li>✅ Digital certificate management</li>
            <li>✅ SEFAZ integration</li>
            <li>✅ API access for integrations</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${this.configService.get('FRONTEND_URL')}/dashboard" 
               style="background-color: #16a34a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Go to Dashboard
            </a>
          </div>
          <p style="margin-top: 30px; font-size: 14px; color: #6b7280;">
            Need help getting started? Check out our documentation or contact support.
          </p>
        </div>
      `,
      text: `Welcome to EZNFE, ${firstName}! Your account is ready for managing Brazilian electronic invoices.`,
    };
  }

  private getApiKeyCreatedEmailTemplate(keyName: string, prefix: string): EmailTemplate {
    return {
      subject: 'New API Key Created - EZNFE',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">New API Key Created</h2>
          <p>A new API key has been created for your account:</p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <strong>Key Name:</strong> ${keyName}<br>
            <strong>Key Prefix:</strong> ${prefix}***
          </div>
          <p style="color: #dc2626; font-weight: bold;">
            ⚠️ Security Notice: Make sure to store your API key securely and never share it publicly.
          </p>
          <p style="margin-top: 30px; font-size: 12px; color: #6b7280;">
            If you didn't create this API key, please contact support immediately.
          </p>
        </div>
      `,
      text: `New API key "${keyName}" created for your account (${prefix}***).`,
    };
  }

  private getSecurityAlertEmailTemplate(
    alertType: string, 
    details: Record<string, any>
  ): EmailTemplate {
    const alertMessages = {
      login_from_new_ip: 'Login from new IP address',
      password_changed: 'Password changed',
      api_key_used: 'API key used',
    };

    const subject = `Security Alert: ${alertMessages[alertType]} - EZNFE`;

    return {
      subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc2626;">🔒 Security Alert</h2>
          <p>We detected ${alertMessages[alertType]} on your EZNFE account.</p>
          <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0;">
            <strong>Details:</strong><br>
            ${Object.entries(details).map(([key, value]) => `${key}: ${value}`).join('<br>')}
          </div>
          <p>
            If this was you, no action is needed. If you don't recognize this activity, 
            please secure your account immediately by changing your password and reviewing your API keys.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${this.configService.get('FRONTEND_URL')}/security" 
               style="background-color: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Review Security Settings
            </a>
          </div>
        </div>
      `,
      text: `Security Alert: ${alertMessages[alertType]} detected on your account.`,
    };
  }
}
