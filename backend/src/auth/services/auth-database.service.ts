import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RefreshToken } from '../entities/refresh-token.entity';
import { User } from '../../database/entities/user.entity';
import { UserAccount } from '../../users/entities/user-account.entity';
import { Account } from '../../database/entities/account.entity';
import {
  TenantQueryHelper,
  TenantAwareRepository,
} from '../../common/helpers/tenant-query.helper';
import { TenantContext } from '../../common/interfaces/tenant-context.interface';
import { TenantContextService } from '../../common/services/tenant-context.service';

/**
 * Service that handles all tenant-aware database operations for the auth module
 */
@Injectable()
export class AuthDatabaseService {
  private readonly logger = new Logger(AuthDatabaseService.name);

  constructor(
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserAccount)
    private readonly userAccountRepository: Repository<UserAccount>,
    @InjectRepository(Account)
    private readonly accountRepository: Repository<Account>,
    private readonly tenantContextService: TenantContextService,
  ) {}

  /**
   * Get a tenant-aware repository for refresh tokens
   */
  getRefreshTokenRepo(
    tenantContext: TenantContext,
  ): TenantAwareRepository<RefreshToken> {
    return TenantQueryHelper.createTenantRepository(
      this.refreshTokenRepository,
      tenantContext,
    );
  }

  /**
   * Get a tenant-aware repository for users
   */
  getUserRepo(tenantContext: TenantContext): TenantAwareRepository<User> {
    return TenantQueryHelper.createTenantRepository(
      this.userRepository,
      tenantContext,
    );
  }

  /**
   * Get a tenant-aware repository for user accounts
   */
  getUserAccountRepo(
    tenantContext: TenantContext,
  ): TenantAwareRepository<UserAccount> {
    return TenantQueryHelper.createTenantRepository(
      this.userAccountRepository,
      tenantContext,
    );
  }

  /**
   * Get the account repository (accounts are not tenant-aware entities)
   */
  getAccountRepo(): Repository<Account> {
    return this.accountRepository;
  }

  /**
   * Find refresh token by token value
   */
  async findRefreshToken(
    token: string,
    tenantContext?: TenantContext,
  ): Promise<RefreshToken | null> {
    const context =
      tenantContext || this.tenantContextService.getCurrentContext();

    if (!context) {
      // Fall back to non-tenant-aware query for initial login
      return this.refreshTokenRepository.findOne({
        where: { token },
      });
    }

    const tenantRepo = this.getRefreshTokenRepo(context);
    return tenantRepo.findOne({
      where: { token },
    });
  }

  /**
   * Save refresh token
   */
  async saveRefreshToken(
    refreshToken: RefreshToken,
    tenantContext?: TenantContext,
  ): Promise<RefreshToken> {
    const context =
      tenantContext || this.tenantContextService.getCurrentContext();

    if (!context) {
      // Fall back to non-tenant-aware save for initial login
      return this.refreshTokenRepository.save(refreshToken);
    }

    const tenantRepo = this.getRefreshTokenRepo(context);
    return tenantRepo.save(refreshToken);
  }

  /**
   * Delete refresh token
   */
  async deleteRefreshToken(
    refreshToken: RefreshToken,
    tenantContext?: TenantContext,
  ): Promise<void> {
    const context =
      tenantContext || this.tenantContextService.getCurrentContext();

    if (!context) {
      // Fall back to non-tenant-aware delete
      await this.refreshTokenRepository.delete(refreshToken.id);
      return;
    }

    const tenantRepo = this.getRefreshTokenRepo(context);
    await tenantRepo.delete(refreshToken.id);
  }

  /**
   * Find user by email (special case for authentication)
   * Don't apply tenant filtering for initial authentication
   */
  async findUserByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
      relations: ['userAccounts', 'userAccounts.account'],
    });
  }

  /**
   * Find user by ID with tenant context
   */
  async findUserById(
    userId: string,
    tenantContext?: TenantContext,
  ): Promise<User | null> {
    const context =
      tenantContext || this.tenantContextService.getCurrentContext();

    if (!context) {
      // Fall back to non-tenant-aware query if no context (e.g., during login)
      return this.userRepository.findOne({
        where: { id: userId },
        relations: ['userAccounts', 'userAccounts.account'],
      });
    }

    const tenantRepo = this.getUserRepo(context);
    return tenantRepo.findOne({
      where: { id: userId },
      relations: ['userAccounts', 'userAccounts.account'],
    });
  }

  /**
   * Find user account by user ID and account ID
   */
  async findUserAccount(
    userId: string,
    accountId: string,
    tenantContext?: TenantContext,
  ): Promise<UserAccount | null> {
    const context =
      tenantContext || this.tenantContextService.getCurrentContext();

    if (!context) {
      return this.userAccountRepository.findOne({
        where: { userId, accountId },
        relations: ['account'],
      });
    }

    const tenantRepo = this.getUserAccountRepo(context);
    return tenantRepo.findOne({
      where: { userId, accountId },
      relations: ['account'],
    });
  }

  /**
   * Find account by ID (accounts are not tenant-aware, so no tenant filtering needed)
   */
  async findAccountById(accountId: string): Promise<Account | null> {
    return this.accountRepository.findOne({
      where: { id: accountId },
    });
  }
}
