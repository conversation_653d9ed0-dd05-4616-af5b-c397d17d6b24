# EZNFE Authentication System

## Overview

The EZNFE authentication system provides comprehensive user authentication, authorization, and security features for the Brazilian electronic invoice (NF-e) management platform. It implements multi-tenant architecture with business unit context switching, role-based access control, and both JWT and API key authentication methods.

## 🏗️ Architecture

### Multi-Tenant Design
- **Account Level**: Top-level tenant isolation
- **Business Unit Level**: Secondary context for invoice operations
- **User Context**: Users can switch between business units within their account
- **Data Isolation**: All queries are scoped to the user's account/business unit

### Authentication Methods
1. **JWT Tokens**: For web application users
2. **API Keys**: For programmatic access and integrations
3. **Flexible Authentication**: Endpoints can accept both methods

## 📦 Key Components

### Services
- **`JwtAuthService`**: Core JWT token management and validation
- **`EnhancedAuthService`**: High-level authentication business logic
- **`EmailService`**: User verification and notification emails

### Strategies
- **`JwtStrategy`**: Passport strategy for JWT token validation
- **`LocalStrategy`**: Email/password authentication
- **`ApiKeyStrategy`**: API key validation and user context resolution

### Guards
- **`JwtAuthGuard`**: JWT-only authentication
- **`ApiKeyGuard`**: API key-only authentication
- **`FlexibleAuthGuard`**: Accepts both JWT and API keys
- **`TenantGuard`**: Multi-tenant data isolation enforcement
- **`RolesGuard`**: Role-based access control

### Decorators
- **`@CurrentUser()`**: Inject authenticated user into controllers
- **`@Roles(...roles)`**: Specify required roles for endpoints
- **`@RequireTenant()`**: Enforce account context
- **`@RequireBusinessUnit()`**: Enforce business unit context

## 🔐 Security Features

### Password Security
- **bcrypt hashing** with configurable salt rounds (default: 12)
- **Password complexity requirements**: uppercase, lowercase, number, special character
- **Account lockout**: 5 failed attempts lock account for 15 minutes
- **Password reset**: Secure token-based password reset with 1-hour expiration

### Rate Limiting
- **General rate limiting**: Configurable per-IP request limits
- **Authentication rate limiting**: Special limits for login/registration endpoints
- **API key rate limiting**: Per-key hourly/daily/monthly limits

### Session Management
- **JWT access tokens**: Short-lived (15 minutes default)
- **Refresh tokens**: Long-lived (7 days default) with rotation
- **Token blacklisting**: Revoke all sessions on password change
- **Automatic cleanup**: Expired refresh tokens are cleaned up

### API Key Security
- **Cryptographically secure generation**: 64-character random keys
- **SHA-256 hashing**: Keys are hashed for storage
- **Scoped permissions**: Read, Write, Admin, Webhook scopes
- **Rate limiting**: Per-key usage limits with tracking
- **IP restrictions**: Optional IP whitelist per key
- **Expiration**: Configurable expiration dates
- **Usage tracking**: Detailed usage statistics and alerting

## 🎭 Role-Based Access Control (RBAC)

### Role Hierarchy
1. **Owner**: Full account access, can manage users and billing
2. **Admin**: Manage business units, certificates, and API keys
3. **Member**: Create and manage invoices, clients, products
4. **Viewer**: Read-only access to invoices and data

### Permission Model
- **Hierarchical**: Higher roles inherit lower role permissions
- **Business unit scoped**: Permissions apply within current business unit
- **API key scoped**: API keys can have restricted scopes

## 🌐 Multi-Tenant Context

### Account Isolation
```typescript
// All queries automatically scoped to user's account
@UseGuards(FlexibleAuthGuard, TenantGuard)
@RequireTenant()
async getInvoices(@CurrentUser() user) {
  // user.accountId is automatically used for data filtering
}
```

### Business Unit Switching
```typescript
// Users can switch business unit context
@Post('switch-business-unit')
async switchBusinessUnit(@CurrentUser() user, @Body() switchDto) {
  // Validates business unit belongs to user's account
  // Issues new token with updated business unit context
}
```

## 📧 Email Integration

### Supported Email Types
- **Email verification**: Account activation emails
- **Password reset**: Secure password reset links
- **Welcome emails**: Post-registration welcome messages
- **Security alerts**: Login notifications and security events
- **API key notifications**: API key creation/revocation alerts

### Email Templates
- **HTML templates**: Responsive email designs
- **Text fallbacks**: Plain text versions for all emails
- **Configurable URLs**: Environment-specific links (dev/staging/prod)

## 🔄 Registration Flow

1. **User submits registration** with account and optional business unit
2. **Validation**: Email uniqueness, CNPJ validation, password strength
3. **Account creation**: Create account, subscription, user in transaction
4. **Business unit creation**: Optional business unit with Brazilian address validation
5. **Email verification**: Send verification email with token
6. **Immediate login**: Return tokens for immediate authentication

## 🔑 API Key Management

### Key Generation
```typescript
const apiKey = await authService.createApiKey(userId, {
  name: 'Integration Key',
  scopes: [ApiKeyScope.READ, ApiKeyScope.WRITE],
  expiresInDays: 90,
  rateLimits: {
    hourly: 1000,
    daily: 10000,
    monthly: 100000
  }
});
```

### Key Usage Tracking
- **Request counting**: Hourly, daily, monthly usage tracking
- **Last used information**: IP, user agent, endpoint tracking
- **Usage alerts**: Configurable threshold-based notifications
- **Automatic reset**: Usage counters reset automatically

## 📊 Monitoring & Analytics

### Security Events
- **Failed login attempts**: Track and alert on suspicious activity
- **New IP logins**: Notify users of logins from new locations
- **API key usage**: Monitor API key usage patterns
- **Password changes**: Track password security events

### Rate Limiting Metrics
- **Request patterns**: Monitor request rates per IP/user
- **API key usage**: Track API key utilization
- **Security violations**: Log rate limit and security violations

## 🧪 Testing Strategy

### Unit Tests
- **Service logic**: Authentication business logic
- **Validators**: Brazilian document validators
- **Security functions**: Password hashing, token generation

### Integration Tests
- **API endpoints**: Full authentication flows
- **Guard behavior**: Multi-tenant isolation
- **Database operations**: User/account/business unit creation

### End-to-End Tests
- **Complete user flows**: Registration → verification → login
- **Business unit switching**: Context switching workflows
- **API key workflows**: Creation, usage, revocation

## 🚀 Production Deployment

### Environment Variables
```bash
# JWT Configuration
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN_MS=*********

# Security
BCRYPT_SALT_ROUNDS=12

# Email Configuration
SENDGRID_API_KEY=your-sendgrid-key
FROM_EMAIL=<EMAIL>
FRONTEND_URL=https://app.eznfe.com

# Database
DATABASE_URL=********************************/eznfe

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
```

### Security Checklist
- [ ] Strong JWT secret (256+ bits)
- [ ] HTTPS enforcement in production
- [ ] Rate limiting configured
- [ ] Email service configured
- [ ] Database connection secured
- [ ] Security headers enabled
- [ ] CORS configured appropriately
- [ ] API key rate limits set
- [ ] Monitoring and alerting enabled

## 📖 API Documentation

The authentication system automatically generates OpenAPI/Swagger documentation for all endpoints. Access the interactive API documentation at `/api/docs` in your deployment.

### Key Endpoints
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `POST /auth/refresh` - Token refresh
- `POST /auth/logout` - User logout
- `GET /auth/profile` - Get user profile
- `POST /auth/switch-business-unit` - Switch context
- `POST /auth/api-keys` - Create API key
- `GET /auth/api-keys` - List API keys
- `DELETE /auth/api-keys/:id` - Revoke API key

## 🛠️ Customization

### Adding New Authentication Methods
1. Create new Passport strategy in `strategies/`
2. Implement validation logic
3. Add to `FlexibleAuthGuard` for combined authentication
4. Update user context resolution

### Extending Role System
1. Add new roles to `UserRole` enum
2. Update role hierarchy in `RolesGuard`
3. Add role-specific business logic
4. Update API documentation

### Custom Security Policies
1. Extend `SecurityMiddleware` with new policies
2. Add custom validation to registration flow
3. Implement additional rate limiting strategies
4. Add custom audit logging

This authentication system provides a solid foundation for secure, scalable, multi-tenant Brazilian invoice management while maintaining flexibility for future enhancements.
