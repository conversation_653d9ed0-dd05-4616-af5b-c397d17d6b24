import {
  IsEmail,
  IsNotEmpty,
  IsS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>th,
  IsOptional,
  ValidateNested,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAccountDto {
  @ApiProperty({ example: 'My Company Inc.', description: 'Account name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ example: 'Premium account for enterprise features', description: 'Account description' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}

export class CreateBusinessUnitDto {
  @ApiProperty({ example: 'Main Office', description: 'Business unit name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({ example: 'ACME Corporation Ltd.', description: 'Legal name (Razão Social)' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  legalName: string;

  @ApiPropertyOptional({ example: 'ACME Store', description: 'Trade name (Nome Fantasia)' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  tradeName?: string;

  @ApiProperty({ 
    example: '**************',
    description: 'CNPJ number (14 digits, numbers only)' 
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{14}$/, { message: 'CNPJ must be exactly 14 digits' })
  cnpj: string;

  @ApiPropertyOptional({ example: '*********', description: 'State registration (IE)' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  stateRegistration?: string;

  @ApiPropertyOptional({ example: '987654321', description: 'Municipal registration (IM)' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  municipalRegistration?: string;

  @ApiProperty({ example: '01234567', description: 'ZIP code (CEP)' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{8}$/, { message: 'CEP must be exactly 8 digits' })
  zipCode: string;

  @ApiProperty({ example: 'Rua das Flores', description: 'Street name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  street: string;

  @ApiPropertyOptional({ example: '123', description: 'Street number' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  number?: string;

  @ApiPropertyOptional({ example: 'Sala 456', description: 'Address complement' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  complement?: string;

  @ApiPropertyOptional({ example: 'Centro', description: 'District (Bairro)' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  district?: string;

  @ApiProperty({ example: 'São Paulo', description: 'City name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  city: string;

  @ApiProperty({ 
    example: 'SP',
    description: 'State code (2 letters)' 
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[A-Z]{2}$/, { message: 'State must be exactly 2 uppercase letters' })
  state: string;

  @ApiPropertyOptional({ example: '<EMAIL>', description: 'Business unit email' })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional({ example: '***********', description: 'Phone number' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiPropertyOptional({ example: 'https://company.com', description: 'Website URL' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  website?: string;
}

export class RegisterDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email address' })
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(255)
  email: string;

  @ApiProperty({ 
    example: 'SecurePassword123!',
    description: 'Password (min 8 characters, must contain uppercase, lowercase, number, and special character)' 
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @MaxLength(128)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    {
      message: 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
    }
  )
  password: string;

  @ApiProperty({ example: 'John', description: 'First name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  firstName: string;

  @ApiProperty({ example: 'Doe', description: 'Last name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  lastName: string;

  @ApiPropertyOptional({ example: '***********', description: 'Phone number' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiProperty({ type: CreateAccountDto, description: 'Account information' })
  @ValidateNested()
  @Type(() => CreateAccountDto)
  account: CreateAccountDto;

  @ApiPropertyOptional({ 
    type: CreateBusinessUnitDto, 
    description: 'Optional initial business unit to create' 
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateBusinessUnitDto)
  businessUnit?: CreateBusinessUnitDto;
}
