import {
  IsEmail,
  IsNotEmpty,
  IsString,
  <PERSON><PERSON><PERSON>th,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateEmitterDto } from '../../emitters/dto/create-emitter.dto';

export class SignUpDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;

  @IsString()
  @IsNotEmpty()
  accountName: string;

  /** Optional initial emitter to register alongside the account */
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateEmitterDto)
  emitter?: CreateEmitterDto;
}
