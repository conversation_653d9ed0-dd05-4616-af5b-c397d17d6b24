import { 
  Body, 
  Controller, 
  Post, 
  Get, 
  Patch,
  Delete,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { EnhancedAuthService } from './enhanced-auth.service';
import { CurrentUser } from './decorators/current-user.decorator';
import { Roles } from './decorators/roles.decorator';
import { RequireTenant, RequireBusinessUnit } from './guards/tenant.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { FlexibleAuthGuard } from './guards/flexible-auth.guard';
import { TenantGuard } from './guards/tenant.guard';
import { RolesGuard } from './guards/roles.guard';

import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { SwitchBusinessUnitDto } from './dto/switch-business-unit.dto';
import { UserRole } from '../database/entities/enums';
import { ApiKeyScope } from '../database/entities/api-key.entity';

@ApiTags('authentication')
@Controller('auth')
export class EnhancedAuthController {
  constructor(private readonly authService: EnhancedAuthService) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Register new user with account' })
  @ApiResponse({ 
    status: 201, 
    description: 'User successfully registered and logged in' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data or validation failed' 
  })
  @ApiResponse({ 
    status: 409, 
    description: 'User with email already exists or CNPJ already registered' 
  })
  @ApiBody({ type: RegisterDto })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login user with email and password' })
  @ApiResponse({ 
    status: 200, 
    description: 'User successfully authenticated' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Invalid credentials or account locked' 
  })
  @ApiBody({ type: LoginDto })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token using refresh token' })
  @ApiResponse({ 
    status: 200, 
    description: 'New access token generated successfully' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Invalid or expired refresh token' 
  })
  @ApiBody({ type: RefreshTokenDto })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refreshToken);
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Logout user by revoking refresh token' })
  @ApiResponse({ 
    status: 200, 
    description: 'User successfully logged out' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Refresh token not found' 
  })
  @ApiBody({ type: RefreshTokenDto })
  async logout(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.logout(refreshTokenDto.refreshToken);
  }

  @Get('profile')
  @UseGuards(FlexibleAuthGuard, TenantGuard)
  @ApiBearerAuth()
  @RequireTenant()
  @ApiOperation({ summary: 'Get current user profile and available business units' })
  @ApiResponse({ 
    status: 200, 
    description: 'User profile retrieved successfully' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Authentication required' 
  })
  async getProfile(@CurrentUser() user: any) {
    return this.authService.getProfile(user.id);
  }

  @Post('switch-business-unit')
  @UseGuards(JwtAuthGuard, TenantGuard)
  @ApiBearerAuth()
  @RequireTenant()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Switch business unit context' })
  @ApiResponse({ 
    status: 200, 
    description: 'Business unit context switched successfully' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Business unit not found or access denied' 
  })
  @ApiBody({ type: SwitchBusinessUnitDto })
  async switchBusinessUnit(
    @CurrentUser() user: any,
    @Body() switchDto: SwitchBusinessUnitDto,
  ) {
    return this.authService.switchBusinessUnit(user.id, switchDto);
  }

  @Post('api-keys')
  @UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
  @ApiBearerAuth()
  @RequireTenant()
  @Roles(UserRole.OWNER, UserRole.ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create new API key' })
  @ApiResponse({ 
    status: 201, 
    description: 'API key created successfully' 
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions' 
  })
  async createApiKey(
    @CurrentUser() user: any,
    @Body() createData: {
      name: string;
      description?: string;
      scopes: ApiKeyScope[];
      expiresInDays?: number;
      rateLimits?: {
        hourly?: number;
        daily?: number;
        monthly?: number;
      };
    },
  ) {
    return this.authService.createApiKey(user.id, createData);
  }

  @Get('api-keys')
  @UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
  @ApiBearerAuth()
  @RequireTenant()
  @Roles(UserRole.OWNER, UserRole.ADMIN, UserRole.MEMBER)
  @ApiOperation({ summary: 'Get user API keys' })
  @ApiResponse({ 
    status: 200, 
    description: 'API keys retrieved successfully' 
  })
  async getApiKeys(@CurrentUser() user: any) {
    return this.authService.getApiKeys(user.id);
  }

  @Delete('api-keys/:id')
  @UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
  @ApiBearerAuth()
  @RequireTenant()
  @Roles(UserRole.OWNER, UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Revoke API key' })
  @ApiResponse({ 
    status: 200, 
    description: 'API key revoked successfully' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'API key not found' 
  })
  @ApiParam({ name: 'id', description: 'API key ID' })
  async revokeApiKey(
    @CurrentUser() user: any,
    @Param('id') apiKeyId: string,
  ) {
    return this.authService.revokeApiKey(user.id, apiKeyId);
  }

  @Get('verify-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify user email with token' })
  @ApiResponse({ 
    status: 200, 
    description: 'Email verified successfully' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid verification token' 
  })
  @ApiQuery({ name: 'token', description: 'Email verification token' })
  async verifyEmail(@Query('token') token: string) {
    return this.authService.verifyEmail(token);
  }

  @Post('request-password-reset')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset email' })
  @ApiResponse({ 
    status: 200, 
    description: 'Password reset email sent (if user exists)' 
  })
  async requestPasswordReset(@Body('email') email: string) {
    return this.authService.requestPasswordReset(email);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ 
    status: 200, 
    description: 'Password reset successfully' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid or expired reset token' 
  })
  async resetPassword(
    @Body() resetData: { token: string; newPassword: string },
  ) {
    return this.authService.resetPassword(resetData.token, resetData.newPassword);
  }
}
