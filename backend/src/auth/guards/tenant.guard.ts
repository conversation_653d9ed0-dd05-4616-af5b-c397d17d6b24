import { Injectable, CanActivate, ExecutionContext, ForbiddenException, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

export const TENANT_REQUIRED_KEY = 'tenant-required';
export const BUSINESS_UNIT_REQUIRED_KEY = 'business-unit-required';

/**
 * Decorator to mark endpoints that require tenant context
 */
export const RequireTenant = () => SetMetadata(TENANT_REQUIRED_KEY, true);

/**
 * Decorator to mark endpoints that require business unit context
 */
export const RequireBusinessUnit = () => SetMetadata(BUSINESS_UNIT_REQUIRED_KEY, true);

@Injectable()
export class TenantGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // If no user is authenticated, deny access
    if (!user) {
      throw new ForbiddenException('Authentication required');
    }

    // Check if tenant (account) context is required
    const requiresTenant = this.reflector.getAllAndOverride<boolean>(TENANT_REQUIRED_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (requiresTenant && !user.accountId) {
      throw new ForbiddenException('Tenant context required');
    }

    // Check if business unit context is required
    const requiresBusinessUnit = this.reflector.getAllAndOverride<boolean>(BUSINESS_UNIT_REQUIRED_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (requiresBusinessUnit && !user.businessUnitId) {
      throw new ForbiddenException('Business unit context required');
    }

    // Extract resource IDs from request parameters
    const { accountId, businessUnitId } = request.params;

    // Validate account access if accountId is in the URL
    if (accountId && accountId !== user.accountId) {
      throw new ForbiddenException('Access denied: Account mismatch');
    }

    // Validate business unit access if businessUnitId is in the URL
    if (businessUnitId) {
      // User must have a business unit context
      if (!user.businessUnitId) {
        throw new ForbiddenException('Business unit context required for this operation');
      }

      // Business unit in URL must match user's current business unit
      if (businessUnitId !== user.businessUnitId) {
        throw new ForbiddenException('Access denied: Business unit mismatch');
      }
    }

    // For API key authentication, check scopes and permissions
    if (user.authMethod === 'api-key') {
      return this.validateApiKeyAccess(user, context);
    }

    return true;
  }

  /**
   * Validate API key specific access rules
   */
  private validateApiKeyAccess(user: any, context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const method = request.method;
    const apiKey = user.apiKey;

    if (!apiKey) {
      throw new ForbiddenException('Invalid API key context');
    }

    // Check if API key has required scopes for the HTTP method
    const requiredScope = this.getRequiredScopeForMethod(method);
    
    if (!apiKey.scopes.includes(requiredScope) && !apiKey.scopes.includes('admin')) {
      throw new ForbiddenException(`API key does not have required scope: ${requiredScope}`);
    }

    // Check specific permissions if defined
    if (apiKey.permissions) {
      try {
        const permissions = JSON.parse(apiKey.permissions);
        const endpoint = `${method} ${request.route?.path || request.url}`;
        
        // If permissions are defined, check if this endpoint is allowed
        if (permissions.length > 0 && !this.isEndpointAllowed(endpoint, permissions)) {
          throw new ForbiddenException('API key does not have permission for this endpoint');
        }
      } catch {
        // If permissions parsing fails, continue (treat as no specific permissions)
      }
    }

    // Validate HTTPS requirement
    if (apiKey.requiresHttps && request.protocol !== 'https') {
      throw new ForbiddenException('API key requires HTTPS');
    }

    // Validate origin if restricted
    const origin = request.headers.origin;
    if (origin && !apiKey.isOriginAllowed(origin)) {
      throw new ForbiddenException('Origin not allowed for this API key');
    }

    return true;
  }

  /**
   * Get required API key scope for HTTP method
   */
  private getRequiredScopeForMethod(method: string): string {
    switch (method.toUpperCase()) {
      case 'GET':
      case 'HEAD':
      case 'OPTIONS':
        return 'read';
      case 'POST':
      case 'PUT':
      case 'PATCH':
      case 'DELETE':
        return 'write';
      default:
        return 'read';
    }
  }

  /**
   * Check if endpoint is allowed based on permissions array
   */
  private isEndpointAllowed(endpoint: string, permissions: string[]): boolean {
    return permissions.some(permission => {
      // Support wildcard matching
      const pattern = permission.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`, 'i');
      return regex.test(endpoint);
    });
  }
}
