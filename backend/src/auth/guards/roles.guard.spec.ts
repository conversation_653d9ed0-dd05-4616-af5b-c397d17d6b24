import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { ForbiddenException } from '@nestjs/common';
import { RolesGuard } from './roles.guard';
import { UserRole } from '../../users/entities/user-account.entity';
import { ROLES_KEY } from '../decorators/roles.decorator';

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<RolesGuard>(RolesGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    const mockExecutionContext = {
      getHandler: jest.fn(),
      getClass: jest.fn(),
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn(),
      }),
    };

    it('should allow access if no roles are required', () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(undefined);

      expect(guard.canActivate(mockExecutionContext as any)).toBe(true);
    });

    it('should allow access if user has required role (owner)', () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: { role: UserRole.OWNER },
      });

      expect(guard.canActivate(mockExecutionContext as any)).toBe(true);
    });

    it('should allow access if user has required role (admin)', () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: { role: UserRole.ADMIN },
      });

      expect(guard.canActivate(mockExecutionContext as any)).toBe(true);
    });

    it('should deny access if user does not have required role', () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: { role: UserRole.MEMBER },
      });

      expect(() => guard.canActivate(mockExecutionContext as any)).toThrow(
        ForbiddenException,
      );
    });

    it('should deny access if user has no role', () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
        user: {},
      });

      expect(() => guard.canActivate(mockExecutionContext as any)).toThrow(
        ForbiddenException,
      );
    });

    it('should deny access if no user is present', () => {
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([
        UserRole.OWNER,
        UserRole.ADMIN,
      ]);

      mockExecutionContext.switchToHttp().getRequest.mockReturnValue({});

      expect(() => guard.canActivate(mockExecutionContext as any)).toThrow(
        ForbiddenException,
      );
    });

    it('should check for roles using the correct key', () => {
      guard.canActivate(mockExecutionContext as any);

      expect(reflector.getAllAndOverride).toHaveBeenCalledWith(ROLES_KEY, [
        mockExecutionContext.getHandler(),
        mockExecutionContext.getClass(),
      ]);
    });
  });
});
