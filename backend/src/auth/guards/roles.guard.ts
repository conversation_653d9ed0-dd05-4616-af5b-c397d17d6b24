import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../../database/entities/enums';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    
    // If no user or no role, deny access
    if (!user || !user.role) {
      throw new ForbiddenException('Access denied: User role not found');
    }
    
    // Check if the user's role is in the required roles
    const hasRequiredRole = requiredRoles.includes(user.role as UserRole);
    
    if (!hasRequiredRole) {
      throw new ForbiddenException(`Access denied: Role '${user.role}' is not authorized for this action`);
    }
    
    return true;
  }
}
