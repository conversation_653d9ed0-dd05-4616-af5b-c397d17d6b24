import { Injectable, ExecutionContext } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class FlexibleAuthGuard extends AuthGuard(['jwt', 'api-key']) {
  canActivate(context: ExecutionContext) {
    // This guard will try both JWT and API key strategies
    // The first one that succeeds will be used
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    // If there's an error or no user, throw an unauthorized exception
    if (err || !user) {
      throw err || new Error('Authentication required');
    }
    
    return user;
  }
}
