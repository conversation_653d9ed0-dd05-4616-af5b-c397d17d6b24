import { 
  Injectable, 
  BadRequestException, 
  UnauthorizedException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, QueryRunner, DataSource } from 'typeorm';
import * as crypto from 'crypto';

import { JwtAuthService, TokenPair, AuthenticatedUser } from './jwt-auth.service';
import { User, UserStatus, UserRole } from '../database/entities/user.entity';
import { Account, AccountStatus } from '../database/entities/account.entity';
import { BusinessUnit, BusinessUnitStatus, TaxRegime } from '../database/entities/business-unit.entity';
import { Subscription, SubscriptionStatus, SubscriptionTier } from '../database/entities/subscription.entity';
import { ApiKey, ApiKeyStatus, ApiKeyScope } from '../database/entities/api-key.entity';

import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { SwitchBusinessUnitDto } from './dto/switch-business-unit.dto';
import { validateCnpj, validateCpf, validateCep } from '../common/validators/brazilian.validators';

export interface LoginResponse {
  user: AuthenticatedUser;
  tokens: TokenPair;
  availableBusinessUnits: Array<{
    id: string;
    name: string;
    cnpj: string;
    isActive: boolean;
  }>;
}

export interface ApiKeyCreationResult {
  id: string;
  name: string;
  key: string; // Only returned during creation
  prefix: string;
  scopes: ApiKeyScope[];
  expiresAt: Date | null;
}

@Injectable()
export class EnhancedAuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Account)
    private readonly accountRepository: Repository<Account>,
    @InjectRepository(BusinessUnit)
    private readonly businessUnitRepository: Repository<BusinessUnit>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    private readonly jwtAuthService: JwtAuthService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Register new user with account and optional business unit
   */
  async register(registerDto: RegisterDto): Promise<LoginResponse> {
    // Validate Brazilian documents if provided
    if (registerDto.businessUnit?.cnpj && !validateCnpj(registerDto.businessUnit.cnpj)) {
      throw new BadRequestException('Invalid CNPJ format');
    }

    if (registerDto.businessUnit?.zipCode && !validateCep(registerDto.businessUnit.zipCode)) {
      throw new BadRequestException('Invalid CEP format');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Check if CNPJ is already registered (if provided)
    if (registerDto.businessUnit?.cnpj) {
      const existingBusinessUnit = await this.businessUnitRepository.findOne({
        where: { cnpj: registerDto.businessUnit.cnpj },
      });

      if (existingBusinessUnit) {
        throw new ConflictException('CNPJ is already registered');
      }
    }

    // Use transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create account
      const account = queryRunner.manager.create(Account, {
        name: registerDto.account.name,
        description: registerDto.account.description,
        status: AccountStatus.ACTIVE,
      });
      const savedAccount = await queryRunner.manager.save(account);

      // Create subscription (default free tier)
      const subscription = queryRunner.manager.create(Subscription, {
        accountId: savedAccount.id,
        tier: SubscriptionTier.FREE,
        status: SubscriptionStatus.ACTIVE,
        startDate: new Date(),
        monthlyInvoiceLimit: 100, // Free tier limit
        apiCallsLimit: 1000,
        storageLimit: **********, // 1GB in bytes
      });
      await queryRunner.manager.save(subscription);

      // Hash password
      const hashedPassword = await this.jwtAuthService.hashPassword(registerDto.password);

      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');

      // Create user
      const user = queryRunner.manager.create(User, {
        email: registerDto.email,
        password: hashedPassword,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
        phone: registerDto.phone,
        role: UserRole.OWNER, // First user is always owner
        status: UserStatus.PENDING_VERIFICATION,
        accountId: savedAccount.id,
        emailVerificationToken,
      });
      const savedUser = await queryRunner.manager.save(user);

      // Create business unit if provided
      let businessUnit: BusinessUnit | undefined;
      if (registerDto.businessUnit) {
        businessUnit = queryRunner.manager.create(BusinessUnit, {
          ...registerDto.businessUnit,
          accountId: savedAccount.id,
          taxRegime: TaxRegime.SIMPLES_NACIONAL, // Default
          status: BusinessUnitStatus.SETUP_PENDING,
          country: 'BR',
        });
        const savedBusinessUnit = await queryRunner.manager.save(businessUnit);
        
        // Update user's current business unit
        savedUser.currentBusinessUnitId = savedBusinessUnit.id;
        await queryRunner.manager.save(savedUser);
        
        businessUnit = savedBusinessUnit;
      }

      await queryRunner.commitTransaction();

      // TODO: Send email verification
      // await this.emailService.sendVerificationEmail(savedUser.email, emailVerificationToken);

      // Generate tokens for immediate login
      const tokens = await this.jwtAuthService.generateTokenPair(savedUser, businessUnit?.id);
      
      // Get user info
      const authenticatedUser: AuthenticatedUser = {
        id: savedUser.id,
        email: savedUser.email,
        firstName: savedUser.firstName,
        lastName: savedUser.lastName,
        role: savedUser.role,
        account: {
          id: savedAccount.id,
          name: savedAccount.name,
        },
        businessUnit: businessUnit ? {
          id: businessUnit.id,
          name: businessUnit.name,
          cnpj: businessUnit.cnpj,
        } : undefined,
      };

      const availableBusinessUnits = businessUnit ? [{
        id: businessUnit.id,
        name: businessUnit.name,
        cnpj: businessUnit.cnpj,
        isActive: businessUnit.isActive,
      }] : [];

      return {
        user: authenticatedUser,
        tokens,
        availableBusinessUnits,
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Login user with email and password
   */
  async login(loginDto: LoginDto): Promise<LoginResponse> {
    // Validate credentials
    const user = await this.jwtAuthService.validateCredentials(loginDto.email, loginDto.password);
    
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedException('Account is not active or email not verified');
    }

    // Get user with relations
    const fullUser = await this.userRepository.findOne({
      where: { id: user.id },
      relations: ['account', 'currentBusinessUnit'],
    });

    if (!fullUser) {
      throw new UnauthorizedException('User not found');
    }

    // Generate tokens
    const tokens = await this.jwtAuthService.generateTokenPair(fullUser);

    // Get available business units
    const availableBusinessUnits = await this.businessUnitRepository.find({
      where: { accountId: fullUser.accountId },
      select: ['id', 'name', 'cnpj', 'status'],
    });

    const authenticatedUser: AuthenticatedUser = {
      id: fullUser.id,
      email: fullUser.email,
      firstName: fullUser.firstName,
      lastName: fullUser.lastName,
      role: fullUser.role,
      account: {
        id: fullUser.account.id,
        name: fullUser.account.name,
      },
      businessUnit: fullUser.currentBusinessUnit ? {
        id: fullUser.currentBusinessUnit.id,
        name: fullUser.currentBusinessUnit.name,
        cnpj: fullUser.currentBusinessUnit.cnpj,
      } : undefined,
    };

    return {
      user: authenticatedUser,
      tokens,
      availableBusinessUnits: availableBusinessUnits.map(bu => ({
        id: bu.id,
        name: bu.name,
        cnpj: bu.cnpj,
        isActive: bu.status === BusinessUnitStatus.ACTIVE,
      })),
    };
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<TokenPair> {
    return this.jwtAuthService.refreshAccessToken(refreshToken);
  }

  /**
   * Logout user
   */
  async logout(refreshToken: string): Promise<{ success: boolean }> {
    const revoked = await this.jwtAuthService.revokeRefreshToken(refreshToken);
    return { success: revoked };
  }

  /**
   * Switch business unit context
   */
  async switchBusinessUnit(userId: string, switchDto: SwitchBusinessUnitDto): Promise<TokenPair> {
    return this.jwtAuthService.switchBusinessUnit(userId, switchDto.businessUnitId);
  }

  /**
   * Get user profile with available business units
   */
  async getProfile(userId: string): Promise<{
    user: AuthenticatedUser;
    availableBusinessUnits: Array<{
      id: string;
      name: string;
      cnpj: string;
      isActive: boolean;
    }>;
  }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['account', 'currentBusinessUnit'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get available business units
    const availableBusinessUnits = await this.businessUnitRepository.find({
      where: { accountId: user.accountId },
      select: ['id', 'name', 'cnpj', 'status'],
    });

    const authenticatedUser: AuthenticatedUser = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      account: {
        id: user.account.id,
        name: user.account.name,
      },
      businessUnit: user.currentBusinessUnit ? {
        id: user.currentBusinessUnit.id,
        name: user.currentBusinessUnit.name,
        cnpj: user.currentBusinessUnit.cnpj,
      } : undefined,
    };

    return {
      user: authenticatedUser,
      availableBusinessUnits: availableBusinessUnits.map(bu => ({
        id: bu.id,
        name: bu.name,
        cnpj: bu.cnpj,
        isActive: bu.status === BusinessUnitStatus.ACTIVE,
      })),
    };
  }

  /**
   * Create API key for user
   */
  async createApiKey(
    userId: string,
    data: {
      name: string;
      description?: string;
      scopes: ApiKeyScope[];
      expiresInDays?: number;
      rateLimits?: {
        hourly?: number;
        daily?: number;
        monthly?: number;
      };
    },
  ): Promise<ApiKeyCreationResult> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['account'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Generate API key
    const { key, hash, prefix } = this.jwtAuthService.generateApiKey();

    // Calculate expiration
    let expiresAt: Date | null = null;
    if (data.expiresInDays) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + data.expiresInDays);
    }

    // Create API key entity
    const apiKey = this.apiKeyRepository.create({
      name: data.name,
      description: data.description || undefined,
      keyHash: hash,
      keyPrefix: prefix,
      status: ApiKeyStatus.ACTIVE,
      scopes: data.scopes,
      accountId: user.accountId,
      createdByUserId: userId,
      expiresAt: expiresAt || undefined,
      neverExpires: !expiresAt,
      rateLimitPerHour: data.rateLimits?.hourly || 1000,
      rateLimitPerDay: data.rateLimits?.daily || 10000,
      rateLimitPerMonth: data.rateLimits?.monthly || 100000,
    });

    const savedApiKey = await this.apiKeyRepository.save(apiKey);

    return {
      id: savedApiKey.id,
      name: savedApiKey.name,
      key, // Only return the actual key during creation
      prefix: savedApiKey.keyPrefix,
      scopes: savedApiKey.scopes,
      expiresAt: savedApiKey.expiresAt ?? null,
    };
  }

  /**
   * Get user's API keys (without actual key values)
   */
  async getApiKeys(userId: string): Promise<Array<{
    id: string;
    name: string;
    prefix: string;
    scopes: ApiKeyScope[];
    status: ApiKeyStatus;
    lastUsedAt: Date | null;
    expiresAt: Date | null;
    createdAt: Date;
  }>> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const apiKeys = await this.apiKeyRepository.find({
      where: { 
        accountId: user.accountId,
        createdByUserId: userId,
      },
      order: { createdAt: 'DESC' },
    });

    return apiKeys.map(key => ({
      id: key.id,
      name: key.name,
      prefix: key.keyPrefix,
      scopes: key.scopes,
      status: key.status,
      lastUsedAt: key.lastUsedAt ?? null,
      expiresAt: key.expiresAt ?? null,
      createdAt: key.createdAt,
    }));
  }

  /**
   * Revoke API key
   */
  async revokeApiKey(userId: string, apiKeyId: string): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const apiKey = await this.apiKeyRepository.findOne({
      where: { 
        id: apiKeyId,
        accountId: user.accountId,
        createdByUserId: userId,
      },
    });

    if (!apiKey) {
      throw new NotFoundException('API key not found');
    }

    apiKey.revoke('Revoked by user');
    await this.apiKeyRepository.save(apiKey);

    return { success: true };
  }

  /**
   * Verify email with token
   */
  async verifyEmail(token: string): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      throw new BadRequestException('Invalid verification token');
    }

    user.emailVerifiedAt = new Date();
    user.emailVerificationToken = undefined;
    user.status = UserStatus.ACTIVE;
    
    await this.userRepository.save(user);

    return { success: true };
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      // Don't reveal if user exists
      return { success: true };
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour

    user.passwordResetToken = resetToken;
    user.passwordResetExpiresAt = expiresAt;
    
    await this.userRepository.save(user);

    // TODO: Send password reset email
    // await this.emailService.sendPasswordResetEmail(user.email, resetToken);

    return { success: true };
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { passwordResetToken: token },
    });

    if (!user || !user.passwordResetExpiresAt || user.passwordResetExpiresAt < new Date()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Hash new password
    const hashedPassword = await this.jwtAuthService.hashPassword(newPassword);

    user.password = hashedPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpiresAt = undefined;
    user.loginAttempts = 0;
    user.lockedAt = undefined;
    
    await this.userRepository.save(user);

    // Revoke all existing refresh tokens
    await this.jwtAuthService.revokeAllRefreshTokens(user.id);

    return { success: true };
  }
}
