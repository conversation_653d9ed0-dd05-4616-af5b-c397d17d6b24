import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { LogoutDto } from './dto/logout.dto';
import { SwitchEmitterDto } from './dto/switch-emitter.dto';

const mockAuthService = () => ({
  signUp: jest.fn(),
  signIn: jest.fn(),
  refresh: jest.fn(),
  logout: jest.fn(),
  switchEmitter: jest.fn(),
  getEmittersForAccount: jest.fn(),
});

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useFactory: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('logout', () => {
    it('should call authService.logout with the refresh token', async () => {
      const logoutDto: LogoutDto = {
        refreshToken: 'test-refresh-token',
      };
      const expectedResult = { success: true };

      jest.spyOn(authService, 'logout').mockResolvedValue(expectedResult);

      const result = await controller.logout(logoutDto);

      expect(authService.logout).toHaveBeenCalledWith(logoutDto.refreshToken);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('refresh', () => {
    it('should call authService.refresh with the refresh token', async () => {
      const refreshToken = 'test-refresh-token';
      const expectedResult = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
      };

      jest.spyOn(authService, 'refresh').mockResolvedValue(expectedResult);

      const result = await controller.refresh(refreshToken);

      expect(authService.refresh).toHaveBeenCalledWith(refreshToken);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('switchEmitter', () => {
    it('should call authService.switchEmitter with the correct parameters', async () => {
      const user = {
        id: 'user-id',
        accountId: 'account-id',
      };
      const switchEmitterDto: SwitchEmitterDto = {
        emitterId: 'emitter-id',
      };
      const refreshToken = 'refresh-token';
      const expectedResult = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        user: {
          id: user.id,
          emitterId: switchEmitterDto.emitterId,
        },
      };

      jest.spyOn(authService, 'switchEmitter').mockResolvedValue(expectedResult);

      const result = await controller.switchEmitter(user, switchEmitterDto, refreshToken);

      expect(authService.switchEmitter).toHaveBeenCalledWith(
        user.id,
        user.accountId,
        switchEmitterDto.emitterId,
        refreshToken,
      );
      expect(result).toEqual(expectedResult);
    });

    it('should call authService.switchEmitter without refresh token if not provided', async () => {
      const user = {
        id: 'user-id',
        accountId: 'account-id',
      };
      const switchEmitterDto: SwitchEmitterDto = {
        emitterId: 'emitter-id',
      };
      const expectedResult = {
        accessToken: 'new-access-token',
        user: {
          id: user.id,
          emitterId: switchEmitterDto.emitterId,
        },
      };

      jest.spyOn(authService, 'switchEmitter').mockResolvedValue(expectedResult);

      const result = await controller.switchEmitter(user, switchEmitterDto);

      expect(authService.switchEmitter).toHaveBeenCalledWith(
        user.id,
        user.accountId,
        switchEmitterDto.emitterId,
        undefined,
      );
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getProfile', () => {
    it('should return user profile with available emitters', async () => {
      const user = {
        id: 'user-id',
        accountId: 'account-id',
      };
      const emitters = [
        { id: 'emitter-1', name: 'Emitter 1' },
        { id: 'emitter-2', name: 'Emitter 2' },
      ];

      jest.spyOn(authService, 'getEmittersForAccount').mockResolvedValue(emitters);

      const result = await controller.getProfile(user);

      expect(authService.getEmittersForAccount).toHaveBeenCalledWith(user.accountId);
      expect(result).toEqual({
        ...user,
        availableEmitters: emitters,
      });
    });
  });
});
