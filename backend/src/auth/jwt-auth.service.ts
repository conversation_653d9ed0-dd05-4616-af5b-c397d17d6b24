import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';

import { User, UserStatus } from '../database/entities/user.entity';
import { Account } from '../database/entities/account.entity';
import { BusinessUnit } from '../database/entities/business-unit.entity';
import { RefreshToken } from './entities/refresh-token.entity';

export interface JwtPayload {
  sub: string; // User ID
  email: string;
  accountId: string;
  role: string;
  businessUnitId?: string;
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface AuthenticatedUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  account: {
    id: string;
    name: string;
  };
  businessUnit?: {
    id: string;
    name: string;
    cnpj: string;
  };
}

@Injectable()
export class JwtAuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Account)
    private readonly accountRepository: Repository<Account>,
    @InjectRepository(BusinessUnit)
    private readonly businessUnitRepository: Repository<BusinessUnit>,
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generate JWT access token and refresh token pair
   */
  async generateTokenPair(user: User, businessUnitId?: string): Promise<TokenPair> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      accountId: user.accountId,
      role: user.role,
      businessUnitId: businessUnitId || user.currentBusinessUnitId,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.generateRefreshToken(user.id, user.accountId, businessUnitId);
    
    const expiresIn = this.getAccessTokenExpiration();

    return {
      accessToken,
      refreshToken,
      expiresIn,
    };
  }

  /**
   * Generate a secure refresh token and store it in the database
   */
  async generateRefreshToken(userId: string, accountId: string, businessUnitId?: string): Promise<string> {
    // Generate cryptographically secure random token
    const token = crypto.randomBytes(64).toString('hex');
    
    // Calculate expiration (7 days default)
    const expiresInMs = this.configService.get('REFRESH_TOKEN_EXPIRES_IN_MS', 7 * 24 * 60 * 60 * 1000);
    const expiresAt = new Date(Date.now() + expiresInMs);

    // Store refresh token in database
    const refreshTokenEntity = this.refreshTokenRepository.create({
      userId,
      accountId,
      businessUnitId: businessUnitId || undefined,
      token,
      expiresAt,
    });

    await this.refreshTokenRepository.save(refreshTokenEntity);
    
    return token;
  }

  /**
   * Validate and extract payload from JWT token
   */
  async validateAccessToken(token: string): Promise<JwtPayload | null> {
    try {
      const payload = this.jwtService.verify(token) as JwtPayload;
      return payload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Refresh access token using valid refresh token
   */
  async refreshAccessToken(refreshToken: string): Promise<TokenPair> {
    // Find refresh token in database
    const storedToken = await this.refreshTokenRepository.findOne({
      where: { token: refreshToken },
      relations: ['user', 'user.account'],
    });

    if (!storedToken) {
      throw new Error('Invalid refresh token');
    }

    // Check if token has expired
    if (storedToken.expiresAt < new Date()) {
      await this.refreshTokenRepository.remove(storedToken);
      throw new Error('Refresh token has expired');
    }

    // Verify user is still active
    const user = await this.userRepository.findOne({
      where: { id: storedToken.userId },
      relations: ['account'],
    });

    if (!user || user.status !== UserStatus.ACTIVE) {
      await this.refreshTokenRepository.remove(storedToken);
      throw new Error('User is not active');
    }

    // Generate new token pair
    const newTokenPair = await this.generateTokenPair(user, storedToken.businessUnitId);
    
    // Remove old refresh token (token rotation)
    await this.refreshTokenRepository.remove(storedToken);

    return newTokenPair;
  }

  /**
   * Revoke refresh token (logout)
   */
  async revokeRefreshToken(refreshToken: string): Promise<boolean> {
    const storedToken = await this.refreshTokenRepository.findOne({
      where: { token: refreshToken },
    });

    if (storedToken) {
      await this.refreshTokenRepository.remove(storedToken);
      return true;
    }

    return false;
  }

  /**
   * Revoke all refresh tokens for a user (logout from all devices)
   */
  async revokeAllRefreshTokens(userId: string): Promise<void> {
    await this.refreshTokenRepository.delete({ userId });
  }

  /**
   * Get user information from JWT payload
   */
  async getUserFromPayload(payload: JwtPayload): Promise<AuthenticatedUser | null> {
    const user = await this.userRepository.findOne({
      where: { id: payload.sub },
      relations: ['account', 'currentBusinessUnit'],
    });

    if (!user || user.status !== UserStatus.ACTIVE) {
      return null;
    }

    // If payload has businessUnitId, fetch that specific business unit
    let businessUnit = user.currentBusinessUnit;
    if (payload.businessUnitId && payload.businessUnitId !== user.currentBusinessUnitId) {
      businessUnit = await this.businessUnitRepository.findOne({
        where: { 
          id: payload.businessUnitId,
          accountId: user.accountId, // Ensure user can only access business units from their account
        },
      }) ?? undefined;
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      account: {
        id: user.account.id,
        name: user.account.name,
      },
      businessUnit: businessUnit ? {
        id: businessUnit.id,
        name: businessUnit.name,
        cnpj: businessUnit.cnpj,
      } : undefined,
    };
  }

  /**
   * Switch business unit context for a user
   */
  async switchBusinessUnit(userId: string, businessUnitId: string): Promise<TokenPair> {
    // Verify user exists and is active
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['account'],
    });

    if (!user || user.status !== UserStatus.ACTIVE) {
      throw new Error('User not found or inactive');
    }

    // Verify business unit exists and belongs to user's account
    const businessUnit = await this.businessUnitRepository.findOne({
      where: { 
        id: businessUnitId,
        accountId: user.accountId,
      },
    });

    if (!businessUnit) {
      throw new Error('Business unit not found or access denied');
    }

    // Update user's current business unit
    user.currentBusinessUnitId = businessUnitId;
    await this.userRepository.save(user);

    // Generate new token pair with updated business unit context
    return this.generateTokenPair(user, businessUnitId);
  }

  /**
   * Validate user credentials
   */
  async validateCredentials(email: string, password: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['account'],
    });

    if (!user) {
      return null;
    }

    // Check if user account is locked due to too many failed attempts
    if (user.isLocked) {
      throw new Error('Account is temporarily locked due to too many failed login attempts');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      // Increment login attempts
      user.loginAttempts = (user.loginAttempts || 0) + 1;
      
      // Lock account after 5 failed attempts for 15 minutes
      if (user.loginAttempts >= 5) {
        user.lockedAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
      }
      
      await this.userRepository.save(user);
      return null;
    }

    // Reset login attempts on successful login
    user.loginAttempts = 0;
    user.lockedAt = undefined;
    user.lastLoginAt = new Date();
    await this.userRepository.save(user);

    return user;
  }

  /**
   * Hash password with bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = this.configService.get('BCRYPT_SALT_ROUNDS', 12);
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate secure API key
   */
  generateApiKey(): { key: string; hash: string; prefix: string } {
    // Generate the full API key
    const key = `ezf_${crypto.randomBytes(32).toString('hex')}`;
    
    // Create hash for storage
    const hash = crypto.createHash('sha256').update(key).digest('hex');
    
    // Extract prefix for display
    const prefix = key.substring(0, 12);

    return { key, hash, prefix };
  }

  /**
   * Verify API key against stored hash
   */
  verifyApiKey(apiKey: string, storedHash: string): boolean {
    const hash = crypto.createHash('sha256').update(apiKey).digest('hex');
    return hash === storedHash;
  }

  /**
   * Clean up expired refresh tokens
   */
  async cleanupExpiredTokens(): Promise<number> {
    const result = await this.refreshTokenRepository.delete({
      expiresAt: new Date(),
    });

    return result.affected || 0;
  }

  /**
   * Get access token expiration in seconds
   */
  private getAccessTokenExpiration(): number {
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '15m');
    
    // Convert string format to seconds
    if (typeof expiresIn === 'string') {
      const match = expiresIn.match(/^(\d+)([smhd])$/);
      if (match) {
        const value = parseInt(match[1], 10);
        const unit = match[2];
        
        switch (unit) {
          case 's': return value;
          case 'm': return value * 60;
          case 'h': return value * 60 * 60;
          case 'd': return value * 24 * 60 * 60;
          default: return 15 * 60; // 15 minutes default
        }
      }
    }

    return typeof expiresIn === 'number' ? expiresIn : 15 * 60;
  }
}
