import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Strategy } from 'passport-custom';

import { Api<PERSON>ey, ApiKeyStatus } from '../../database/entities/api-key.entity';
import { User, UserStatus } from '../../database/entities/user.entity';
import { JwtAuthService } from '../jwt-auth.service';

interface ApiKeyRequest {
  headers: {
    'x-api-key'?: string;
    authorization?: string;
    'user-agent'?: string;
  };
  ip?: string;
  route?: any;
  url?: string;
}

@Injectable()
export class ApiKeyStrategy extends PassportStrategy(Strategy, 'api-key') {
  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtAuthService: JwtAuthService,
  ) {
    super();
  }

  async validate(req: ApiKeyRequest): Promise<any> {
    const apiKey = this.extractApiKey(req);
    
    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    // Find the API key in the database by checking the hash
    const apiKeyEntity = await this.findApiKeyByValue(apiKey);
    
    if (!apiKeyEntity) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Check if API key is active and not expired/revoked
    if (!apiKeyEntity.canBeUsed) {
      throw new UnauthorizedException(`API key is ${apiKeyEntity.status}`);
    }

    // Check IP restrictions
    if (req.ip && !apiKeyEntity.isIpAllowed(req.ip)) {
      throw new UnauthorizedException('IP address not allowed for this API key');
    }

    // Check rate limits
    const rateLimitResult = apiKeyEntity.isRateLimited();
    if (rateLimitResult.limited) {
      throw new UnauthorizedException(rateLimitResult.reason);
    }

    // Get the user who created this API key
    const user = await this.userRepository.findOne({
      where: { id: apiKeyEntity.createdByUserId },
      relations: ['account', 'currentBusinessUnit'],
    });

    if (!user || user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('API key owner is not active');
    }

    // Update usage statistics
    await this.updateApiKeyUsage(apiKeyEntity, req);

    // Return user context with API key information
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      accountId: user.accountId,
      businessUnitId: user.currentBusinessUnitId,
      account: {
        id: user.account.id,
        name: user.account.name,
      },
      businessUnit: user.currentBusinessUnit ? {
        id: user.currentBusinessUnit.id,
        name: user.currentBusinessUnit.name,
        cnpj: user.currentBusinessUnit.cnpj,
      } : undefined,
      apiKey: {
        id: apiKeyEntity.id,
        name: apiKeyEntity.name,
        scopes: apiKeyEntity.scopes,
        permissions: apiKeyEntity.permissions,
      },
      authMethod: 'api-key',
    };
  }

  /**
   * Extract API key from request headers
   */
  private extractApiKey(req: ApiKeyRequest): string | null {
    // Check X-API-Key header first
    if (req.headers['x-api-key']) {
      return req.headers['x-api-key'];
    }

    // Check Authorization header with "Bearer" prefix
    if (req.headers.authorization) {
      const [type, token] = req.headers.authorization.split(' ');
      if (type === 'Bearer' && token && token.startsWith('ezf_')) {
        return token;
      }
    }

    return null;
  }

  /**
   * Find API key by its actual value (check against stored hashes)
   */
  private async findApiKeyByValue(apiKey: string): Promise<ApiKey | null> {
    // Get all active API keys and check their hashes
    // In a production system, you might want to optimize this by using a better lookup strategy
    const apiKeys = await this.apiKeyRepository.find({
      where: { status: ApiKeyStatus.ACTIVE },
      relations: ['account'],
    });

    for (const key of apiKeys) {
      if (this.jwtAuthService.verifyApiKey(apiKey, key.keyHash)) {
        return key;
      }
    }

    return null;
  }

  /**
   * Update API key usage statistics and tracking information
   */
  private async updateApiKeyUsage(apiKey: ApiKey, req: ApiKeyRequest): Promise<void> {
    // Reset usage counters if needed
    apiKey.resetUsageIfNeeded();

    // Increment usage
    apiKey.incrementUsage();

    // Update last usage information
    const userAgent = req.headers['user-agent'];
    const endpoint = (req as any).route?.path || (req as any).url;
    apiKey.updateLastUsage(req.ip, userAgent, endpoint);

    // Save updated API key
    await this.apiKeyRepository.save(apiKey);

    // Check if usage alert should be sent
    if (apiKey.shouldSendUsageAlert()) {
      // TODO: Implement usage alert notification
      // await this.notificationService.sendUsageAlert(apiKey);
      apiKey.markAlertSent();
      await this.apiKeyRepository.save(apiKey);
    }
  }
}
