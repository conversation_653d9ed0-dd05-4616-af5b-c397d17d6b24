import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { JwtAuthService, JwtPayload } from '../jwt-auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly jwtAuthService: JwtAuthService,
    private readonly configService: ConfigService,
  ) {
    const secret = configService.get('JWT_SECRET');
    if (!secret) {
      throw new Error('JWT_SECRET is not configured');
    }
    
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
      passReqToCallback: false,
    });
  }

  async validate(payload: JwtPayload) {
    // Get user information from the JWT payload
    const user = await this.jwtAuthService.getUserFromPayload(payload);
    
    if (!user) {
      throw new UnauthorizedException('Invalid token - user not found or inactive');
    }

    // Return user context for request
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      accountId: payload.accountId,
      businessUnitId: payload.businessUnitId,
      account: user.account,
      businessUnit: user.businessUnit,
      authMethod: 'jwt',
    };
  }
}
