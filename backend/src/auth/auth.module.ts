import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtAuthService } from './jwt-auth.service';
import { EnhancedAuthService } from './enhanced-auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { ApiKeyStrategy } from './strategies/api-key.strategy';
import { TenantGuard } from './guards/tenant.guard';
import { RolesGuard } from './guards/roles.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { ApiKeyGuard } from './guards/api-key.guard';
import { FlexibleAuthGuard } from './guards/flexible-auth.guard';
import { RefreshTokenCleanupTask } from './tasks/refresh-token-cleanup.task';

import { User } from '../database/entities/user.entity';
import { Account } from '../database/entities/account.entity';
import { BusinessUnit } from '../database/entities/business-unit.entity';
import { ApiKey } from '../database/entities/api-key.entity';
import { Subscription } from '../database/entities/subscription.entity';
import { RefreshToken } from './entities/refresh-token.entity';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([
      User,
      Account,
      BusinessUnit,
      ApiKey,
      Subscription,
      RefreshToken,
    ]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '15m'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    // Services
    AuthService,
    JwtAuthService,
    EnhancedAuthService,
    
    // Strategies
    JwtStrategy,
    LocalStrategy,
    ApiKeyStrategy,
    
    // Guards
    TenantGuard,
    RolesGuard,
    JwtAuthGuard,
    ApiKeyGuard,
    FlexibleAuthGuard,
    
    // Tasks
    RefreshTokenCleanupTask,
  ],
  exports: [
    AuthService,
    JwtAuthService,
    EnhancedAuthService,
    TenantGuard,
    RolesGuard,
    JwtAuthGuard,
    ApiKeyGuard,
    FlexibleAuthGuard,
  ],
})
export class AuthModule {}
