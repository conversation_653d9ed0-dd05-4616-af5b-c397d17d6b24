import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
// Removed direct imports to break circular dependencies
// import { User } from '../../database/entities/user.entity';
// import { Account } from '../../database/entities/account.entity';
// import { BusinessUnit } from '../../database/entities/business-unit.entity';

@Entity('refresh_tokens')
@Index(['token'], { unique: true })
@Index(['userId'])
@Index(['accountId'])
@Index(['expiresAt'])
export class RefreshToken {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  /** The account context for which this refresh token was issued */
  @Column({ type: 'uuid' })
  accountId: string;

  /** The business unit context for this refresh token (optional) */
  @Column({ type: 'uuid', nullable: true })
  businessUnitId?: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  token: string;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @Column({ type: 'varchar', length: 45, nullable: true })
  lastUsedFromIp: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  userAgent: string;

  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt: Date;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Relationships - using string references to break circular dependencies
  @ManyToOne('User', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: any;

  @ManyToOne('Account', { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'accountId' })
  account: any;

  @ManyToOne('BusinessUnit', { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'businessUnitId' })
  businessUnit: any;

  // Helper methods
  get isExpired(): boolean {
    return this.expiresAt < new Date();
  }

  get isExpiringSoon(): boolean {
    const hoursUntilExpiration =
      (this.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60);
    return hoursUntilExpiration <= 24; // Expires in 24 hours or less
  }

  updateLastUsage(ip?: string, userAgent?: string): void {
    this.lastUsedAt = new Date();
    if (ip) this.lastUsedFromIp = ip;
    if (userAgent) this.userAgent = userAgent;
  }
}
