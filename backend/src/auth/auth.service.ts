import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Repository } from 'typeorm';
import { EmittersService } from '../emitters/emitters.service';
import { UsersService } from '../users/users.service';
import { SignInDto } from './dto/sign-in.dto';
import { SignUpDto } from './dto/sign-up.dto';
import { RefreshToken } from './entities/refresh-token.entity';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private emittersService: EmittersService,
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
  ) {}

  async signUp(signUpDto: SignUpDto) {
    return this.usersService.create(signUpDto);
  }

  async signIn(signInDto: SignInDto) {
    const { email, password } = signInDto;
    const user = await this.usersService.validateUser(email, password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Get the first account (in a real app, you might want to let the user choose)
    const userAccount = user.userAccounts[0];
    if (!userAccount) {
      throw new UnauthorizedException('User has no accounts');
    }

    // Determine default emitter for this account (first one, if any)
    let defaultEmitterId: string | null = null;
    const emitters = await this.emittersService.findAll(userAccount.accountId);
    if (emitters && emitters.length > 0) {
      defaultEmitterId = emitters[0].id;
    }
    const payload: any = {
      sub: user.id,
      email: user.email,
      accountId: userAccount.accountId,
      role: userAccount.role,
      emitterId: defaultEmitterId,
    };

    // Generate JWT access token
    const accessToken = this.jwtService.sign(payload);
    // Generate and persist refresh token
    const refreshToken = crypto.randomBytes(64).toString('hex');
    // Refresh token expiration (ms): default 7 days
    const expiresInMs = process.env.REFRESH_TOKEN_EXPIRATION_MS
      ? parseInt(process.env.REFRESH_TOKEN_EXPIRATION_MS, 10)
      : 7 * 24 * 60 * 60 * 1000;
    const expiresAt = new Date(Date.now() + expiresInMs);
    const refreshTokenEntity = this.refreshTokenRepository.create({
      userId: user.id,
      accountId: userAccount.accountId,
      token: refreshToken,
      expiresAt,
    });
    await this.refreshTokenRepository.save(refreshTokenEntity);
    return {
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        account: {
          id: userAccount.account.id,
          name: userAccount.account.name,
        },
        role: userAccount.role,
        emitterId: defaultEmitterId,
      },
    };
  }

  async validateUser(email: string, password: string) {
    return this.usersService.validateUser(email, password);
  }

  /**
   * Exchange a valid refresh token for a new access token (and rotate it)
   */
  async refresh(oldToken: string) {
    // Find token
    const tokenEntity = await this.refreshTokenRepository.findOne({
      where: { token: oldToken },
    });
    if (!tokenEntity) {
      throw new UnauthorizedException('Invalid refresh token');
    }
    // Check expiration
    if (tokenEntity.expiresAt.getTime() < Date.now()) {
      await this.refreshTokenRepository.delete(tokenEntity.id);
      throw new UnauthorizedException('Refresh token expired');
    }
    // Load user and account context
    const user = await this.usersService.findById(tokenEntity.userId);
    // Find corresponding user-account relation
    const userAccount = user.userAccounts.find(
      (ua) => ua.accountId === tokenEntity.accountId,
    );
    if (!userAccount) {
      throw new UnauthorizedException('Invalid token context');
    }
    // Create new access token
    // Determine default emitter context for refreshed token
    let defaultEmitterId: string | null = null;
    const emitters = await this.emittersService.findAll(userAccount.accountId);
    if (emitters && emitters.length > 0) {
      defaultEmitterId = emitters[0].id;
    }
    const payload: any = {
      sub: user.id,
      email: user.email,
      accountId: userAccount.accountId,
      role: userAccount.role,
      emitterId: defaultEmitterId,
    };
    const accessToken = this.jwtService.sign(payload);
    // Rotate refresh token: generate new value and expiration
    const newToken = crypto.randomBytes(64).toString('hex');
    const expiresInMs2 = process.env.REFRESH_TOKEN_EXPIRATION_MS
      ? parseInt(process.env.REFRESH_TOKEN_EXPIRATION_MS, 10)
      : 7 * 24 * 60 * 60 * 1000;
    tokenEntity.token = newToken;
    tokenEntity.expiresAt = new Date(Date.now() + expiresInMs2);
    await this.refreshTokenRepository.save(tokenEntity);
    return { accessToken, refreshToken: newToken };
  }

  /**
   * Logout a user by revoking their refresh token
   */
  async logout(refreshToken: string): Promise<{ success: boolean }> {
    // Find token
    const tokenEntity = await this.refreshTokenRepository.findOne({
      where: { token: refreshToken },
    });

    if (!tokenEntity) {
      throw new NotFoundException('Refresh token not found');
    }

    // Delete the token from the database
    await this.refreshTokenRepository.delete(tokenEntity.id);

    return { success: true };
  }

  /**
   * Get all emitters for an account
   */
  async getEmittersForAccount(accountId: string) {
    return this.emittersService.findAll(accountId);
  }

  /**
   * Switch the emitter context for a user
   * Issues a new access token with the updated emitterId in the payload
   */
  async switchEmitter(
    userId: string,
    accountId: string,
    emitterId: string,
    refreshToken?: string,
  ) {
    // Validate that the emitter belongs to the user's account
    const emitter = await this.emittersService.findOne(emitterId, accountId);

    if (!emitter) {
      throw new NotFoundException(
        `Emitter with ID ${emitterId} not found in your account`,
      );
    }

    // Load user to get email and role
    const user = await this.usersService.findById(userId);

    // Find corresponding user-account relation
    const userAccount = user.userAccounts.find(
      (ua) => ua.accountId === accountId,
    );

    if (!userAccount) {
      throw new UnauthorizedException('Invalid account context');
    }

    // Create new access token with updated emitterId
    const payload: any = {
      sub: userId,
      email: user.email,
      accountId: accountId,
      role: userAccount.role,
      emitterId: emitterId,
    };

    const accessToken = this.jwtService.sign(payload);

    // If refresh token is provided, rotate it
    let newRefreshToken: string | undefined;

    if (refreshToken) {
      const tokenEntity = await this.refreshTokenRepository.findOne({
        where: { token: refreshToken },
      });

      if (!tokenEntity) {
        throw new NotFoundException('Refresh token not found');
      }

      // Check expiration
      if (tokenEntity.expiresAt.getTime() < Date.now()) {
        await this.refreshTokenRepository.delete(tokenEntity.id);
        throw new UnauthorizedException('Refresh token expired');
      }

      // Rotate refresh token: generate new value and expiration
      newRefreshToken = crypto.randomBytes(64).toString('hex');
      const expiresInMs = process.env.REFRESH_TOKEN_EXPIRATION_MS
        ? parseInt(process.env.REFRESH_TOKEN_EXPIRATION_MS, 10)
        : 7 * 24 * 60 * 60 * 1000;
      tokenEntity.token = newRefreshToken;
      tokenEntity.expiresAt = new Date(Date.now() + expiresInMs);
      await this.refreshTokenRepository.save(tokenEntity);
    }

    // Return the new tokens and user context
    return {
      accessToken,
      refreshToken: newRefreshToken,
      user: {
        id: user.id,
        email: user.email,
        account: {
          id: userAccount.account.id,
          name: userAccount.account.name,
        },
        role: userAccount.role,
        emitterId: emitterId,
      },
    };
  }
}
