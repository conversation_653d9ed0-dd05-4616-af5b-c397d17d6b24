import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CertificateStatus } from '../common/enums/certificate-status.enum';
import { EmittersService } from '../emitters/emitters.service';
import { Emitter } from '../emitters/entities/emitter.entity';
import { UserRole } from '../users/entities/user-account.entity';
import { CertificatesService } from './certificates.service';
import { UploadCertificateDto } from './dto/upload-certificate.dto';
import { Certificate } from './entities/certificate.entity';

const mockCertificateRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(),
  update: jest.fn(),
});

const mockEmittersService = () => ({
  findOne: jest.fn(),
});

const mockEmitterRepository = () => ({
  update: jest.fn(),
});

describe('CertificatesService', () => {
  let service: CertificatesService;
  let certificateRepository: Repository<Certificate>;
  let emittersService: EmittersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CertificatesService,
        {
          provide: getRepositoryToken(Certificate),
          useFactory: mockCertificateRepository,
        },
        {
          provide: EmittersService,
          useFactory: mockEmittersService,
        },
        {
          provide: getRepositoryToken(Emitter),
          useFactory: mockEmitterRepository,
        },
      ],
    }).compile();

    service = module.get<CertificatesService>(CertificatesService);
    certificateRepository = module.get<Repository<Certificate>>(
      getRepositoryToken(Certificate),
    );
    emittersService = module.get<EmittersService>(EmittersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('upload', () => {
    it('should upload a certificate', async () => {
      const emitterId = 'test-emitter-id';
      const accountId = 'test-account-id';
      const uploadCertificateDto: UploadCertificateDto = {
        pfxBuffer: Buffer.from('test-pfx-buffer'),
        password: 'test-password',
      };

      const mockEmitter = {
        id: emitterId,
        name: 'Test Emitter',
        accountId,
      };

      const mockCertificate = {
        id: 'test-certificate-id',
        emitterId,
        serialNumber: 'test-serial-number',
        subject: 'CN=Test Emitter, O=Test Emitter, C=BR',
        issuer: 'CN=AC-TEST, O=ICP-Brasil, C=BR',
        validFrom: new Date(),
        validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        thumbprint: 'test-thumbprint',
        pfxEncrypted: 'test-pfx-encrypted',
        password: 'test-password',
        status: CertificateStatus.VALID,
        isActive: false,
      };

      jest
        .spyOn(emittersService, 'findOne')
        .mockResolvedValue(mockEmitter as any);
      jest
        .spyOn(certificateRepository, 'create')
        .mockReturnValue(mockCertificate as any);
      jest
        .spyOn(certificateRepository, 'save')
        .mockResolvedValue(mockCertificate as any);

      const result = await service.upload(
        emitterId,
        accountId,
        uploadCertificateDto,
      );

      expect(emittersService.findOne).toHaveBeenCalledWith(
        emitterId,
        accountId,
      );
      expect(certificateRepository.create).toHaveBeenCalled();
      expect(certificateRepository.save).toHaveBeenCalledWith(mockCertificate);
      expect(result).toEqual(mockCertificate);
    });

    it('should throw NotFoundException if emitter not found', async () => {
      const emitterId = 'test-emitter-id';
      const accountId = 'test-account-id';
      const uploadCertificateDto: UploadCertificateDto = {
        pfxBuffer: Buffer.from('test-pfx-buffer'),
        password: 'test-password',
      };

      jest.spyOn(emittersService, 'findOne').mockImplementation(() => {
        throw new NotFoundException('Emitter not found');
      });

      await expect(
        service.upload(emitterId, accountId, uploadCertificateDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if certificate processing fails', async () => {
      const emitterId = 'test-emitter-id';
      const accountId = 'test-account-id';
      const uploadCertificateDto: UploadCertificateDto = {
        pfxBuffer: Buffer.from('test-pfx-buffer'),
        password: 'test-password',
      };

      const mockEmitter = {
        id: emitterId,
        name: 'Test Emitter',
        accountId,
      };

      jest
        .spyOn(emittersService, 'findOne')
        .mockResolvedValue(mockEmitter as any);
      jest.spyOn(certificateRepository, 'create').mockImplementation(() => {
        throw new Error('Certificate processing error');
      });

      await expect(
        service.upload(emitterId, accountId, uploadCertificateDto),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    it('should return all certificates for an emitter', async () => {
      const emitterId = 'test-emitter-id';
      const accountId = 'test-account-id';

      const mockEmitter = {
        id: emitterId,
        name: 'Test Emitter',
        accountId,
      };

      const mockCertificates = [
        {
          id: 'test-certificate-id-1',
          emitterId,
          serialNumber: 'test-serial-number-1',
        },
        {
          id: 'test-certificate-id-2',
          emitterId,
          serialNumber: 'test-serial-number-2',
        },
      ];

      jest
        .spyOn(emittersService, 'findOne')
        .mockResolvedValue(mockEmitter as any);
      jest
        .spyOn(certificateRepository, 'find')
        .mockResolvedValue(mockCertificates as any);

      const result = await service.findAll(emitterId, accountId);

      expect(emittersService.findOne).toHaveBeenCalledWith(
        emitterId,
        accountId,
      );
      expect(certificateRepository.find).toHaveBeenCalledWith({
        where: { emitterId },
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(mockCertificates);
    });

    it('should throw NotFoundException if emitter not found', async () => {
      const emitterId = 'test-emitter-id';
      const accountId = 'test-account-id';

      jest.spyOn(emittersService, 'findOne').mockImplementation(() => {
        throw new NotFoundException('Emitter not found');
      });

      await expect(service.findAll(emitterId, accountId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('activate', () => {
    it('should activate a certificate and revoke other valid certificates', async () => {
      const certificateId = 'test-certificate-id';
      const accountId = 'test-account-id';
      const emitterId = 'test-emitter-id';

      const mockCertificate = {
        id: certificateId,
        emitterId,
        status: CertificateStatus.PENDING,
        isActive: false,
        emitter: {
          id: emitterId,
          accountId,
        },
      };

      const activatedCertificate = {
        ...mockCertificate,
        status: CertificateStatus.VALID,
        isActive: true,
        activatedAt: expect.any(Date),
      };

      // Mock existing valid certificates
      const existingValidCertificates = [
        {
          id: 'existing-cert-1',
          emitterId,
          status: CertificateStatus.VALID,
          isActive: true,
        },
        {
          id: 'existing-cert-2',
          emitterId,
          status: CertificateStatus.VALID,
          isActive: false,
        },
      ];

      jest
        .spyOn(certificateRepository, 'findOne')
        .mockResolvedValue(mockCertificate as any);

      jest
        .spyOn(certificateRepository, 'find')
        .mockResolvedValue(existingValidCertificates as any);

      const saveSpy = jest
        .spyOn(certificateRepository, 'save')
        .mockImplementation((entity) => {
          if (entity.id === certificateId) {
            return Promise.resolve(activatedCertificate as any);
          }
          return Promise.resolve(entity as any);
        });

      const emitterRepoUpdateSpy = jest
        .spyOn(module.get(getRepositoryToken(Emitter)), 'update')
        .mockResolvedValue({ affected: 1 } as any);

      const result = await service.activate(
        certificateId,
        accountId,
        UserRole.OWNER,
      );

      // Verify certificate lookup
      expect(certificateRepository.findOne).toHaveBeenCalledWith({
        where: { id: certificateId },
        relations: ['emitter'],
      });

      // Verify finding existing valid certificates
      expect(certificateRepository.find).toHaveBeenCalledWith({
        where: {
          emitterId: mockCertificate.emitterId,
          status: CertificateStatus.VALID,
        },
      });

      // Verify existing certificates were revoked
      expect(saveSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'existing-cert-1',
          status: CertificateStatus.REVOKED,
          isActive: false,
        }),
      );

      expect(saveSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'existing-cert-2',
          status: CertificateStatus.REVOKED,
          isActive: false,
        }),
      );

      // Verify new certificate was activated
      expect(saveSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          id: certificateId,
          status: CertificateStatus.VALID,
          isActive: true,
          activatedAt: expect.any(Date),
        }),
      );

      // Verify emitter status was updated
      expect(emitterRepoUpdateSpy).toHaveBeenCalledWith(
        mockCertificate.emitterId,
        { certificateStatus: CertificateStatus.VALID },
      );

      expect(result).toEqual(activatedCertificate);
    });

    it('should throw NotFoundException if certificate not found', async () => {
      const certificateId = 'test-certificate-id';
      const accountId = 'test-account-id';

      jest.spyOn(certificateRepository, 'findOne').mockResolvedValue(null);

      await expect(
        service.activate(certificateId, accountId, UserRole.OWNER),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if certificate belongs to different account', async () => {
      const certificateId = 'test-certificate-id';
      const accountId = 'test-account-id';
      const emitterId = 'test-emitter-id';

      const mockCertificate = {
        id: certificateId,
        emitterId,
        isActive: false,
        emitter: {
          id: emitterId,
          accountId: 'different-account-id',
        },
      };

      jest
        .spyOn(certificateRepository, 'findOne')
        .mockResolvedValue(mockCertificate as any);

      await expect(
        service.activate(certificateId, accountId, UserRole.OWNER),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
