import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThan, MoreThan, Repository } from 'typeorm';
import { CertificateStatus } from '../../common/enums/certificate-status.enum';
import { Emitter } from '../../emitters/entities/emitter.entity';
import { Certificate } from '../entities/certificate.entity';

@Injectable()
export class CertificateExpirationTask {
  private readonly logger = new Logger(CertificateExpirationTask.name);

  constructor(
    @InjectRepository(Certificate)
    private certificatesRepository: Repository<Certificate>,
    @InjectRepository(Emitter)
    private emittersRepository: Repository<Emitter>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCertificateExpiration() {
    this.logger.debug('Running certificate expiration check');

    const now = new Date();

    // Find all certificates that have expired but are still marked as valid
    const expiredCertificates = await this.certificatesRepository.find({
      where: {
        validTo: Less<PERSON>han(now),
        status: CertificateStatus.VALID,
      },
      relations: ['emitter'],
    });

    for (const certificate of expiredCertificates) {
      this.logger.debug(
        `Certificate ${certificate.id} has expired, marking as expired`,
      );

      // Mark the certificate as expired
      certificate.status = CertificateStatus.EXPIRED;
      await this.certificatesRepository.save(certificate);

      // Check if there's a pending certificate that can be activated
      const pendingCertificate = await this.certificatesRepository.findOne({
        where: {
          emitterId: certificate.emitterId,
          status: CertificateStatus.PENDING,
          validFrom: LessThan(now),
          validTo: MoreThan(now),
        },
        order: { validTo: 'DESC' },
      });

      if (pendingCertificate) {
        this.logger.debug(
          `Activating pending certificate ${pendingCertificate.id} for emitter ${certificate.emitterId}`,
        );

        // Activate the pending certificate
        pendingCertificate.status = CertificateStatus.VALID;
        pendingCertificate.activatedAt = now;
        await this.certificatesRepository.save(pendingCertificate);

        // Update the emitter's certificate status
        await this.emittersRepository.update(
          { id: certificate.emitterId },
          { certificateStatus: CertificateStatus.VALID },
        );
      } else {
        // No pending certificate, mark the emitter as having an expired certificate
        await this.emittersRepository.update(
          { id: certificate.emitterId },
          { certificateStatus: CertificateStatus.EXPIRED },
        );
      }
    }

    this.logger.debug('Certificate expiration check completed');
  }
}
