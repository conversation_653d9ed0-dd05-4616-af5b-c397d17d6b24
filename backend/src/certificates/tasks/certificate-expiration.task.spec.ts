import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CertificateStatus } from '../../common/enums/certificate-status.enum';
import { Emitter } from '../../emitters/entities/emitter.entity';
import { Certificate } from '../entities/certificate.entity';
import { CertificateExpirationTask } from './certificate-expiration.task';

describe('CertificateExpirationTask', () => {
  let task: CertificateExpirationTask;
  let certificateRepository: Repository<Certificate>;
  let emitterRepository: Repository<Emitter>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CertificateExpirationTask,
        {
          provide: getRepositoryToken(Certificate),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Emitter),
          useClass: Repository,
        },
      ],
    }).compile();

    task = module.get<CertificateExpirationTask>(CertificateExpirationTask);
    certificateRepository = module.get<Repository<Certificate>>(
      getRepositoryToken(Certificate),
    );
    emitterRepository = module.get<Repository<Emitter>>(
      getRepositoryToken(Emitter),
    );
  });

  it('should be defined', () => {
    expect(task).toBeDefined();
  });

  describe('handleCertificateExpiration', () => {
    it('should mark expired certificates as EXPIRED and update emitter status when no pending certificates exist', async () => {
      // Mock the current date
      const now = new Date('2023-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => now as any);

      // Mock an expired certificate
      const expiredCertificate = {
        id: 'expired-cert-id',
        emitterId: 'emitter-id',
        status: CertificateStatus.VALID,
        validTo: new Date('2023-01-14T12:00:00Z'), // Expired yesterday
        emitter: {
          id: 'emitter-id',
          accountId: 'account-id',
        },
      };

      // Mock the repository methods
      jest
        .spyOn(certificateRepository, 'find')
        .mockResolvedValue([expiredCertificate as any]);
      jest
        .spyOn(certificateRepository, 'save')
        .mockImplementation((entity) => Promise.resolve(entity as any));
      jest.spyOn(certificateRepository, 'findOne').mockResolvedValue(null); // No pending certificate
      jest
        .spyOn(emitterRepository, 'update')
        .mockResolvedValue({ affected: 1 } as any);

      // Run the task
      await task.handleCertificateExpiration();

      // Verify the certificate was marked as expired
      expect(certificateRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'expired-cert-id',
          status: CertificateStatus.EXPIRED,
        }),
      );

      // Verify the emitter status was updated to EXPIRED since there are no pending certificates
      expect(emitterRepository.update).toHaveBeenCalledWith(
        { id: 'emitter-id' },
        { certificateStatus: CertificateStatus.EXPIRED },
      );
    });

    it('should activate pending certificates when the current one expires', async () => {
      // Mock the current date
      const now = new Date('2023-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => now as any);

      // Mock an expired certificate
      const expiredCertificate = {
        id: 'expired-cert-id',
        emitterId: 'emitter-id',
        status: CertificateStatus.VALID,
        validTo: new Date('2023-01-14T12:00:00Z'), // Expired yesterday
        emitter: {
          id: 'emitter-id',
          accountId: 'account-id',
        },
      };

      // Mock a pending certificate
      const pendingCertificate = {
        id: 'pending-cert-id',
        emitterId: 'emitter-id',
        status: CertificateStatus.PENDING,
        validFrom: new Date('2023-01-01T12:00:00Z'), // Valid from 2 weeks ago
        validTo: new Date('2024-01-01T12:00:00Z'), // Valid for another year
      };

      // Mock the repository methods
      jest
        .spyOn(certificateRepository, 'find')
        .mockResolvedValue([expiredCertificate as any]);
      jest
        .spyOn(certificateRepository, 'save')
        .mockImplementation((entity) => Promise.resolve(entity as any));
      jest
        .spyOn(certificateRepository, 'findOne')
        .mockResolvedValue(pendingCertificate as any);
      jest
        .spyOn(emitterRepository, 'update')
        .mockResolvedValue({ affected: 1 } as any);

      // Run the task
      await task.handleCertificateExpiration();

      // Verify the expired certificate was marked as expired
      expect(certificateRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'expired-cert-id',
          status: CertificateStatus.EXPIRED,
        }),
      );

      // Verify the pending certificate was activated
      expect(certificateRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'pending-cert-id',
          status: CertificateStatus.VALID,
          activatedAt: now,
        }),
      );

      // Verify the emitter status was updated to VALID
      expect(emitterRepository.update).toHaveBeenCalledWith(
        { id: 'emitter-id' },
        { certificateStatus: CertificateStatus.VALID },
      );
    });

    it('should handle multiple expired certificates', async () => {
      // Mock the current date
      const now = new Date('2023-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => now as any);

      // Mock multiple expired certificates for different emitters
      const expiredCertificates = [
        {
          id: 'expired-cert-1',
          emitterId: 'emitter-1',
          status: CertificateStatus.VALID,
          validTo: new Date('2023-01-14T12:00:00Z'), // Expired yesterday
          emitter: {
            id: 'emitter-1',
            accountId: 'account-id',
          },
        },
        {
          id: 'expired-cert-2',
          emitterId: 'emitter-2',
          status: CertificateStatus.VALID,
          validTo: new Date('2023-01-10T12:00:00Z'), // Expired 5 days ago
          emitter: {
            id: 'emitter-2',
            accountId: 'account-id',
          },
        },
      ];

      // Mock the repository methods
      jest
        .spyOn(certificateRepository, 'find')
        .mockResolvedValue(expiredCertificates as any);
      jest
        .spyOn(certificateRepository, 'save')
        .mockImplementation((entity) => Promise.resolve(entity as any));

      // Mock findOne to return a pending certificate for emitter-1 but not for emitter-2
      jest
        .spyOn(certificateRepository, 'findOne')
        .mockImplementation((options: any) => {
          if (options.where.emitterId === 'emitter-1') {
            return Promise.resolve({
              id: 'pending-cert-1',
              emitterId: 'emitter-1',
              status: CertificateStatus.PENDING,
              validFrom: new Date('2023-01-01T12:00:00Z'),
              validTo: new Date('2024-01-01T12:00:00Z'),
            } as any);
          }
          return Promise.resolve(null);
        });

      jest
        .spyOn(emitterRepository, 'update')
        .mockResolvedValue({ affected: 1 } as any);

      // Run the task
      await task.handleCertificateExpiration();

      // Verify both certificates were marked as expired
      expect(certificateRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'expired-cert-1',
          status: CertificateStatus.EXPIRED,
        }),
      );

      expect(certificateRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'expired-cert-2',
          status: CertificateStatus.EXPIRED,
        }),
      );

      // Verify the pending certificate for emitter-1 was activated
      expect(certificateRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'pending-cert-1',
          status: CertificateStatus.VALID,
          activatedAt: now,
        }),
      );

      // Verify emitter statuses were updated correctly
      expect(emitterRepository.update).toHaveBeenCalledWith(
        { id: 'emitter-1' },
        { certificateStatus: CertificateStatus.VALID },
      );

      expect(emitterRepository.update).toHaveBeenCalledWith(
        { id: 'emitter-2' },
        { certificateStatus: CertificateStatus.EXPIRED },
      );
    });

    it('should not process certificates that are not expired', async () => {
      // Mock the current date
      const now = new Date('2023-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => now as any);

      // Mock a valid certificate that is not expired
      const validCertificate = {
        id: 'valid-cert-id',
        emitterId: 'emitter-id',
        status: CertificateStatus.VALID,
        validTo: new Date('2023-02-15T12:00:00Z'), // Expires in a month
        emitter: {
          id: 'emitter-id',
          accountId: 'account-id',
        },
      };

      // Mock the repository methods
      jest.spyOn(certificateRepository, 'find').mockResolvedValue([]);
      const saveSpy = jest
        .spyOn(certificateRepository, 'save')
        .mockImplementation((entity) => Promise.resolve(entity as any));
      const updateSpy = jest
        .spyOn(emitterRepository, 'update')
        .mockResolvedValue({ affected: 0 } as any);

      // Run the task
      await task.handleCertificateExpiration();

      // Verify no certificates were updated
      expect(saveSpy).not.toHaveBeenCalled();
      expect(updateSpy).not.toHaveBeenCalled();
    });
  });
});
