import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CertificateStatus } from '../../common/enums/certificate-status.enum';
import { Emitter } from '../../emitters/entities/emitter.entity';

@Entity('certificates')
export class Certificate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  emitterId: string;

  @ManyToOne(() => Emitter, (emitter) => emitter.certificates)
  emitter: Emitter;

  @Column()
  serialNumber: string;

  @Column()
  subject: string;

  @Column()
  issuer: string;

  @Column()
  validFrom: Date;

  @Column()
  validTo: Date;

  @Column()
  thumbprint: string;

  @Column({ type: 'text' })
  pfxEncrypted: string;

  @Column()
  password: string;

  @Column({
    type: process.env.NODE_ENV === 'development' ? 'varchar' : 'enum',
    enum: CertificateStatus,
    default: CertificateStatus.PENDING,
  })
  status: CertificateStatus;

  @Column({ default: false })
  isActive: boolean;

  @Column({ nullable: true })
  activatedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
