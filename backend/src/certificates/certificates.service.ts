import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CertificateStatus } from '../common/enums/certificate-status.enum';
import { EmittersService } from '../emitters/emitters.service';
import { Emitter } from '../emitters/entities/emitter.entity';
import { UserRole } from '../database/entities/enums';
import { UploadCertificateDto } from './dto/upload-certificate.dto';
import { Certificate } from './entities/certificate.entity';
// Import crypto as a Node.js module
// Import crypto and system utilities
import { execSync } from 'child_process';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

@Injectable()
export class CertificatesService {
  constructor(
    @InjectRepository(Certificate)
    private certificatesRepository: Repository<Certificate>,
    private emittersService: EmittersService,
    @InjectRepository(Emitter)
    private readonly emitterRepository: Repository<Emitter>,
  ) {}

  async upload(
    emitterId: string,
    accountId: string,
    uploadCertificateDto: UploadCertificateDto,
  ): Promise<Certificate> {
    const emitter = await this.emittersService.findOne(emitterId, accountId);

    if (!emitter) {
      throw new NotFoundException('Emitter not found');
    }

    try {
      const { pfxBuffer, password } = uploadCertificateDto;
      // Write PFX to temp file
      const tmpDir = os.tmpdir();
      const pfxPath = path.join(tmpDir, `cert-${Date.now()}.pfx`);
      fs.writeFileSync(pfxPath, pfxBuffer);
      // Extract client certificate (PEM) via OpenSSL
      const pemPath = pfxPath + '.pem';
      try {
        execSync(
          `openssl pkcs12 -in ${pfxPath} -passin pass:${password} -clcerts -nokeys -nodes -out ${pemPath}`,
        );
      } catch {
        throw new BadRequestException('Invalid PFX or wrong password');
      }
      const pem = fs.readFileSync(pemPath, 'utf8');
      let x509: crypto.X509Certificate;
      try {
        x509 = new crypto.X509Certificate(pem);
      } catch {
        throw new BadRequestException('Failed to parse certificate');
      }
      const serialNumber = x509.serialNumber;
      const subject = x509.subject;
      const issuer = x509.issuer;
      const validFrom = new Date(x509.validFrom);
      const validTo = new Date(x509.validTo);
      const thumbprint = x509.fingerprint; // SHA-1 fingerprint
      // Encrypt PFX buffer with AES-256-CBC
      const keyBase64 = process.env.CERTIFICATE_ENCRYPTION_KEY;
      if (!keyBase64) {
        throw new InternalServerErrorException('Encryption key not configured');
      }
      const key = Buffer.from(keyBase64, 'base64');
      if (key.length !== 32) {
        throw new InternalServerErrorException('Invalid encryption key length');
      }
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      const encrypted = Buffer.concat([
        cipher.update(pfxBuffer),
        cipher.final(),
      ]);
      const pfxEncrypted = iv.toString('hex') + ':' + encrypted.toString('hex');
      // Encrypt password similarly
      const ivPwd = crypto.randomBytes(16);
      const cipherPwd = crypto.createCipheriv('aes-256-cbc', key, ivPwd);
      const pwdEncryptedBuf = Buffer.concat([
        cipherPwd.update(Buffer.from(password, 'utf8')),
        cipherPwd.final(),
      ]);
      const passwordEncrypted =
        ivPwd.toString('hex') + ':' + pwdEncryptedBuf.toString('hex');
      // Create certificate entity (pending by default)
      const certificate = this.certificatesRepository.create({
        emitterId,
        serialNumber,
        subject,
        issuer,
        validFrom,
        validTo,
        thumbprint,
        pfxEncrypted,
        password: passwordEncrypted,
        status: CertificateStatus.PENDING,
        isActive: false,
      });
      const saved = await this.certificatesRepository.save(certificate);
      // Clean up temp files
      try {
        fs.unlinkSync(pfxPath);
        fs.unlinkSync(pemPath);
      } catch {}
      return saved;
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(
        `Failed to process certificate: ${error.message}`,
      );
    }
  }

  async findAll(emitterId: string, accountId: string): Promise<Certificate[]> {
    const emitter = await this.emittersService.findOne(emitterId, accountId);

    if (!emitter) {
      throw new NotFoundException('Emitter not found');
    }

    return this.certificatesRepository.find({
      where: { emitterId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Manually activate a pending certificate (owner/admin only)
   */
  async activate(
    id: string,
    accountId: string,
    userRole: UserRole,
  ): Promise<Certificate> {
    const certificate = await this.certificatesRepository.findOne({
      where: { id },
      relations: ['emitter'],
    });

    if (!certificate || certificate.emitter.accountId !== accountId) {
      throw new NotFoundException('Certificate not found');
    }
    // Only owner/admin may activate certificates
    if (![UserRole.OWNER, UserRole.ADMIN].includes(userRole)) {
      throw new ForbiddenException(
        'Insufficient permissions to activate certificate',
      );
    }
    // Only pending certificates can be activated
    if (certificate.status !== CertificateStatus.PENDING) {
      throw new ConflictException('Certificate cannot be activated');
    }

    // Deactivate other valid certificates for this emitter
    const otherValidCertificates = await this.certificatesRepository.find({
      where: {
        emitterId: certificate.emitterId,
        status: CertificateStatus.VALID,
      },
    });

    // Update each certificate except the current one
    for (const cert of otherValidCertificates) {
      if (cert.id !== certificate.id) {
        cert.isActive = false;
        cert.status = CertificateStatus.REVOKED;
        await this.certificatesRepository.save(cert);
      }
    }
    // Activate this certificate
    certificate.status = CertificateStatus.VALID;
    certificate.isActive = true;
    certificate.activatedAt = new Date();
    const saved = await this.certificatesRepository.save(certificate);
    // Update emitter's overall certificate status
    await this.emitterRepository.update(certificate.emitterId, {
      certificateStatus: CertificateStatus.VALID,
    });
    return saved;
  }
}
