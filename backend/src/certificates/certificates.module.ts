import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmittersModule } from '../emitters/emitters.module';
import { Emitter } from '../emitters/entities/emitter.entity';
import { CertificatesController } from './certificates.controller';
import { CertificatesService } from './certificates.service';
import { Certificate } from './entities/certificate.entity';
import { CertificateExpirationTask } from './tasks/certificate-expiration.task';

@Module({
  imports: [
    TypeOrmModule.forFeature([Certificate, Emitter]),
    ScheduleModule.forRoot(),
    EmittersModule,
  ],
  controllers: [CertificatesController],
  providers: [CertificatesService, CertificateExpirationTask],
  exports: [CertificatesService],
})
export class CertificatesModule {}
