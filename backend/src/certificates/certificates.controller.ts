import {
  Controller,
  Post,
  Get,
  Patch,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CertificatesService } from './certificates.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UploadCertificateDto } from './dto/upload-certificate.dto';

@Controller('emitters/:emitterId/certificates')
@UseGuards(JwtAuthGuard)
export class CertificatesController {
  constructor(private readonly certificatesService: CertificatesService) {}

  @Post()
  @UseInterceptors(FileInterceptor('pfxFile'))
  async upload(
    @Param('emitterId') emitterId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 }), // 1MB
          new FileTypeValidator({ fileType: 'application/x-pkcs12' }),
        ],
      }),
    )
    pfxFile: any,
    @Body('password') password: string,
    @CurrentUser() user: any,
  ) {
    const uploadCertificateDto: UploadCertificateDto = {
      pfxBuffer: pfxFile.buffer,
      password,
    };

    return this.certificatesService.upload(
      emitterId,
      user.accountId,
      uploadCertificateDto,
    );
  }

  @Get()
  findAll(@Param('emitterId') emitterId: string, @CurrentUser() user: any) {
    return this.certificatesService.findAll(emitterId, user.accountId);
  }

  @Patch(':id/activate')
  activate(@Param('id') id: string, @CurrentUser() user: any) {
    return this.certificatesService.activate(id, user.accountId, user.role);
  }
}
