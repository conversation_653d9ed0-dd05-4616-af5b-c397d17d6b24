/**
 * Brazilian document validation utilities
 * Provides validation for CNPJ, CPF, CEP, and other Brazilian-specific formats
 */

/**
 * Validates Brazilian CNPJ (Cadastro Nacional de Pessoa Jurídica)
 * @param cnpj - CNPJ string with or without formatting
 * @returns boolean indicating if CNPJ is valid
 */
export function validateCNPJ(cnpj: string): boolean {
  if (!cnpj) return false;

  // Remove all non-numeric characters
  const cleanCnpj = cnpj.replace(/[^\d]/g, '');

  // Check if has 14 digits
  if (cleanCnpj.length !== 14) return false;

  // Check if all digits are the same (invalid CNPJ)
  if (/^(\d)\1{13}$/.test(cleanCnpj)) return false;

  // Validate first check digit
  let sum = 0;
  let weight = 5;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cleanCnpj[i]) * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  let remainder = sum % 11;
  let checkDigit1 = remainder < 2 ? 0 : 11 - remainder;

  if (parseInt(cleanCnpj[12]) !== checkDigit1) return false;

  // Validate second check digit
  sum = 0;
  weight = 6;
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cleanCnpj[i]) * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  remainder = sum % 11;
  let checkDigit2 = remainder < 2 ? 0 : 11 - remainder;

  return parseInt(cleanCnpj[13]) === checkDigit2;
}

/**
 * Validates Brazilian CPF (Cadastro de Pessoas Físicas)
 * @param cpf - CPF string with or without formatting
 * @returns boolean indicating if CPF is valid
 */
export function validateCPF(cpf: string): boolean {
  if (!cpf) return false;

  // Remove all non-numeric characters
  const cleanCpf = cpf.replace(/[^\d]/g, '');

  // Check if has 11 digits
  if (cleanCpf.length !== 11) return false;

  // Check if all digits are the same (invalid CPF)
  if (/^(\d)\1{10}$/.test(cleanCpf)) return false;

  // Validate first check digit
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCpf[i]) * (10 - i);
  }
  let remainder = sum % 11;
  let checkDigit1 = remainder < 2 ? 0 : 11 - remainder;

  if (parseInt(cleanCpf[9]) !== checkDigit1) return false;

  // Validate second check digit
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCpf[i]) * (11 - i);
  }
  remainder = sum % 11;
  let checkDigit2 = remainder < 2 ? 0 : 11 - remainder;

  return parseInt(cleanCpf[10]) === checkDigit2;
}

/**
 * Validates Brazilian CEP (Código de Endereçamento Postal)
 * @param cep - CEP string with or without formatting
 * @returns boolean indicating if CEP format is valid
 */
export function validateCEP(cep: string): boolean {
  if (!cep) return false;

  // Remove all non-numeric characters
  const cleanCep = cep.replace(/[^\d]/g, '');

  // Check if has 8 digits
  if (cleanCep.length !== 8) return false;

  // Check if all digits are zeros (invalid CEP)
  if (cleanCep === '00000000') return false;

  return true;
}

/**
 * Validates Brazilian State Registration (Inscrição Estadual)
 * Basic validation - each state has specific rules
 * @param stateRegistration - State registration string
 * @param state - Brazilian state code (2 letters)
 * @returns boolean indicating if format is valid
 */
export function validateStateRegistration(stateRegistration: string, state?: string): boolean {
  if (!stateRegistration) return false;

  // Remove all non-alphanumeric characters
  const clean = stateRegistration.replace(/[^\w]/g, '').toUpperCase();

  // Check for ISENTO (exempt)
  if (clean === 'ISENTO') return true;

  // Basic length validation (most states use 9-12 characters)
  if (clean.length < 8 || clean.length > 14) return false;

  // TODO: Implement state-specific validation rules
  // For now, just check that it contains at least one number
  return /\d/.test(clean);
}

/**
 * Formats CNPJ with standard Brazilian formatting
 * @param cnpj - CNPJ string
 * @returns formatted CNPJ (XX.XXX.XXX/XXXX-XX)
 */
export function formatCNPJ(cnpj: string): string {
  if (!cnpj) return '';

  const cleanCnpj = cnpj.replace(/[^\d]/g, '');
  if (cleanCnpj.length !== 14) return cnpj;

  return cleanCnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5');
}

/**
 * Formats CPF with standard Brazilian formatting
 * @param cpf - CPF string
 * @returns formatted CPF (XXX.XXX.XXX-XX)
 */
export function formatCPF(cpf: string): string {
  if (!cpf) return '';

  const cleanCpf = cpf.replace(/[^\d]/g, '');
  if (cleanCpf.length !== 11) return cpf;

  return cleanCpf.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4');
}

/**
 * Formats CEP with standard Brazilian formatting
 * @param cep - CEP string
 * @returns formatted CEP (XXXXX-XXX)
 */
export function formatCEP(cep: string): string {
  if (!cep) return '';

  const cleanCep = cep.replace(/[^\d]/g, '');
  if (cleanCep.length !== 8) return cep;

  return cleanCep.replace(/^(\d{5})(\d{3})$/, '$1-$2');
}

/**
 * Removes all formatting from a Brazilian document
 * @param document - Document string with formatting
 * @returns clean document string with only numbers
 */
export function cleanDocument(document: string): string {
  if (!document) return '';
  return document.replace(/[^\d]/g, '');
}

/**
 * Validates if a string is a valid Brazilian phone number
 * @param phone - Phone number string
 * @returns boolean indicating if phone format is valid
 */
export function validateBrazilianPhone(phone: string): boolean {
  if (!phone) return false;

  const cleanPhone = phone.replace(/[^\d]/g, '');

  // Brazilian phone formats:
  // Landline: 10 digits (XX XXXX-XXXX)
  // Mobile: 11 digits (XX 9XXXX-XXXX)
  return cleanPhone.length === 10 || cleanPhone.length === 11;
}

/**
 * Formats Brazilian phone number
 * @param phone - Phone number string
 * @returns formatted phone number
 */
export function formatBrazilianPhone(phone: string): string {
  if (!phone) return '';

  const cleanPhone = phone.replace(/[^\d]/g, '');

  if (cleanPhone.length === 10) {
    // Landline: (XX) XXXX-XXXX
    return cleanPhone.replace(/^(\d{2})(\d{4})(\d{4})$/, '($1) $2-$3');
  } else if (cleanPhone.length === 11) {
    // Mobile: (XX) 9XXXX-XXXX
    return cleanPhone.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3');
  }

  return phone;
}

/**
 * Validates Brazilian state code
 * @param state - 2-letter state code
 * @returns boolean indicating if state code is valid
 */
export function validateBrazilianState(state: string): boolean {
  const validStates = [
    'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO',
    'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI',
    'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
  ];

  return validStates.includes(state?.toUpperCase());
}

/**
 * Gets the full name of a Brazilian state from its code
 * @param stateCode - 2-letter state code
 * @returns full state name
 */
export function getBrazilianStateName(stateCode: string): string {
  const stateNames: Record<string, string> = {
    'AC': 'Acre',
    'AL': 'Alagoas',
    'AP': 'Amapá',
    'AM': 'Amazonas',
    'BA': 'Bahia',
    'CE': 'Ceará',
    'DF': 'Distrito Federal',
    'ES': 'Espírito Santo',
    'GO': 'Goiás',
    'MA': 'Maranhão',
    'MT': 'Mato Grosso',
    'MS': 'Mato Grosso do Sul',
    'MG': 'Minas Gerais',
    'PA': 'Pará',
    'PB': 'Paraíba',
    'PR': 'Paraná',
    'PE': 'Pernambuco',
    'PI': 'Piauí',
    'RJ': 'Rio de Janeiro',
    'RN': 'Rio Grande do Norte',
    'RS': 'Rio Grande do Sul',
    'RO': 'Rondônia',
    'RR': 'Roraima',
    'SC': 'Santa Catarina',
    'SP': 'São Paulo',
    'SE': 'Sergipe',
    'TO': 'Tocantins',
  };

  return stateNames[stateCode?.toUpperCase()] || stateCode;
}

/**
 * Address standardization and validation utilities
 */
export interface StandardizedAddress {
  zipCode: string;
  street: string;
  number?: string;
  complement?: string;
  district: string;
  city: string;
  state: string;
  country: string;
  ibgeCode?: string;
  isValid: boolean;
  errors: string[];
}

/**
 * Standardizes a Brazilian address
 * @param address - Raw address data
 * @returns standardized address with validation results
 */
export function standardizeBrazilianAddress(address: {
  zipCode?: string;
  street?: string;
  number?: string;
  complement?: string;
  district?: string;
  city?: string;
  state?: string;
  country?: string;
}): StandardizedAddress {
  const errors: string[] = [];
  const result: StandardizedAddress = {
    zipCode: '',
    street: '',
    number: address.number || '',
    complement: address.complement || '',
    district: '',
    city: '',
    state: '',
    country: 'BR',
    isValid: false,
    errors: [],
  };

  // Validate and format CEP
  if (!address.zipCode) {
    errors.push('CEP é obrigatório');
  } else if (!validateCEP(address.zipCode)) {
    errors.push('CEP inválido');
  } else {
    result.zipCode = formatCEP(address.zipCode);
  }

  // Validate street
  if (!address.street?.trim()) {
    errors.push('Logradouro é obrigatório');
  } else {
    result.street = address.street.trim().toUpperCase();
  }

  // Validate district
  if (!address.district?.trim()) {
    errors.push('Bairro é obrigatório');
  } else {
    result.district = address.district.trim().toUpperCase();
  }

  // Validate city
  if (!address.city?.trim()) {
    errors.push('Cidade é obrigatória');
  } else {
    result.city = address.city.trim().toUpperCase();
  }

  // Validate state
  if (!address.state) {
    errors.push('Estado é obrigatório');
  } else if (!validateBrazilianState(address.state)) {
    errors.push('Estado inválido');
  } else {
    result.state = address.state.toUpperCase();
  }

  // Standardize number
  if (address.number?.trim()) {
    result.number = address.number.trim();
  } else {
    result.number = 'S/N'; // Sem número
  }

  // Standardize complement
  if (address.complement?.trim()) {
    result.complement = address.complement.trim().toUpperCase();
  }

  result.errors = errors;
  result.isValid = errors.length === 0;

  return result;
}

/**
 * Tax calculation validation utilities
 */
export interface TaxCalculationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  totalTax: number;
}

/**
 * Validates tax calculations for Brazilian fiscal rules
 * @param params - Tax calculation parameters
 * @returns validation result with errors and warnings
 */
export function validateTaxCalculation(params: {
  baseValue: number;
  icmsRate?: number;
  pisRate?: number;
  cofinsRate?: number;
  ipiRate?: number;
  issRate?: number;
  icmsCst?: string;
  pisCst?: string;
  cofinsCst?: string;
  ipiCst?: string;
  cfop?: string;
  ncm?: string;
  emitterState?: string;
  clientState?: string;
}): TaxCalculationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let totalTax = 0;

  // Validate base value
  if (params.baseValue < 0) {
    errors.push('Valor base não pode ser negativo');
  }

  if (params.baseValue === 0) {
    warnings.push('Valor base é zero');
  }

  // Validate ICMS
  if (params.icmsRate !== undefined) {
    if (params.icmsRate < 0 || params.icmsRate > 25) {
      errors.push('Alíquota de ICMS deve estar entre 0% e 25%');
    } else {
      totalTax += (params.baseValue * params.icmsRate) / 100;
    }

    if (params.icmsCst && !isValidICMSCST(params.icmsCst)) {
      errors.push('CST de ICMS inválido');
    }
  }

  // Validate PIS
  if (params.pisRate !== undefined) {
    if (params.pisRate < 0 || params.pisRate > 10) {
      errors.push('Alíquota de PIS deve estar entre 0% e 10%');
    } else {
      totalTax += (params.baseValue * params.pisRate) / 100;
    }

    if (params.pisCst && !isValidPISCST(params.pisCst)) {
      errors.push('CST de PIS inválido');
    }
  }

  // Validate COFINS
  if (params.cofinsRate !== undefined) {
    if (params.cofinsRate < 0 || params.cofinsRate > 10) {
      errors.push('Alíquota de COFINS deve estar entre 0% e 10%');
    } else {
      totalTax += (params.baseValue * params.cofinsRate) / 100;
    }

    if (params.cofinsCst && !isValidCOFINSCST(params.cofinsCst)) {
      errors.push('CST de COFINS inválido');
    }
  }

  // Validate IPI
  if (params.ipiRate !== undefined) {
    if (params.ipiRate < 0 || params.ipiRate > 50) {
      errors.push('Alíquota de IPI deve estar entre 0% e 50%');
    } else {
      totalTax += (params.baseValue * params.ipiRate) / 100;
    }

    if (params.ipiCst && !isValidIPICST(params.ipiCst)) {
      errors.push('CST de IPI inválido');
    }
  }

  // Validate ISS (for services)
  if (params.issRate !== undefined) {
    if (params.issRate < 0 || params.issRate > 5) {
      errors.push('Alíquota de ISS deve estar entre 0% e 5%');
    } else {
      totalTax += (params.baseValue * params.issRate) / 100;
    }
  }

  // Validate CFOP
  if (params.cfop && !isValidCFOP(params.cfop)) {
    errors.push('CFOP inválido');
  }

  // Validate NCM
  if (params.ncm && !isValidNCM(params.ncm)) {
    errors.push('NCM inválido');
  }

  // Interstate operation checks
  if (params.emitterState && params.clientState && params.emitterState !== params.clientState) {
    warnings.push('Operação interestadual - verificar alíquotas específicas');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    totalTax: Math.round(totalTax * 100) / 100, // Round to 2 decimal places
  };
}

/**
 * Validates ICMS CST (Código de Situação Tributária)
 * @param cst - CST code
 * @returns boolean indicating if CST is valid
 */
export function isValidICMSCST(cst: string): boolean {
  const validCSTs = [
    '000', '010', '020', '030', '040', '041', '050', '051', '060', '070',
    '090', '101', '102', '103', '201', '202', '203', '300', '400', '500',
    '900'
  ];
  return validCSTs.includes(cst);
}

/**
 * Validates PIS CST
 * @param cst - CST code
 * @returns boolean indicating if CST is valid
 */
export function isValidPISCST(cst: string): boolean {
  const validCSTs = [
    '01', '02', '03', '04', '05', '06', '07', '08', '09', '49',
    '50', '51', '52', '53', '54', '55', '56', '60', '61', '62',
    '63', '64', '65', '66', '67', '70', '71', '72', '73', '74',
    '75', '98', '99'
  ];
  return validCSTs.includes(cst);
}

/**
 * Validates COFINS CST
 * @param cst - CST code
 * @returns boolean indicating if CST is valid
 */
export function isValidCOFINSCST(cst: string): boolean {
  const validCSTs = [
    '01', '02', '03', '04', '05', '06', '07', '08', '09', '49',
    '50', '51', '52', '53', '54', '55', '56', '60', '61', '62',
    '63', '64', '65', '66', '67', '70', '71', '72', '73', '74',
    '75', '98', '99'
  ];
  return validCSTs.includes(cst);
}

/**
 * Validates IPI CST
 * @param cst - CST code
 * @returns boolean indicating if CST is valid
 */
export function isValidIPICST(cst: string): boolean {
  const validCSTs = [
    '00', '01', '02', '03', '04', '05', '49', '50', '51', '52',
    '53', '54', '55', '99'
  ];
  return validCSTs.includes(cst);
}

/**
 * Validates CFOP (Código Fiscal de Operações e Prestações)
 * @param cfop - CFOP code
 * @returns boolean indicating if CFOP is valid
 */
export function isValidCFOP(cfop: string): boolean {
  if (!cfop || cfop.length !== 4 || !/^\d{4}$/.test(cfop)) {
    return false;
  }

  const firstDigit = cfop[0];
  const validFirstDigits = ['1', '2', '3', '5', '6', '7'];
  
  return validFirstDigits.includes(firstDigit);
}

/**
 * Validates NCM (Nomenclatura Comum do Mercosul)
 * @param ncm - NCM code
 * @returns boolean indicating if NCM is valid
 */
export function isValidNCM(ncm: string): boolean {
  if (!ncm) return false;
  
  const cleanNcm = ncm.replace(/[^\d]/g, '');
  return cleanNcm.length === 8 && /^\d{8}$/.test(cleanNcm);
}

/**
 * Calculates ICMS rate based on states
 * @param emitterState - Emitter state code
 * @param clientState - Client state code
 * @returns suggested ICMS rate
 */
export function getICMSRate(emitterState: string, clientState: string): number {
  // Same state operation
  if (emitterState === clientState) {
    // Default internal rate - varies by state, using 18% as common rate
    const internalRates: Record<string, number> = {
      'SP': 18,
      'RJ': 20,
      'MG': 18,
      'RS': 18,
      'PR': 18,
      'SC': 17,
      // Add more states as needed
    };
    return internalRates[emitterState] || 18;
  }

  // Interstate operation - standard rates
  const southeastStates = ['SP', 'RJ', 'MG', 'ES'];
  const southStates = ['RS', 'SC', 'PR'];
  const developedStates = [...southeastStates, ...southStates];

  // From developed to developed state
  if (developedStates.includes(emitterState) && developedStates.includes(clientState)) {
    return 12; // Standard interstate rate
  }

  // From developed to less developed state
  if (developedStates.includes(emitterState) && !developedStates.includes(clientState)) {
    return 7; // Reduced rate to incentivize development
  }

  // From less developed to developed state
  if (!developedStates.includes(emitterState) && developedStates.includes(clientState)) {
    return 12; // Standard interstate rate
  }

  // Between less developed states
  return 12; // Standard interstate rate
}

/**
 * Validates if tax configuration is consistent
 * @param params - Tax configuration parameters
 * @returns validation result
 */
export function validateTaxConfiguration(params: {
  icmsRate?: number;
  icmsCst?: string;
  pisRate?: number;
  pisCst?: string;
  cofinsRate?: number;
  cofinsCst?: string;
  ipiRate?: number;
  ipiCst?: string;
  cfop?: string;
  ncm?: string;
}): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check ICMS consistency
  if (params.icmsRate !== undefined && params.icmsCst) {
    // CST 040, 041, 050, 051 should have zero rate
    if (['040', '041', '050', '051'].includes(params.icmsCst) && params.icmsRate > 0) {
      errors.push('CST de ICMS indica isenção/não tributação, mas alíquota é maior que zero');
    }
    
    // CST 000, 010, 020, 070, 090 should have positive rate
    if (['000', '010', '020', '070', '090'].includes(params.icmsCst) && params.icmsRate === 0) {
      warnings.push('CST de ICMS indica tributação, mas alíquota é zero');
    }
  }

  // Check PIS/COFINS consistency
  if (params.pisRate !== undefined && params.cofinsRate !== undefined) {
    // PIS and COFINS usually have proportional rates
    const expectedCofinsRate = params.pisRate * 4.6; // Approximate proportion
    const tolerance = 0.5;
    
    if (Math.abs(params.cofinsRate - expectedCofinsRate) > tolerance) {
      warnings.push('Alíquotas de PIS e COFINS podem estar desproporcionais');
    }
  }

  // Check CST consistency between PIS and COFINS
  if (params.pisCst && params.cofinsCst) {
    if (params.pisCst !== params.cofinsCst) {
      warnings.push('CST de PIS e COFINS são diferentes - verificar se está correto');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
