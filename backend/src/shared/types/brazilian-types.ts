/**
 * Brazilian-specific type definitions
 * Defines types and interfaces for Brazilian business entities, tax calculations, and NF-e operations
 */

// Tax-related types
export enum TaxRegime {
  SIMPLES_NACIONAL = 'simples_nacional',
  LUCRO_PRESUMIDO = 'lucro_presumido',
  LUCRO_REAL = 'lucro_real',
  LUCRO_ARBITRADO = 'lucro_arbitrado',
}

export enum TaxType {
  ICMS = 'icms',
  PIS = 'pis',
  COFINS = 'cofins',
  ISS = 'iss',
  IR = 'ir',
  CSLL = 'csll',
  IPI = 'ipi',
}

// Document types
export enum DocumentType {
  CNPJ = 'cnpj',
  CPF = 'cpf',
  PASSPORT = 'passport',
  FOREIGN_ID = 'foreign_id',
}

export enum PersonType {
  JURIDICA = 'juridica', // Legal entity
  FISICA = 'fisica', // Individual
  ESTRANGEIRA = 'estrangeira', // Foreign entity
}

// Address types
export interface BrazilianAddress {
  zipCode: string; // CEP
  street: string;
  number?: string;
  complement?: string;
  district: string; // Bairro
  city: string;
  state: string; // 2-letter state code
  country: string; // ISO country code
  ibgeCode?: string; // IBGE municipality code
}

// Tax calculation types
export interface TaxCalculation {
  taxType: TaxType;
  rate: number; // Percentage (0-100)
  baseValue: number; // Value used for calculation
  taxValue: number; // Calculated tax amount
  cst?: string; // Código de Situação Tributária
  cfop?: string; // Código Fiscal de Operações e Prestações
}

export interface TotalTaxes {
  icms: TaxCalculation;
  pis: TaxCalculation;
  cofins: TaxCalculation;
  iss?: TaxCalculation;
  ir?: TaxCalculation;
  csll?: TaxCalculation;
  ipi?: TaxCalculation;
  totalTaxValue: number;
}

// NF-e specific types
export enum InvoiceType {
  ENTRADA = 0, // Entry (purchase)
  SAIDA = 1, // Exit (sale)
}

export enum InvoiceStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  SENT_TO_SEFAZ = 'sent_to_sefaz',
  AUTHORIZED = 'authorized',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  DENIED = 'denied',
}

export enum PaymentMethod {
  DINHEIRO = '01', // Cash
  CHEQUE = '02', // Check
  CARTAO_CREDITO = '03', // Credit card
  CARTAO_DEBITO = '04', // Debit card
  CREDITO_LOJA = '05', // Store credit
  VALE_ALIMENTACAO = '10', // Food voucher
  VALE_REFEICAO = '11', // Meal voucher
  VALE_PRESENTE = '12', // Gift voucher
  VALE_COMBUSTIVEL = '13', // Fuel voucher
  DUPLICATA_MERCANTIL = '14', // Commercial duplicate
  BOLETO_BANCARIO = '15', // Bank slip
  DEPOSITO_BANCARIO = '16', // Bank deposit
  PIX = '17', // PIX
  TRANSFERENCIA_BANCARIA = '18', // Bank transfer
  PROGRAMA_FIDELIDADE = '19', // Loyalty program
  SEM_PAGAMENTO = '90', // No payment
  OUTROS = '99', // Others
}

export enum PaymentTiming {
  A_VISTA = 0, // Cash payment
  A_PRAZO = 1, // Term payment
}

// Product and service types
export interface NCMInfo {
  code: string; // NCM code (8 digits)
  description: string;
  unit: string; // Unit of measurement
  tax?: {
    ipi?: number;
    pis?: number;
    cofins?: number;
  };
}

export interface CFOPInfo {
  code: string; // CFOP code (4 digits)
  description: string;
  type: 'entrada' | 'saida';
  interstate: boolean;
}

// SEFAZ integration types
export enum SefazEnvironment {
  HOMOLOGACAO = 'homologacao',
  PRODUCAO = 'producao',
}

export enum SefazService {
  AUTORIZACAO = 'autorizacao',
  CONSULTA_PROTOCOLO = 'consulta_protocolo',
  CONSULTA_STATUS = 'consulta_status',
  CANCELAMENTO = 'cancelamento',
  CARTA_CORRECAO = 'carta_correcao',
  INUTILIZACAO = 'inutilizacao',
  DISTRIBUICAO = 'distribuicao',
}

export interface SefazResponse {
  success: boolean;
  protocol?: string;
  statusCode: string;
  statusDescription: string;
  xmlResponse?: string;
  errors?: string[];
  warnings?: string[];
  processedAt: Date;
}

// Certificate types
export enum CertificateType {
  A1 = 'A1', // Software certificate (PKCS#12)
  A3 = 'A3', // Hardware certificate (smart card/token)
}

export interface CertificateInfo {
  type: CertificateType;
  subject: string;
  issuer: string;
  serialNumber: string;
  validFrom: Date;
  validTo: Date;
  cnpj?: string; // CNPJ associated with certificate
}

// Invoice event types
export enum InvoiceEventType {
  CREATED = 'created',
  APPROVED = 'approved',
  SENT = 'sent',
  AUTHORIZED = 'authorized',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  CORRECTION_LETTER = 'correction_letter',
  STATUS_CONSULTED = 'status_consulted',
}

// Audit and compliance types
export interface AuditAction {
  action: string;
  entityType: string;
  entityId: string;
  userId: string;
  accountId: string;
  businessUnitId?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  performedAt: Date;
}

// Brazilian business entity types
export interface BrazilianBusinessEntity {
  cnpj: string;
  legalName: string; // Razão Social
  tradeName?: string; // Nome Fantasia
  stateRegistration?: string; // Inscrição Estadual
  municipalRegistration?: string; // Inscrição Municipal
  taxRegime: TaxRegime;
  address: BrazilianAddress;
  email?: string;
  phone?: string;
}

// Invoice totals and summary
export interface InvoiceTotals {
  subtotal: number; // Sum of all items before taxes
  discountValue: number;
  totalBeforeTax: number; // Subtotal - discount
  totalTaxValue: number; // Sum of all taxes
  totalValue: number; // Total including taxes
  taxes: TotalTaxes;
}

// Payment information
export interface PaymentInfo {
  method: PaymentMethod;
  timing: PaymentTiming;
  installments?: PaymentInstallment[];
}

export interface PaymentInstallment {
  number: number;
  dueDate: Date;
  value: number;
}

// Transport information
export interface TransportInfo {
  carrierId?: string;
  carrierName?: string;
  carrierCnpj?: string;
  carrierAddress?: BrazilianAddress;
  freightType: 'emitente' | 'destinatario' | 'terceiros'; // Who pays freight
  freightValue?: number;
  insuranceValue?: number;
  additionalExpenses?: number;
  packages?: PackageInfo[];
}

export interface PackageInfo {
  quantity: number;
  type: string; // Type of package (box, pallet, etc.)
  brand?: string;
  numericalSequence?: string;
  grossWeight?: number;
  netWeight?: number;
}

// Error types for Brazilian operations
export interface BrazilianValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface SefazError {
  code: string;
  message: string;
  correction?: string;
}

// Common validation constraints
export const VALIDATION_PATTERNS = {
  CNPJ: /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/,
  CNPJ_NUMBERS_ONLY: /^\d{14}$/,
  CPF: /^\d{3}\.\d{3}\.\d{3}-\d{2}$/,
  CPF_NUMBERS_ONLY: /^\d{11}$/,
  CEP: /^\d{5}-\d{3}$/,
  CEP_NUMBERS_ONLY: /^\d{8}$/,
  BRAZILIAN_PHONE: /^(\(\d{2}\)\s?)?\d{4,5}-?\d{4}$/,
  NCM: /^\d{8}$/,
  CFOP: /^\d{4}$/,
  STATE_CODE: /^[A-Z]{2}$/,
} as const;

// Business rules constants
export const BUSINESS_RULES = {
  MAX_INVOICE_ITEMS: 990,
  MAX_INVOICE_VALUE: 999999999.99,
  MIN_INVOICE_VALUE: 0.01,
  CERTIFICATE_EXPIRY_WARNING_DAYS: 30,
  DEFAULT_INVOICE_SERIES: 1,
  SEFAZ_TIMEOUT_SECONDS: 30,
  MAX_CORRECTION_LETTERS: 20,
} as const;
