import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';

import { Public } from '../auth/decorators/public.decorator';
import { SkipTenantCheck } from '../../common/decorators/skip-tenant-check.decorator';
import { SkipAudit } from '../../common/decorators/skip-audit.decorator';

@ApiTags('Health')
@Controller('health')
@SkipTenantCheck()
@SkipAudit()
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
  ) {}

  @Get()
  @Public()
  @HealthCheck()
  @ApiOperation({
    summary: 'Complete health check',
    description: 'Check the health of all system components',
  })
  @ApiResponse({
    status: 200,
    description: 'Health check completed successfully',
  })
  @ApiResponse({
    status: 503,
    description: 'One or more health checks failed',
  })
  check() {
    return this.health.check([
      // Database health check
      () => this.db.pingCheck('database'),
      
      // Memory health check (heap should not exceed 300MB)
      () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),
      
      // RSS memory check (should not exceed 500MB)
      () => this.memory.checkRSS('memory_rss', 500 * 1024 * 1024),
      
      // Disk health check (90% threshold)
      () => this.disk.checkStorage('storage', {
        thresholdPercent: 0.9,
        path: '/',
      }),
    ]);
  }

  @Get('ready')
  @Public()
  @ApiOperation({
    summary: 'Readiness check',
    description: 'Check if the application is ready to receive traffic',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is ready',
  })
  @ApiResponse({
    status: 503,
    description: 'Application is not ready',
  })
  readiness() {
    return this.health.check([
      () => this.db.pingCheck('database'),
    ]);
  }

  @Get('live')
  @Public()
  @ApiOperation({
    summary: 'Liveness check',
    description: 'Check if the application is alive (basic health check)',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is alive',
  })
  liveness() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
