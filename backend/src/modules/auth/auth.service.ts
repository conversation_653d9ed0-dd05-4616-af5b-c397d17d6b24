import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

import {
  User,
  UserRole,
  UserStatus,
} from '../../database/entities/user.entity';
import { Account, AccountStatus } from '../../database/entities/account.entity';
import {
  BusinessUnit,
  BusinessUnitStatus,
} from '../../database/entities/business-unit.entity';
import { CompanyType } from '../../database/entities/enums';
import {
  Subscription,
  SubscriptionTier,
  SubscriptionStatus,
  BillingCycle,
} from '../../database/entities/subscription.entity';
import { JwtPayload } from './strategies/jwt.strategy';
import {
  LoginDto,
  RegisterDto,
  RefreshTokenDto,
  ForgotPasswordDto,
  ResetPasswordDto,
} from './dto/auth.dto';

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    isEmailVerified: boolean;
    account: {
      id: string;
      name: string;
      status: AccountStatus;
      trialEndsAt?: Date;
    };
    currentBusinessUnit?: {
      id: string;
      name: string;
      cnpj: string;
      status: BusinessUnitStatus;
      isDefault: boolean;
    };
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(BusinessUnit)
    private businessUnitRepository: Repository<BusinessUnit>,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
    private dataSource: DataSource,
  ) {}

  /**
   * User login with email and password
   */
  async login(loginDto: LoginDto, ipAddress?: string): Promise<AuthResponse> {
    const { email, password } = loginDto;

    // Find user with account and business unit relations
    const user = await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
      relations: ['account', 'currentBusinessUnit'],
    });

    if (!user) {
      // Emit security event for failed login attempt
      this.eventEmitter.emit('auth.login.failed', {
        email,
        ipAddress,
        reason: 'user_not_found',
      });

      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is locked
    if (user.isLocked) {
      this.eventEmitter.emit('auth.login.failed', {
        userId: user.id,
        email,
        ipAddress,
        reason: 'account_locked',
      });

      throw new UnauthorizedException(
        'Account is temporarily locked due to multiple failed login attempts',
      );
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      // Increment login attempts
      await this.incrementLoginAttempts(user);

      this.eventEmitter.emit('auth.login.failed', {
        userId: user.id,
        email,
        ipAddress,
        reason: 'invalid_password',
      });

      throw new UnauthorizedException('Invalid credentials');
    }

    // Check account status
    if (
      user.account.status === 'suspended' ||
      user.account.status === 'cancelled'
    ) {
      this.eventEmitter.emit('auth.login.failed', {
        userId: user.id,
        email,
        ipAddress,
        reason: 'account_inactive',
      });

      throw new UnauthorizedException(`Account is ${user.account.status}`);
    }

    // Check if trial has expired
    if (
      user.account.status === 'trial' &&
      user.account.trialEndsAt &&
      user.account.trialEndsAt < new Date()
    ) {
      this.eventEmitter.emit('auth.login.failed', {
        userId: user.id,
        email,
        ipAddress,
        reason: 'trial_expired',
      });

      throw new UnauthorizedException('Account trial has expired');
    }

    // Check if email verification is required
    const requireEmailVerification = this.configService.get(
      'auth.account.requireEmailVerification',
    );
    if (requireEmailVerification && !user.isEmailVerified) {
      throw new UnauthorizedException('Email verification required');
    }

    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await this.resetLoginAttempts(user);
    }

    // Update last login
    user.lastLoginAt = new Date();
    user.lastLoginIp = ipAddress;
    await this.userRepository.save(user);

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Emit successful login event
    this.eventEmitter.emit('auth.login.success', {
      userId: user.id,
      email: user.email,
      accountId: user.accountId,
      ipAddress,
    });

    return this.buildAuthResponse(user, tokens);
  }

  /**
   * User registration with account creation
   */
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const { email, password, firstName, lastName, companyName, cnpj } =
      registerDto;

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Start transaction
    return await this.dataSource.transaction(async (manager) => {
      // Create account
      const account = manager.create(Account, {
        name: companyName,
        cnpj,
        status: AccountStatus.TRIAL,
        trialEndsAt: new Date(
          Date.now() +
            this.configService.get('app.business.trialDurationDays', 14) *
              24 *
              60 *
              60 *
              1000,
        ),
      });

      const savedAccount = await manager.save(account);

      // Create free subscription
      const subscription = manager.create(Subscription, {
        accountId: savedAccount.id,
        tier: SubscriptionTier.FREE,
        status: SubscriptionStatus.TRIAL,
        billingCycle: BillingCycle.MONTHLY,
        currentPeriodStart: new Date(),
        currentPeriodEnd: savedAccount.trialEndsAt,
        trialStart: new Date(),
        trialEnd: savedAccount.trialEndsAt,
      });

      await manager.save(subscription);

      // Create default business unit
      let businessUnit: BusinessUnit | null = null;
      if (cnpj) {
        businessUnit = manager.create(BusinessUnit, {
          name: companyName,
          legalName: companyName,
          cnpj,
          status: BusinessUnitStatus.ACTIVE,
          isDefault: true,
          accountId: savedAccount.id,
          street: 'RUA EXEMPLO',
          number: 'S/N',
          district: 'CENTRO',
          city: 'SÃO PAULO',
          state: 'SP',
          zipCode: '01000-000',
        });

        businessUnit = await manager.save(businessUnit);
      }

      // Hash password
      const saltRounds = this.configService.get('auth.password.saltRounds', 12);
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = manager.create(User, {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        role: UserRole.OWNER, // First user is always owner
        status: this.configService.get('auth.account.requireEmailVerification')
          ? UserStatus.PENDING_VERIFICATION
          : UserStatus.ACTIVE,
        accountId: savedAccount.id,
        currentBusinessUnitId: businessUnit?.id,
        emailVerificationToken: this.configService.get(
          'auth.account.requireEmailVerification',
        )
          ? this.generateVerificationToken()
          : undefined,
      });

      const savedUser = await manager.save(user);

      // Load user with relations for response
      const userWithRelations = await manager.findOne(User, {
        where: { id: savedUser.id },
        relations: ['account', 'currentBusinessUnit'],
      });

      if (!userWithRelations) {
        throw new Error('Failed to load user with relations after creation');
      }

      // Generate tokens
      const tokens = await this.generateTokens(userWithRelations);

      // Emit registration event
      this.eventEmitter.emit('auth.user.registered', {
        userId: savedUser.id,
        email: savedUser.email,
        accountId: savedAccount.id,
        requiresEmailVerification: this.configService.get(
          'auth.account.requireEmailVerification',
        ),
        emailVerificationToken: savedUser.emailVerificationToken,
      });

      return this.buildAuthResponse(userWithRelations, tokens);
    });
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthResponse> {
    const { refreshToken } = refreshTokenDto;

    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('auth.jwt.refreshToken.secret'),
      }) as JwtPayload;

      // Find user and validate refresh token
      const user = await this.userRepository.findOne({
        where: {
          id: payload.sub,
          refreshToken,
        },
        relations: ['account', 'currentBusinessUnit'],
      });

      if (!user || !user.hasValidRefreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      return this.buildAuthResponse(user, tokens);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * User logout - invalidate refresh token
   */
  async logout(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      refreshToken: undefined,
      refreshTokenExpiresAt: undefined,
    });

    this.eventEmitter.emit('auth.user.logout', { userId });
  }

  /**
   * Request password reset
   */
  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    const { email } = forgotPasswordDto;

    const user = await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
    });

    if (!user) {
      // Don't reveal if email exists or not for security
      return;
    }

    // Generate password reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Save reset token
    await this.userRepository.update(user.id, {
      passwordResetToken: resetToken,
      passwordResetExpiresAt: resetTokenExpiry,
    });

    // Emit password reset event
    this.eventEmitter.emit('auth.password.reset.requested', {
      userId: user.id,
      email: user.email,
      resetToken,
    });
  }

  /**
   * Reset password using reset token
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { token, newPassword } = resetPasswordDto;

    const user = await this.userRepository.findOne({
      where: {
        passwordResetToken: token,
      },
    });

    if (
      user &&
      user.passwordResetExpiresAt &&
      user.passwordResetExpiresAt < new Date()
    ) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Validate password strength
    await this.validatePasswordStrength(newPassword);

    // Hash new password
    const saltRounds = this.configService.get('auth.password.saltRounds', 12);
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password and clear reset token
    await this.userRepository.update(user.id, {
      password: hashedPassword,
      passwordResetToken: undefined,
      passwordResetExpiresAt: undefined,
      loginAttempts: 0, // Reset login attempts
      lockedAt: undefined, // Unlock account if locked
    });

    // Emit password reset success event
    this.eventEmitter.emit('auth.password.reset.completed', {
      userId: user.id,
      email: user.email,
    });
  }

  /**
   * Verify email address
   */
  async verifyEmail(token: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      throw new BadRequestException('Invalid verification token');
    }

    // Update user as verified
    await this.userRepository.update(user.id, {
      emailVerifiedAt: new Date(),
      emailVerificationToken: undefined,
      status: UserStatus.ACTIVE, // Activate user after email verification
    });

    // Emit email verification event
    this.eventEmitter.emit('auth.email.verified', {
      userId: user.id,
      email: user.email,
    });
  }

  /**
   * Generate JWT tokens
   */
  private async generateTokens(
    user: User,
  ): Promise<{ accessToken: string; refreshToken: string; expiresIn: number }> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      accountId: user.accountId,
      currentBusinessUnitId: user.currentBusinessUnitId,
    };

    // Generate access token
    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get('auth.jwt.accessToken.secret'),
      expiresIn: this.configService.get('auth.jwt.accessToken.expiresIn'),
      algorithm: this.configService.get('auth.jwt.accessToken.algorithm'),
      audience: this.configService.get('auth.jwt.accessToken.audience'),
      issuer: this.configService.get('auth.jwt.accessToken.issuer'),
    });

    // Generate refresh token
    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('auth.jwt.refreshToken.secret'),
      expiresIn: this.configService.get('auth.jwt.refreshToken.expiresIn'),
      algorithm: this.configService.get('auth.jwt.refreshToken.algorithm'),
      audience: this.configService.get('auth.jwt.refreshToken.audience'),
      issuer: this.configService.get('auth.jwt.refreshToken.issuer'),
    });

    // Save refresh token to user
    const refreshTokenExpiry = new Date();
    refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 7); // 7 days

    await this.userRepository.update(user.id, {
      refreshToken,
      refreshTokenExpiresAt: refreshTokenExpiry,
    });

    // Get expiration time in seconds
    const expiresIn = this.parseTimeToSeconds(
      this.configService.get('auth.jwt.accessToken.expiresIn') || '15m',
    );

    return { accessToken, refreshToken, expiresIn };
  }

  /**
   * Build authentication response
   */
  private buildAuthResponse(
    user: User,
    tokens: { accessToken: string; refreshToken: string; expiresIn: number },
  ): AuthResponse {
    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
        account: {
          id: user.account.id,
          name: user.account.name,
          status: user.account.status,
          trialEndsAt: user.account.trialEndsAt,
        },
        currentBusinessUnit: user.currentBusinessUnit
          ? {
              id: user.currentBusinessUnit.id,
              name: user.currentBusinessUnit.name,
              cnpj: user.currentBusinessUnit.cnpj,
              status: user.currentBusinessUnit.status,
              isDefault: user.currentBusinessUnit.isDefault,
            }
          : undefined,
      },
      tokens,
    };
  }

  /**
   * Increment login attempts and lock account if necessary
   */
  private async incrementLoginAttempts(user: User): Promise<void> {
    const maxAttempts = this.configService.get(
      'auth.account.maxLoginAttempts',
      5,
    );
    const lockoutDuration = this.configService.get(
      'auth.account.lockoutDurationMinutes',
      15,
    );

    user.loginAttempts += 1;

    if (user.loginAttempts >= maxAttempts) {
      user.lockedAt = new Date(Date.now() + lockoutDuration * 60 * 1000);
    }

    await this.userRepository.save(user);
  }

  /**
   * Reset login attempts
   */
  private async resetLoginAttempts(user: User): Promise<void> {
    user.loginAttempts = 0;
    user.lockedAt = undefined;
    await this.userRepository.save(user);
  }

  /**
   * Generate email verification token
   */
  private generateVerificationToken(): string {
    const tokenLength = this.configService.get(
      'auth.account.emailVerificationTokenLength',
      32,
    );
    return crypto.randomBytes(tokenLength).toString('hex');
  }

  /**
   * Parse time string to seconds
   */
  private parseTimeToSeconds(timeString: string): number {
    const match = timeString.match(/^(\d+)([smhd])$/);
    if (!match) return 900; // Default 15 minutes

    const [, amount, unit] = match;
    const multipliers = { s: 1, m: 60, h: 3600, d: 86400 };
    return parseInt(amount) * (multipliers[unit] || 60);
  }

  /**
   * Validate password strength
   */
  private async validatePasswordStrength(password: string): Promise<void> {
    const config = this.configService.get('auth.password');

    if (password.length < config.minLength) {
      throw new BadRequestException(
        `Password must be at least ${config.minLength} characters long`,
      );
    }

    if (password.length > config.maxLength) {
      throw new BadRequestException(
        `Password must be no more than ${config.maxLength} characters long`,
      );
    }

    if (config.requireUppercase && !/[A-Z]/.test(password)) {
      throw new BadRequestException(
        'Password must contain at least one uppercase letter',
      );
    }

    if (config.requireLowercase && !/[a-z]/.test(password)) {
      throw new BadRequestException(
        'Password must contain at least one lowercase letter',
      );
    }

    if (config.requireNumbers && !/\d/.test(password)) {
      throw new BadRequestException(
        'Password must contain at least one number',
      );
    }

    if (
      config.requireSymbols &&
      !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    ) {
      throw new BadRequestException(
        'Password must contain at least one special character',
      );
    }
  }
}
