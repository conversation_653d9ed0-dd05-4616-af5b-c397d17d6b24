import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RefreshTokenCleanupTask } from '../../auth/tasks/refresh-token-cleanup.task';

// Entities
import { User } from '../../database/entities/user.entity';
import { Account } from '../../database/entities/account.entity';
import { BusinessUnit } from '../../database/entities/business-unit.entity';
import { Subscription } from '../../database/entities/subscription.entity';
import { RefreshToken } from '../../auth/entities/refresh-token.entity';

@Module({
  imports: [
    // Passport configuration
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // JWT configuration
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('auth.jwt.accessToken.secret'),
        signOptions: {
          expiresIn: configService.get('auth.jwt.accessToken.expiresIn'),
          algorithm: configService.get('auth.jwt.accessToken.algorithm'),
          audience: configService.get('auth.jwt.accessToken.audience'),
          issuer: configService.get('auth.jwt.accessToken.issuer'),
        },
      }),
    }),

    // Database entities
    TypeOrmModule.forFeature([
      User,
      Account,
      BusinessUnit,
      Subscription,
      RefreshToken,
    ]),
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, JwtAuthGuard, RefreshTokenCleanupTask],
  exports: [AuthService, JwtAuthGuard, JwtModule, PassportModule],
})
export class AuthModule {}
