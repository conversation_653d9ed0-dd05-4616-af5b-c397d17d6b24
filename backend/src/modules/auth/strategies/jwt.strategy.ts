import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { User } from '../../../database/entities/user.entity';

export interface JwtPayload {
  sub: string; // user ID
  email: string;
  role: string;
  accountId: string;
  currentBusinessUnitId?: string;
  iat?: number;
  exp?: number;
  aud?: string;
  iss?: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    configService: ConfigService,
  ) {
    const secret = configService.get('auth.jwt.accessToken.secret');
    if (!secret) {
      throw new Error('JWT secret is not configured');
    }
    
    const algorithm = configService.get('auth.jwt.accessToken.algorithm');
    const audience = configService.get('auth.jwt.accessToken.audience');
    const issuer = configService.get('auth.jwt.accessToken.issuer');
    
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
      algorithms: algorithm ? [algorithm] : undefined,
      audience: audience || undefined,
      issuer: issuer || undefined,
    });
  }

  async validate(payload: JwtPayload): Promise<User> {
    const { sub: userId } = payload;

    // Load user with necessary relationships
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: [
        'account', 
        'currentBusinessUnit'
      ],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if user is active and not locked
    if (!user.isActive) {
      throw new UnauthorizedException('User account is not active');
    }

    if (user.isLocked) {
      throw new UnauthorizedException('User account is temporarily locked');
    }

    // Validate account is active
    if (user.account && (user.account.status === 'suspended' || user.account.status === 'cancelled')) {
      throw new UnauthorizedException(`Account is ${user.account.status}`);
    }

    // Validate business unit if present
    if (user.currentBusinessUnit && user.currentBusinessUnit.status !== 'active') {
      throw new UnauthorizedException(`Current business unit is ${user.currentBusinessUnit.status}`);
    }

    // Check if account trial has expired
    if (user.account?.status === 'trial' && user.account.trialEndsAt && user.account.trialEndsAt < new Date()) {
      throw new UnauthorizedException('Account trial has expired');
    }

    return user;
  }
}
