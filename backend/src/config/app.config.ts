import { registerAs } from '@nestjs/config';

export const appConfig = registerAs('app', () => ({
  name: process.env.APP_NAME || 'EZ NFe SaaS',
  version: process.env.APP_VERSION || '1.0.0',
  environment: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),
  host: process.env.HOST || '0.0.0.0',
  
  // API Configuration
  globalPrefix: process.env.API_GLOBAL_PREFIX || 'api',
  apiVersion: process.env.API_VERSION || 'v1',
  
  // CORS Configuration
  cors: {
    enabled: process.env.CORS_ENABLED === 'true',
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : '*',
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-Id', 'X-Business-Unit-Id'],
    credentials: true,
  },
  
  // Rate Limiting
  rateLimit: {
    ttl: parseInt(process.env.RATE_LIMIT_TTL || '60', 10), // seconds
    limit: parseInt(process.env.RATE_LIMIT_MAX || '100', 10), // requests per ttl
  },
  
  // Security
  security: {
    bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS || '12', 10),
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5', 10),
    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000', 10), // 15 minutes
  },
  
  // File Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880', 10), // 5MB
    allowedFileTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || ['pdf', 'xml', 'jpg', 'jpeg', 'png'],
  },
  
  // Pagination
  pagination: {
    defaultLimit: parseInt(process.env.DEFAULT_PAGINATION_LIMIT || '20', 10),
    maxLimit: parseInt(process.env.MAX_PAGINATION_LIMIT || '100', 10),
  },
  
  // Feature Flags
  features: {
    swaggerEnabled: process.env.SWAGGER_ENABLED !== 'false',
    metricsEnabled: process.env.METRICS_ENABLED === 'true',
    healthCheckEnabled: process.env.HEALTH_CHECK_ENABLED !== 'false',
  },
  
  // Monitoring
  monitoring: {
    logLevel: process.env.LOG_LEVEL || 'info',
    logFormat: process.env.LOG_FORMAT || 'json',
    enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING === 'true',
  },
  
  // External Services URLs
  services: {
    receita_federal: {
      baseUrl: process.env.RECEITA_FEDERAL_BASE_URL || 'https://www.receitaws.com.br/v1',
      timeout: parseInt(process.env.RECEITA_FEDERAL_TIMEOUT || '10000', 10),
      rateLimitPerMinute: parseInt(process.env.RECEITA_FEDERAL_RATE_LIMIT || '3', 10),
    },
    viacep: {
      baseUrl: process.env.VIACEP_BASE_URL || 'https://viacep.com.br/ws',
      timeout: parseInt(process.env.VIACEP_TIMEOUT || '5000', 10),
      rateLimitPerMinute: parseInt(process.env.VIACEP_RATE_LIMIT || '60', 10),
    },
    sefaz: {
      homologation: process.env.SEFAZ_HOMOLOGATION_URL || 'https://hom.nfe.fazenda.gov.br',
      production: process.env.SEFAZ_PRODUCTION_URL || 'https://www.nfe.fazenda.gov.br',
      timeout: parseInt(process.env.SEFAZ_TIMEOUT || '30000', 10),
    },
  },
  
  // Business Rules
  business: {
    // Trial configuration
    trialDurationDays: parseInt(process.env.TRIAL_DURATION_DAYS || '14', 10),
    
    // Invoice numbering
    invoiceNumbering: {
      startFrom: parseInt(process.env.INVOICE_START_NUMBER || '1', 10),
      resetMonthly: process.env.INVOICE_RESET_MONTHLY === 'true',
    },
    
    // Certificate validation
    certificateExpirationWarningDays: parseInt(process.env.CERT_EXPIRATION_WARNING_DAYS || '30', 10),
    
    // Validation cache
    validationCache: {
      cnpjTtlMinutes: parseInt(process.env.CNPJ_CACHE_TTL_MINUTES || '1440', 10), // 24 hours
      cepTtlMinutes: parseInt(process.env.CEP_CACHE_TTL_MINUTES || '10080', 10), // 7 days
    },
  },
  
  // Subscription Tiers Configuration
  subscriptionTiers: {
    free: {
      invoicesPerMonth: 50,
      businessUnits: 1,
      users: 1,
      apiCallsPerDay: 100,
      storageGb: 1,
      supportLevel: 'community',
    },
    basic: {
      invoicesPerMonth: 500,
      businessUnits: 3,
      users: 5,
      apiCallsPerDay: 1000,
      storageGb: 10,
      supportLevel: 'email',
      price: 29.90,
    },
    professional: {
      invoicesPerMonth: 2000,
      businessUnits: 10,
      users: 25,
      apiCallsPerDay: 5000,
      storageGb: 50,
      supportLevel: 'priority',
      price: 99.90,
    },
    enterprise: {
      invoicesPerMonth: -1, // unlimited
      businessUnits: -1, // unlimited
      users: -1, // unlimited
      apiCallsPerDay: -1, // unlimited
      storageGb: -1, // unlimited
      supportLevel: 'dedicated',
      price: 299.90,
    },
  },
}));
