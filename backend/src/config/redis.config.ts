import { registerAs } from '@nestjs/config';

export const redisConfig = registerAs('redis', () => ({
  // Connection Configuration
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD,
  username: process.env.REDIS_USERNAME,
  
  // Database Selection
  db: parseInt(process.env.REDIS_DB || '0', 10), // Main cache database
  jobsDb: parseInt(process.env.REDIS_JOBS_DB || '1', 10), // Bull queue database
  sessionDb: parseInt(process.env.REDIS_SESSION_DB || '2', 10), // Session storage database
  
  // Connection Options
  connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || "10000", 10),
  commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || "5000", 10),
  lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true',
  keepAlive: parseInt(process.env.REDIS_KEEP_ALIVE || "30000", 10),
  
  // Retry Strategy
  retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY_ON_FAILOVER || "100", 10),
  enableReadyCheck: process.env.REDIS_ENABLE_READY_CHECK !== 'false',
  maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES_PER_REQUEST || "3", 10),
  retryDelayOnClusterDown: parseInt(process.env.REDIS_RETRY_DELAY_ON_CLUSTER_DOWN || "300", 10),
  
  // Cache Configuration
  cache: {
    // Default TTL for cache entries (in seconds)
    ttl: parseInt(process.env.REDIS_CACHE_TTL || "300", 10), // 5 minutes
    
    // Maximum number of items in cache
    max: parseInt(process.env.REDIS_CACHE_MAX || "1000", 10),
    
    // Cache key prefixes
    keyPrefix: process.env.REDIS_CACHE_KEY_PREFIX || 'eznfe:cache:',
    
    // Specific TTLs for different data types
    ttls: {
      user: parseInt(process.env.REDIS_USER_TTL || "900", 10), // 15 minutes
      session: parseInt(process.env.REDIS_SESSION_TTL || "1800", 10), // 30 minutes
      cnpjValidation: parseInt(process.env.REDIS_CNPJ_VALIDATION_TTL || "86400", 10), // 24 hours
      cepValidation: parseInt(process.env.REDIS_CEP_VALIDATION_TTL || "604800", 10), // 7 days
      taxCalculation: parseInt(process.env.REDIS_TAX_CALCULATION_TTL || "3600", 10), // 1 hour
      businessUnit: parseInt(process.env.REDIS_BUSINESS_UNIT_TTL || "1800", 10), // 30 minutes
      product: parseInt(process.env.REDIS_PRODUCT_TTL || "3600", 10), // 1 hour
      certificate: parseInt(process.env.REDIS_CERTIFICATE_TTL || "900", 10), // 15 minutes
    },
  },
  
  // Session Configuration
  session: {
    keyPrefix: process.env.REDIS_SESSION_KEY_PREFIX || 'eznfe:session:',
    ttl: parseInt(process.env.REDIS_SESSION_TTL || "1800", 10), // 30 minutes
    rolling: process.env.REDIS_SESSION_ROLLING !== 'false', // Extend TTL on access
    touchAfter: parseInt(process.env.REDIS_SESSION_TOUCH_AFTER || "300", 10), // 5 minutes
  },
  
  // Rate Limiting Configuration
  rateLimit: {
    keyPrefix: process.env.REDIS_RATE_LIMIT_KEY_PREFIX || 'eznfe:rateLimit:',
    windowMs: parseInt(process.env.REDIS_RATE_LIMIT_WINDOW || "60000", 10), // 1 minute
    maxRequests: parseInt(process.env.REDIS_RATE_LIMIT_MAX || "100", 10),
  },
  
  // Lock Configuration (for distributed locking)
  lock: {
    keyPrefix: process.env.REDIS_LOCK_KEY_PREFIX || 'eznfe:lock:',
    ttl: parseInt(process.env.REDIS_LOCK_TTL || "30000", 10), // 30 seconds
    retryDelay: parseInt(process.env.REDIS_LOCK_RETRY_DELAY || "100", 10), // milliseconds
    retryCount: parseInt(process.env.REDIS_LOCK_RETRY_COUNT || "10", 10),
  },
  
  // Pub/Sub Configuration
  pubsub: {
    keyPrefix: process.env.REDIS_PUBSUB_KEY_PREFIX || 'eznfe:pubsub:',
    channels: {
      invoiceEvents: process.env.REDIS_INVOICE_EVENTS_CHANNEL || 'invoice:events',
      userEvents: process.env.REDIS_USER_EVENTS_CHANNEL || 'user:events',
      systemEvents: process.env.REDIS_SYSTEM_EVENTS_CHANNEL || 'system:events',
      notifications: process.env.REDIS_NOTIFICATIONS_CHANNEL || 'notifications',
    },
  },
  
  // Bull Queue Configuration
  bull: {
    keyPrefix: process.env.REDIS_BULL_KEY_PREFIX || 'eznfe:bull:',
    defaultJobOptions: {
      attempts: parseInt(process.env.REDIS_BULL_DEFAULT_ATTEMPTS || "3", 10),
      backoff: {
        type: process.env.REDIS_BULL_BACKOFF_TYPE || 'exponential',
        delay: parseInt(process.env.REDIS_BULL_BACKOFF_DELAY || "2000", 10),
      },
      removeOnComplete: parseInt(process.env.REDIS_BULL_REMOVE_ON_COMPLETE || "100", 10),
      removeOnFail: parseInt(process.env.REDIS_BULL_REMOVE_ON_FAIL || "50", 10),
      ttl: parseInt(process.env.REDIS_BULL_TTL || "86400000", 10), // 24 hours
    },
    settings: {
      stalledInterval: parseInt(process.env.REDIS_BULL_STALLED_INTERVAL || "30000", 10),
      maxStalledCount: parseInt(process.env.REDIS_BULL_MAX_STALLED_COUNT || "3", 10),
    },
  },
  
  // Cluster Configuration (for Redis Cluster)
  cluster: {
    enabled: process.env.REDIS_CLUSTER_ENABLED === 'true',
    nodes: process.env.REDIS_CLUSTER_NODES 
      ? process.env.REDIS_CLUSTER_NODES.split(',').map(node => {
          const [host, port] = node.split(':');
          return { host, port: parseInt(port, 10) || 6379 };
        })
      : [],
    options: {
      enableReadyCheck: process.env.REDIS_CLUSTER_ENABLE_READY_CHECK !== 'false',
      redisOptions: {
        password: process.env.REDIS_PASSWORD,
        connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || "10000", 10),
        commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || "5000", 10),
      },
      clusterRetryDelayOnFailover: parseInt(process.env.REDIS_CLUSTER_RETRY_DELAY_ON_FAILOVER || "100", 10),
      clusterRetryDelayOnClusterDown: parseInt(process.env.REDIS_CLUSTER_RETRY_DELAY_ON_CLUSTER_DOWN || "300", 10),
      clusterMaxRedirections: parseInt(process.env.REDIS_CLUSTER_MAX_REDIRECTIONS || "16", 10),
      scaleReads: process.env.REDIS_CLUSTER_SCALE_READS || 'master',
    },
  },
  
  // Sentinel Configuration (for Redis Sentinel)
  sentinel: {
    enabled: process.env.REDIS_SENTINEL_ENABLED === 'true',
    sentinels: process.env.REDIS_SENTINEL_HOSTS
      ? process.env.REDIS_SENTINEL_HOSTS.split(',').map(host => {
          const [hostname, port] = host.split(':');
          return { host: hostname, port: parseInt(port, 10) || 26379 };
        })
      : [],
    name: process.env.REDIS_SENTINEL_NAME || 'mymaster',
    sentinelPassword: process.env.REDIS_SENTINEL_PASSWORD,
    role: process.env.REDIS_SENTINEL_ROLE || 'master',
    connectTimeout: parseInt(process.env.REDIS_SENTINEL_CONNECT_TIMEOUT || "60000", 10),
    lazyConnect: process.env.REDIS_SENTINEL_LAZY_CONNECT === 'true',
  },
  
  // SSL/TLS Configuration
  tls: process.env.REDIS_TLS_ENABLED === 'true' ? {
    rejectUnauthorized: process.env.REDIS_TLS_REJECT_UNAUTHORIZED !== 'false',
    ca: process.env.REDIS_TLS_CA,
    cert: process.env.REDIS_TLS_CERT,
    key: process.env.REDIS_TLS_KEY,
    servername: process.env.REDIS_TLS_SERVERNAME,
  } : undefined,
  
  // Monitoring and Metrics
  monitoring: {
    enabled: process.env.REDIS_MONITORING_ENABLED === 'true',
    metricsInterval: parseInt(process.env.REDIS_METRICS_INTERVAL || "30000", 10), // 30 seconds
    slowLogEnabled: process.env.REDIS_SLOW_LOG_ENABLED === 'true',
    slowLogThreshold: parseInt(process.env.REDIS_SLOW_LOG_THRESHOLD || "10000", 10), // 10ms
  },
  
  // Health Check Configuration
  healthCheck: {
    enabled: process.env.REDIS_HEALTH_CHECK_ENABLED !== 'false',
    interval: parseInt(process.env.REDIS_HEALTH_CHECK_INTERVAL || "30000", 10), // 30 seconds
    timeout: parseInt(process.env.REDIS_HEALTH_CHECK_TIMEOUT || "5000", 10), // 5 seconds
  },
}));
