import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as dotenv from 'dotenv';

dotenv.config();

export const typeOrmConfig: TypeOrmModuleOptions = {
  // Use SQLite (in-memory) for non-production (development, test), PostgreSQL in production
  type: process.env.NODE_ENV === 'production' ? 'postgres' : 'sqlite',
  ...(process.env.NODE_ENV === 'production'
    ? {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || 'postgres',
        database: process.env.DB_DATABASE || 'eznfe',
      }
    : {
        database: ':memory:',
      }),
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: true, // Always synchronize for now
  logging: process.env.NODE_ENV !== 'production',
  retryAttempts: 10,
  retryDelay: 3000,
  autoLoadEntities: true,
};
