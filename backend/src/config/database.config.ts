import { registerAs } from '@nestjs/config';

export const databaseConfig = registerAs('database', () => ({
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432', 10),
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || 'postgres',
  name: process.env.DATABASE_NAME || 'eznfe_saas',
  
  // Connection Pool Configuration
  maxConnections: parseInt(process.env.DATABASE_MAX_CONNECTIONS || '100', 10),
  connectionTimeout: parseInt(process.env.DATABASE_CONNECTION_TIMEOUT || '30000', 10),
  idleTimeout: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '30000', 10),
  
  // SSL Configuration
  ssl: process.env.DATABASE_SSL === 'true' ? {
    rejectUnauthorized: process.env.DATABASE_SSL_REJECT_UNAUTHORIZED !== 'false',
    ca: process.env.DATABASE_SSL_CA,
    cert: process.env.DATABASE_SSL_CERT,
    key: process.env.DATABASE_SSL_KEY,
  } : false,
  
  // Synchronization and Migrations
  synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
  migrationsRun: process.env.DATABASE_MIGRATIONS_RUN === 'true',
  
  // Logging
  logging: process.env.DATABASE_LOGGING === 'true' ? 'all' : false,
  logger: process.env.DATABASE_LOGGER || 'advanced-console',
  
  // Additional Options
  extra: {
    // Connection pool options
    max: parseInt(process.env.DATABASE_POOL_MAX || '100', 10),
    min: parseInt(process.env.DATABASE_POOL_MIN || '10', 10),
    acquireTimeoutMillis: parseInt(process.env.DATABASE_ACQUIRE_TIMEOUT || '60000', 10),
    createTimeoutMillis: parseInt(process.env.DATABASE_CREATE_TIMEOUT || '30000', 10),
    destroyTimeoutMillis: parseInt(process.env.DATABASE_DESTROY_TIMEOUT || '5000', 10),
    idleTimeoutMillis: parseInt(process.env.DATABASE_IDLE_TIMEOUT_MILLIS || '30000', 10),
    reapIntervalMillis: parseInt(process.env.DATABASE_REAP_INTERVAL || '1000', 10),
    createRetryIntervalMillis: parseInt(process.env.DATABASE_CREATE_RETRY_INTERVAL || '200', 10),
    propagateCreateError: process.env.DATABASE_PROPAGATE_CREATE_ERROR !== 'false',
    
    // PostgreSQL specific options
    statement_timeout: parseInt(process.env.DATABASE_STATEMENT_TIMEOUT || '30000', 10),
    query_timeout: parseInt(process.env.DATABASE_QUERY_TIMEOUT || '60000', 10),
    application_name: process.env.DATABASE_APPLICATION_NAME || 'eznfe-saas-api',
  },
  
  // Entities and Migrations paths (relative to dist folder)
  entities: ['dist/database/entities/**/*.entity.js'],
  migrations: ['dist/database/migrations/**/*.js'],
  subscribers: ['dist/database/subscribers/**/*.js'],
  
  // CLI configuration for migrations
  cli: {
    entitiesDir: 'src/database/entities',
    migrationsDir: 'src/database/migrations',
    subscribersDir: 'src/database/subscribers',
  },
  
  // Timezone configuration
  timezone: process.env.DATABASE_TIMEZONE || 'UTC',
  
  // Character encoding
  charset: process.env.DATABASE_CHARSET || 'utf8mb4',
  
  // Connection naming strategy
  namingStrategy: process.env.DATABASE_NAMING_STRATEGY || 'snake_case',
}));
