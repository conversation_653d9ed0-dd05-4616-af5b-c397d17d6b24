import { registerAs } from '@nestjs/config';

export const authConfig = registerAs('auth', () => ({
  // JWT Configuration
  jwt: {
    // Access Token
    accessToken: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
      expiresIn: process.env.JWT_EXPIRES_IN || '15m', // 15 minutes
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      audience: process.env.JWT_AUDIENCE || 'eznfe-saas-api',
      issuer: process.env.JWT_ISSUER || 'eznfe-saas',
    },
    
    // Refresh Token
    refreshToken: {
      secret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
      expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d', // 7 days
      algorithm: process.env.JWT_REFRESH_ALGORITHM || 'HS256',
      audience: process.env.JWT_REFRESH_AUDIENCE || 'eznfe-saas-api',
      issuer: process.env.JWT_REFRESH_ISSUER || 'eznfe-saas',
    },
    
    // Email Verification Token
    emailVerificationToken: {
      secret: process.env.JWT_EMAIL_VERIFICATION_SECRET || 'your-email-verification-secret',
      expiresIn: process.env.JWT_EMAIL_VERIFICATION_EXPIRES_IN || '24h', // 24 hours
    },
    
    // Password Reset Token
    passwordResetToken: {
      secret: process.env.JWT_PASSWORD_RESET_SECRET || 'your-password-reset-secret',
      expiresIn: process.env.JWT_PASSWORD_RESET_EXPIRES_IN || '1h', // 1 hour
    },
  },
  
  // Password Security
  password: {
    // Bcrypt salt rounds
    saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS || "12", 10),
    
    // Password requirements
    minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || "8", 10),
    maxLength: parseInt(process.env.PASSWORD_MAX_LENGTH || "128", 10),
    requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE !== 'false',
    requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE !== 'false',
    requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS !== 'false',
    requireSymbols: process.env.PASSWORD_REQUIRE_SYMBOLS !== 'false',
    
    // Password history
    preventReuse: parseInt(process.env.PASSWORD_PREVENT_REUSE || "5", 10), // Last 5 passwords
    
    // Password expiration
    expirationDays: parseInt(process.env.PASSWORD_EXPIRATION_DAYS || "0", 10), // 0 = never expires
    warningDays: parseInt(process.env.PASSWORD_WARNING_DAYS || "7", 10), // Warn 7 days before expiration
  },
  
  // Account Security
  account: {
    // Login attempts
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || "5", 10),
    lockoutDurationMinutes: parseInt(process.env.LOCKOUT_DURATION_MINUTES || "15", 10),
    lockoutProgressiveMultiplier: parseFloat(process.env.LOCKOUT_PROGRESSIVE_MULTIPLIER || "2"),
    maxLockoutDurationMinutes: parseInt(process.env.MAX_LOCKOUT_DURATION_MINUTES || "60 * 24", 10), // 24 hours
    
    // Session management
    maxActiveSessions: parseInt(process.env.MAX_ACTIVE_SESSIONS || "5", 10),
    sessionTimeoutMinutes: parseInt(process.env.SESSION_TIMEOUT_MINUTES || "30", 10),
    
    // Email verification
    requireEmailVerification: process.env.REQUIRE_EMAIL_VERIFICATION !== 'false',
    emailVerificationTokenLength: parseInt(process.env.EMAIL_VERIFICATION_TOKEN_LENGTH || "32", 10),
    
    // Two-factor authentication
    twoFactorEnabled: process.env.TWO_FACTOR_ENABLED === 'true',
    twoFactorIssuer: process.env.TWO_FACTOR_ISSUER || 'EZ NFe SaaS',
    twoFactorWindow: parseInt(process.env.TWO_FACTOR_WINDOW || "1", 10), // Time window for TOTP
  },
  
  // OAuth Configuration (for future implementation)
  oauth: {
    google: {
      enabled: process.env.GOOGLE_OAUTH_ENABLED === 'true',
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackUrl: process.env.GOOGLE_CALLBACK_URL || '/auth/google/callback',
    },
    microsoft: {
      enabled: process.env.MICROSOFT_OAUTH_ENABLED === 'true',
      clientId: process.env.MICROSOFT_CLIENT_ID,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
      callbackUrl: process.env.MICROSOFT_CALLBACK_URL || '/auth/microsoft/callback',
    },
  },
  
  // Security Headers
  security: {
    // CSRF Protection
    csrf: {
      enabled: process.env.CSRF_ENABLED === 'true',
      cookieName: process.env.CSRF_COOKIE_NAME || '_csrf',
      secret: process.env.CSRF_SECRET || 'your-csrf-secret',
    },
    
    // Rate Limiting
    rateLimit: {
      windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || "15 * 60 * 1000", 10), // 15 minutes
      max: parseInt(process.env.AUTH_RATE_LIMIT_MAX || "5", 10), // 5 attempts per window
      message: process.env.AUTH_RATE_LIMIT_MESSAGE || 'Too many authentication attempts',
    },
    
    // IP Whitelist/Blacklist
    ipWhitelist: process.env.AUTH_IP_WHITELIST ? process.env.AUTH_IP_WHITELIST.split(',') : [],
    ipBlacklist: process.env.AUTH_IP_BLACKLIST ? process.env.AUTH_IP_BLACKLIST.split(',') : [],
  },
  
  // Cookie Configuration
  cookies: {
    // Refresh token cookie
    refreshToken: {
      name: process.env.REFRESH_TOKEN_COOKIE_NAME || 'refresh_token',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
      domain: process.env.COOKIE_DOMAIN,
    },
    
    // Session cookie
    session: {
      name: process.env.SESSION_COOKIE_NAME || 'session',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
      domain: process.env.COOKIE_DOMAIN,
    },
  },
  
  // API Key Authentication
  apiKey: {
    headerName: process.env.API_KEY_HEADER_NAME || 'X-API-Key',
    enabled: process.env.API_KEY_AUTH_ENABLED !== 'false',
    keyFormat: process.env.API_KEY_FORMAT || 'ezn_', // Prefix for API keys
    keyLength: parseInt(process.env.API_KEY_LENGTH || "32", 10),
  },
  
  // Multi-tenancy
  multiTenant: {
    headerName: process.env.TENANT_HEADER_NAME || 'X-Tenant-Id',
    businessUnitHeaderName: process.env.BUSINESS_UNIT_HEADER_NAME || 'X-Business-Unit-Id',
    required: process.env.TENANT_REQUIRED !== 'false',
    allowTenantSwitching: process.env.ALLOW_TENANT_SWITCHING !== 'false',
  },
  
  // External Authentication Services
  external: {
    // Certificate validation (for A3 certificates)
    certificateValidation: {
      enabled: process.env.CERT_VALIDATION_ENABLED === 'true',
      timeout: parseInt(process.env.CERT_VALIDATION_TIMEOUT || "10000", 10),
      retryAttempts: parseInt(process.env.CERT_VALIDATION_RETRY_ATTEMPTS || "3", 10),
    },
  },
}));
