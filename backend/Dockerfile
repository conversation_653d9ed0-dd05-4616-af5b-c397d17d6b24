FROM node:18-alpine AS builder

WORKDIR /app

## Install build dependencies for native modules (e.g., bcrypt)
RUN apk add --no-cache python3 make g++

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove development dependencies
RUN npm prune --production

# Production image
FROM node:18-alpine

WORKDIR /app

# Install netcat for the entrypoint script and other dependencies
RUN apk add --no-cache netcat-openbsd curl

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/src/crypto-polyfill.js ./dist/crypto-polyfill.js

# Copy entrypoint script
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Create storage and certificates directories
RUN mkdir -p ./storage ./certificates

# Expose the port the app runs on
EXPOSE 3000

# Set entrypoint
ENTRYPOINT ["./docker-entrypoint.sh"]

# Command to run the application
CMD ["node", "dist/main"]
