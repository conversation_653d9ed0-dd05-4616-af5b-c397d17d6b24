# API Design Standards

## Overview

This document defines the API design standards for the ez-NFe backend. All endpoints must follow these conventions to ensure consistency, maintainability, and developer experience.

## RESTful API Conventions

### 1. Resource Naming
- Use plural nouns for resource collections: `/users`, `/invoices`, `/products`
- Use singular nouns for single resources: `/users/{id}`, `/invoices/{id}`
- Use kebab-case for multi-word resources: `/payment-terms`, `/api-keys`
- Avoid verbs in URLs - use HTTP methods instead

### 1.1. Address Management Endpoints
```
GET    /clients/{id}/addresses           - Get all addresses for client
POST   /clients/{id}/addresses           - Add new address to client
GET    /clients/{id}/addresses/{type}    - Get address by type (primary, billing, etc.)
PUT    /clients/{id}/addresses/{type}    - Update address by type
DELETE /clients/{id}/addresses/{addressId} - Remove specific address

GET    /emitters/{id}/addresses          - Get all addresses for emitter
POST   /emitters/{id}/addresses          - Add new address to emitter
GET    /emitters/{id}/addresses/{type}   - Get address by type
PUT    /emitters/{id}/addresses/{type}   - Update address by type
DELETE /emitters/{id}/addresses/{addressId} - Remove specific address

GET    /carriers/{id}/addresses          - Get all addresses for carrier
POST   /carriers/{id}/addresses          - Add new address to carrier
```

### 1.2. Product Tax Management Endpoints
```
GET    /products/{id}/taxes              - Get all tax configurations for product
POST   /products/{id}/taxes              - Add new tax configuration to product
GET    /products/{id}/taxes/{taxType}    - Get specific tax type configuration
PUT    /products/{id}/taxes/{taxId}      - Update specific tax configuration
DELETE /products/{id}/taxes/{taxId}      - Remove tax configuration

GET    /products/{id}/taxes/current      - Get current active tax configurations
POST   /products/{id}/taxes/calculate    - Calculate taxes for given amount
POST   /products/{id}/taxes/bulk-update  - Bulk update tax rates (admin only)
```

### 2. HTTP Methods
```
GET    /resources       - List all resources (with pagination)
GET    /resources/{id}  - Get a specific resource
POST   /resources       - Create a new resource
PUT    /resources/{id}  - Replace entire resource
PATCH  /resources/{id}  - Partial update of resource
DELETE /resources/{id}  - Delete a resource
```

### 3. Nested Resources
```
GET    /emitters/{id}/products     - Get products for specific emitter
POST   /emitters/{id}/products     - Create product for specific emitter
GET    /invoices/{id}/items        - Get items for specific invoice
POST   /invoices/{id}/cancel       - Cancel specific invoice (action)
```

## URL Structure

### 1. Base URL Pattern
```
https://api.eznfe.com/v1/{resource}
```

### 2. Versioning
- Use URL versioning: `/v1/`, `/v2/`
- Maintain backward compatibility for at least 2 versions
- Deprecation notices should be provided 6 months in advance

### 3. Query Parameters
```
GET /invoices?page=1&limit=20&status=authorized&sort=createdAt:desc
GET /products?search=notebook&category=electronics&active=true
GET /clients?documentType=cnpj&state=SP
```

## Request/Response Format

### 1. Content Type
- Always use `application/json` for request/response bodies
- Support `multipart/form-data` only for file uploads
- Include proper `Content-Type` headers

### 2. Request Body Structure
```typescript
// Create Invoice Request
{
  "clientId": "uuid-string",
  "paymentTermId": "uuid-string",
  "items": [
    {
      "productId": "uuid-string",
      "quantity": 2,
      "unitPrice": 100.50,
      "discount": 0
    }
  ],
  "observations": "Additional notes"
}
```

### 3. Response Body Structure
```typescript
// Success Response
{
  "success": true,
  "data": {
    "id": "uuid-string",
    "number": 1001,
    "status": "draft",
    "totalAmount": 201.00,
    "createdAt": "2025-01-08T10:00:00Z"
  },
  "meta": {
    "timestamp": "2025-01-08T10:00:00Z"
  }
}

// Error Response
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "clientId",
        "message": "Client ID is required"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-01-08T10:00:00Z"
  }
}
```

### 4. Pagination Response
```typescript
{
  "success": true,
  "data": [
    // ... array of resources
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    },
    "timestamp": "2025-01-08T10:00:00Z"
  }
}
```

## HTTP Status Codes

### 1. Success Codes
- `200 OK` - Successful GET, PUT, PATCH requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests

### 2. Client Error Codes
- `400 Bad Request` - Invalid request format or data
- `401 Unauthorized` - Missing or invalid authentication
- `403 Forbidden` - Valid auth but insufficient permissions
- `404 Not Found` - Resource doesn't exist
- `409 Conflict` - Resource conflict (duplicate, constraint violation)
- `422 Unprocessable Entity` - Valid format but business logic validation failed
- `429 Too Many Requests` - Rate limit exceeded

### 3. Server Error Codes
- `500 Internal Server Error` - Unexpected server error
- `502 Bad Gateway` - External service error
- `503 Service Unavailable` - Temporary service unavailability

## Error Handling

### 1. Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;           // Machine-readable error code
    message: string;        // Human-readable error message
    details?: any;          // Additional error details
    field?: string;         // Field name for validation errors
    timestamp?: string;     // Error timestamp
  };
  meta: {
    timestamp: string;
    requestId?: string;     // For tracking purposes
  };
}
```

### 2. Error Codes
```typescript
enum ErrorCodes {
  // Authentication & Authorization
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  
  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_FORMAT = 'INVALID_FORMAT',
  REQUIRED_FIELD_MISSING = 'REQUIRED_FIELD_MISSING',
  
  // Business Logic
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  INVALID_OPERATION = 'INVALID_OPERATION',
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  
  // External Services
  SEFAZ_ERROR = 'SEFAZ_ERROR',
  CERTIFICATE_ERROR = 'CERTIFICATE_ERROR',
  
  // System
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}
```

## Authentication & Authorization

### 1. JWT Bearer Token
```
Authorization: Bearer <jwt-token>
```

### 2. API Key Authentication
```
X-API-Key: <api-key>
```

### 3. Token Response Format
```typescript
{
  "success": true,
  "data": {
    "accessToken": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "expiresIn": 900,
    "tokenType": "Bearer",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "currentEmitter": {
        "id": "uuid",
        "name": "Company Name",
        "cnpj": "12345678000199"
      }
    }
  }
}
```

## Filtering and Sorting

### 1. Query Parameters
```
GET /invoices?status=authorized&clientId=uuid&dateFrom=2025-01-01&dateTo=2025-01-31
GET /products?search=notebook&category=electronics&priceMin=100&priceMax=1000
GET /clients?documentType=cnpj&state=SP&city=São Paulo
```

### 2. Sorting
```
GET /invoices?sort=createdAt:desc
GET /products?sort=name:asc,price:desc
```

### 3. Field Selection
```
GET /invoices?fields=id,number,status,totalAmount,createdAt
```

## Pagination

### 1. Query Parameters
```
GET /resources?page=1&limit=20
```

### 2. Default Values
- Default page: 1
- Default limit: 20
- Maximum limit: 100

### 3. Response Headers
```
X-Total-Count: 150
X-Page: 1
X-Per-Page: 20
X-Total-Pages: 8
```

## File Handling

### 1. File Upload
```typescript
POST /certificates
Content-Type: multipart/form-data

{
  "file": <binary-data>,
  "password": "certificate-password",
  "description": "Production certificate"
}
```

### 2. File Download
```typescript
GET /invoices/{id}/xml
Response:
Content-Type: application/xml
Content-Disposition: attachment; filename="invoice-1001.xml"
```

### 3. Presigned URLs
```typescript
GET /invoices/{id}/xml/url
Response:
{
  "success": true,
  "data": {
    "url": "https://storage.eznfe.com/signed-url",
    "expiresAt": "2025-01-08T11:00:00Z"
  }
}
```

## Rate Limiting

### 1. Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1641648000
```

### 2. Rate Limit Tiers
```typescript
enum RateLimitTiers {
  FREE = 100,      // requests per hour
  BASIC = 1000,    // requests per hour
  PREMIUM = 5000,  // requests per hour
  ENTERPRISE = 10000 // requests per hour
}
```

## Webhook Standards

### 1. Webhook Payload
```typescript
{
  "event": "invoice.authorized",
  "data": {
    "id": "uuid",
    "number": 1001,
    "status": "authorized",
    "protocol": "135240000000001",
    "authorizedAt": "2025-01-08T10:00:00Z"
  },
  "timestamp": "2025-01-08T10:00:00Z",
  "signature": "sha256=hash"
}
```

### 2. Webhook Headers
```
Content-Type: application/json
X-Webhook-Event: invoice.authorized
X-Webhook-Signature: sha256=hash
X-Webhook-Timestamp: 1641648000
```

## OpenAPI Documentation

### 1. Swagger Decorators
```typescript
@ApiTags('Invoices')
@ApiOperation({ summary: 'Create a new invoice' })
@ApiResponse({ status: 201, description: 'Invoice created successfully' })
@ApiResponse({ status: 400, description: 'Invalid request data' })
@ApiBearerAuth()
@Post()
async create(@Body() createInvoiceDto: CreateInvoiceDto) {
  // Implementation
}
```

### 2. DTO Documentation
```typescript
export class CreateInvoiceDto {
  @ApiProperty({ description: 'Client UUID', example: 'uuid-string' })
  @IsUUID()
  clientId: string;

  @ApiProperty({ description: 'Invoice items', type: [CreateInvoiceItemDto] })
  @ValidateNested({ each: true })
  @Type(() => CreateInvoiceItemDto)
  items: CreateInvoiceItemDto[];
}
```

## Validation Standards

### 1. DTO Validation
```typescript
export class CreateAddressDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Street name' })
  street: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Street number' })
  number: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Address complement', required: false })
  complement?: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Neighborhood' })
  neighborhood: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'City name' })
  city: string;

  @IsString()
  @Length(2, 2)
  @ApiProperty({ description: 'State code (2 letters)', example: 'SP' })
  state: string;

  @IsString()
  @Matches(/^\d{8}$/, { message: 'ZIP code must be 8 digits' })
  @ApiProperty({ description: 'ZIP code (8 digits)', example: '01234567' })
  zipCode: string;

  @IsOptional()
  @IsString()
  @Length(2, 2)
  @ApiProperty({ description: 'Country code', example: 'BR', default: 'BR' })
  country?: string;
}

export class CreateClientDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Client name' })
  name: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Legal name (razão social)', required: false })
  legalName?: string;

  @IsEnum(DocumentType)
  @ApiProperty({ enum: DocumentType })
  documentType: DocumentType;

  @IsString()
  @Matches(/^\d{11}$|^\d{14}$/, { message: 'Invalid CPF/CNPJ format' })
  @ApiProperty({ description: 'CPF (11 digits) or CNPJ (14 digits)' })
  federalTaxId: string;

  @IsEmail()
  @ApiProperty({ description: 'Valid email address' })
  email: string;

  @ValidateNested()
  @Type(() => CreateAddressDto)
  @ApiProperty({ description: 'Primary address', type: CreateAddressDto })
  primaryAddress: CreateAddressDto;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateClientAddressDto)
  @ApiProperty({
    description: 'Additional addresses',
    type: [CreateClientAddressDto],
    required: false
  })
  additionalAddresses?: CreateClientAddressDto[];
}

export class CreateClientAddressDto {
  @ValidateNested()
  @Type(() => CreateAddressDto)
  @ApiProperty({ description: 'Address details', type: CreateAddressDto })
  address: CreateAddressDto;

  @IsEnum(AddressType)
  @ApiProperty({
    enum: AddressType,
    description: 'Type of address',
    example: AddressType.BILLING
  })
  addressType: AddressType;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'Set as default for this address type',
    default: false
  })
  isDefault?: boolean;
}

export class CreateProductTaxDto {
  @IsEnum(TaxType)
  @ApiProperty({
    enum: TaxType,
    description: 'Type of tax',
    example: TaxType.ICMS
  })
  taxType: TaxType;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'CST code for tax situation',
    example: '00',
    required: false
  })
  cstCode?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'CSOSN code for Simples Nacional',
    example: '101',
    required: false
  })
  csosnCode?: string;

  @IsNumber()
  @Min(0)
  @Max(100)
  @ApiProperty({
    description: 'Tax rate percentage',
    example: 18.00,
    minimum: 0,
    maximum: 100
  })
  rate: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @ApiProperty({
    description: 'Fixed tax amount',
    example: 0,
    default: 0
  })
  fixedAmount?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(100)
  @ApiProperty({
    description: 'Percentage of product value to use as calculation base',
    example: 100,
    default: 100
  })
  calculationBase?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @ApiProperty({
    description: 'Base reduction percentage',
    example: 0,
    default: 0
  })
  reductionPercentage?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @ApiProperty({
    description: 'Additional tax percentage',
    example: 0,
    default: 0
  })
  additionalPercentage?: number;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'Whether this tax is exempt',
    default: false
  })
  isExempt?: boolean;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Reason for tax exemption',
    required: false
  })
  exemptionReason?: string;

  @IsOptional()
  @IsDateString()
  @ApiProperty({
    description: 'Date from which this tax configuration is valid',
    type: 'string',
    format: 'date',
    required: false
  })
  validFrom?: Date;

  @IsOptional()
  @IsDateString()
  @ApiProperty({
    description: 'Date until which this tax configuration is valid',
    type: 'string',
    format: 'date',
    required: false
  })
  validTo?: Date;
}

export class CreateProductDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Internal product code' })
  code: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Product name' })
  name: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Product description', required: false })
  description?: string;

  @IsString()
  @Matches(/^\d{8}$/, { message: 'NCM code must be 8 digits' })
  @ApiProperty({ description: 'NCM code (8 digits)', example: '12345678' })
  ncmCode: string;

  @IsString()
  @Matches(/^\d{4}$/, { message: 'CFOP must be 4 digits' })
  @ApiProperty({ description: 'CFOP code (4 digits)', example: '5102' })
  cfop: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Unit of measurement', example: 'UN' })
  unit: string;

  @IsNumber()
  @Min(0)
  @ApiProperty({ description: 'Unit price', example: 100.50 })
  unitPrice: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(8)
  @ApiProperty({
    description: 'ICMS origin code (0-8)',
    example: 0,
    default: 0
  })
  icmsOrigin?: number;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateProductTaxDto)
  @ApiProperty({
    description: 'Tax configurations for this product',
    type: [CreateProductTaxDto],
    required: false
  })
  taxes?: CreateProductTaxDto[];
}

export class CreateInstallmentDto {
  @IsInt()
  @Min(1)
  @ApiProperty({ description: 'Installment number', example: 1 })
  installmentNumber: number;

  @IsInt()
  @Min(0)
  @ApiProperty({ description: 'Days from invoice date to due date', example: 30 })
  daysToDue: number;

  @IsNumber()
  @Min(0.01)
  @Max(100)
  @ApiProperty({ description: 'Percentage of total amount', example: 50.00 })
  percentage: number;

  @IsOptional()
  @IsEnum(PaymentMethod)
  @ApiProperty({
    enum: PaymentMethod,
    description: 'Payment method for this installment',
    required: false
  })
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Installment description', required: false })
  description?: string;
}

export class CreatePaymentTermDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Payment term name', example: 'À Vista' })
  name: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Payment term description', required: false })
  description?: string;

  @IsEnum(PaymentTermType)
  @ApiProperty({
    enum: PaymentTermType,
    description: 'Type of payment term',
    example: PaymentTermType.CASH
  })
  termType: PaymentTermType;

  @IsEnum(PaymentMethod)
  @ApiProperty({
    enum: PaymentMethod,
    description: 'Default payment method',
    example: PaymentMethod.PIX
  })
  defaultPaymentMethod: PaymentMethod;

  @IsOptional()
  @IsInt()
  @Min(1)
  @ApiProperty({
    description: 'Total number of installments (for simple configuration)',
    example: 1,
    default: 1
  })
  totalInstallments?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  @ApiProperty({
    description: 'Days between installments',
    example: 30,
    default: 30
  })
  daysBetweenInstallments?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  @ApiProperty({
    description: 'Days to first installment',
    example: 0,
    default: 0
  })
  firstInstallmentDays?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @ApiProperty({
    description: 'Cash discount percentage',
    example: 2.5,
    default: 0
  })
  cashDiscountPercentage?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  @ApiProperty({
    description: 'Days eligible for cash discount',
    example: 7,
    default: 0
  })
  cashDiscountDays?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @ApiProperty({
    description: 'Late fee percentage',
    example: 2.0,
    default: 0
  })
  lateFeePercentage?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @ApiProperty({
    description: 'Monthly interest rate',
    example: 1.0,
    default: 0
  })
  interestRateMonthly?: number;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateInstallmentDto)
  @ApiProperty({
    description: 'Custom installment configuration (overrides simple configuration)',
    type: [CreateInstallmentDto],
    required: false
  })
  customInstallments?: CreateInstallmentDto[];
}
```

### 2. Custom Validators
```typescript
@ValidatorConstraint({ name: 'isCnpj', async: false })
export class IsCnpjConstraint implements ValidatorConstraintInterface {
  validate(cnpj: string) {
    return this.isValidCnpj(cnpj);
  }

  defaultMessage() {
    return 'Invalid CNPJ format';
  }

  private isValidCnpj(cnpj: string): boolean {
    // CNPJ validation logic
    return true;
  }
}

export function IsCnpj(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsCnpjConstraint,
    });
  };
}
```

## Testing Standards

### 1. API Testing
```typescript
describe('InvoiceController (e2e)', () => {
  it('POST /invoices should create invoice', async () => {
    const createInvoiceDto = {
      clientId: 'uuid',
      items: [{ productId: 'uuid', quantity: 1, unitPrice: 100 }]
    };

    const response = await request(app.getHttpServer())
      .post('/invoices')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(createInvoiceDto)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.id).toBeDefined();
  });
});
```

### 2. Mock External Services
```typescript
const mockHermesService = {
  transmitInvoice: jest.fn().mockResolvedValue({
    success: true,
    protocol: '135240000000001'
  })
};
```

This API design standard ensures consistency across all endpoints and provides clear guidelines for AI agents implementing the backend services.
