# ez-NFe Backend Technical Specifications

## Overview

This document provides comprehensive technical specifications for developing the ez-NFe SaaS backend using NestJS. These specifications are designed to guide AI agents in implementing a production-ready, scalable, and maintainable system.

## Architecture Principles

### 1. Modular Architecture
- Each business domain should be implemented as a separate NestJS module
- Modules should be loosely coupled with clear interfaces
- Shared functionality should be placed in common modules
- Follow Domain-Driven Design (DDD) principles

### 2. Dependency Injection
- Use NestJS built-in DI container for all dependencies
- Implement interfaces for all services to enable easy testing and mocking
- Use factory providers for complex object creation
- Avoid circular dependencies

### 3. Error Handling
- Implement global exception filters
- Use custom exception classes for business logic errors
- Provide meaningful error messages with error codes
- Log all errors with appropriate context

## Project Structure

```
src/
├── common/                 # Shared utilities and interfaces
│   ├── decorators/        # Custom decorators
│   ├── dto/              # Common DTOs
│   ├── enums/            # Application enums
│   ├── exceptions/       # Custom exception classes
│   ├── filters/          # Exception filters
│   ├── guards/           # Custom guards
│   ├── interceptors/     # Custom interceptors
│   ├── interfaces/       # Common interfaces
│   ├── pipes/            # Custom validation pipes
│   └── utils/            # Utility functions
├── config/               # Configuration modules
├── database/             # Database related files
│   ├── entities/         # TypeORM entities
│   ├── migrations/       # Database migrations
│   └── seeds/            # Database seeders
├── modules/              # Business modules
│   ├── auth/             # Authentication & authorization
│   ├── users/            # User management
│   ├── accounts/         # Account management
│   ├── emitters/         # Business unit management
│   ├── clients/          # Client entity management
│   ├── products/         # Product catalog management
│   ├── carriers/         # Carrier management
│   ├── payment-terms/    # Payment terms management
│   ├── certificates/     # Digital certificate management
│   ├── invoices/         # Invoice management
│   ├── storage/          # File storage abstraction
│   ├── integration/      # External API and webhooks
│   └── billing/          # Subscription and billing
└── main.ts               # Application entry point
```

## Module Structure Template

### **Standard Module Architecture**

Each module in the ez-NFe system follows a consistent structure that promotes maintainability, testability, and scalability:

```
src/modules/[module-name]/
├── [module-name].module.ts              # Module definition and dependencies
├── controllers/
│   ├── [module-name].controller.ts      # Main REST API controller
│   ├── [module-name].controller.spec.ts # Controller unit tests
│   └── admin-[module-name].controller.ts # Admin-specific endpoints (if needed)
├── services/
│   ├── [module-name].service.ts         # Core business logic service
│   ├── [module-name].service.spec.ts    # Service unit tests
│   ├── [module-name]-validation.service.ts # Validation logic
│   └── [module-name]-calculation.service.ts # Complex calculations (if needed)
├── entities/
│   ├── [main-entity].entity.ts          # Primary entity
│   ├── [related-entity].entity.ts       # Related entities
│   └── [junction-entity].entity.ts      # Junction tables (many-to-many)
├── dto/
│   ├── create-[entity].dto.ts           # Creation DTOs with validation
│   ├── update-[entity].dto.ts           # Update DTOs with validation
│   ├── query-[entity].dto.ts            # Query/filter DTOs for search
│   ├── response-[entity].dto.ts         # Response DTOs for API
│   └── bulk-[entity].dto.ts             # Bulk operation DTOs
├── repositories/
│   ├── [entity].repository.ts           # Custom repository methods
│   └── [entity].repository.spec.ts      # Repository tests
├── guards/
│   └── [module-specific].guard.ts       # Module-specific authorization
├── decorators/
│   └── [module-specific].decorator.ts   # Custom parameter decorators
├── interfaces/
│   ├── [module-name].interface.ts       # Service interfaces
│   └── [module-name]-config.interface.ts # Configuration interfaces
├── enums/
│   └── [module-name].enum.ts            # Module-specific enums
├── exceptions/
│   └── [module-name].exception.ts       # Custom business exceptions
├── pipes/
│   └── [module-name]-validation.pipe.ts # Custom validation pipes
├── interceptors/
│   └── [module-name].interceptor.ts     # Module-specific interceptors
└── tests/
    ├── integration/
    │   └── [module-name].integration.spec.ts # Integration tests
    ├── e2e/
    │   └── [module-name].e2e-spec.ts     # End-to-end tests
    └── fixtures/
        └── [module-name].fixtures.ts     # Test data fixtures
```

### **Module Implementation Example: Products Module**

```typescript
// src/modules/products/products.module.ts
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      ProductTax,
      ProductAddress, // If products have addresses
    ]),
    forwardRef(() => EmittersModule), // Avoid circular dependencies
    CacheModule,
  ],
  controllers: [
    ProductsController,
    AdminProductsController,
  ],
  providers: [
    ProductsService,
    ProductTaxService,
    ProductValidationService,
    ProductCalculationService,
    ProductRepository,
    ProductTaxRepository,
    {
      provide: 'PRODUCT_CONFIG',
      useFactory: () => ({
        maxProductsPerEmitter: 10000,
        allowDuplicateCodes: false,
      }),
    },
  ],
  exports: [
    ProductsService,
    ProductTaxService,
    ProductRepository,
  ],
})
export class ProductsModule {}
```

```typescript
// src/modules/products/controllers/products.controller.ts
@Controller('products')
@UseGuards(JwtAuthGuard, AccountAccessGuard)
@ApiTags('Products')
@ApiBearerAuth()
export class ProductsController {
  constructor(
    private readonly productsService: ProductsService,
    private readonly productTaxService: ProductTaxService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({ status: 201, description: 'Product created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 409, description: 'Product code already exists' })
  async create(
    @Body() createProductDto: CreateProductDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<Product>> {
    const product = await this.productsService.create(
      user.currentEmitterId,
      createProductDto,
    );
    return {
      success: true,
      data: product,
      message: 'Product created successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get products with pagination and filters' })
  @ApiResponse({ status: 200, description: 'Products retrieved successfully' })
  async findAll(
    @Query() queryDto: QueryProductsDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<PaginatedResult<Product>>> {
    const result = await this.productsService.findAll(
      user.currentEmitterId,
      queryDto,
    );
    return {
      success: true,
      data: result.data,
      meta: {
        pagination: result.meta,
      },
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get product by ID' })
  @ApiResponse({ status: 200, description: 'Product found' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<Product>> {
    const product = await this.productsService.findOne(
      user.currentEmitterId,
      id,
    );
    return {
      success: true,
      data: product,
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update product' })
  @ApiResponse({ status: 200, description: 'Product updated successfully' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateProductDto: UpdateProductDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<Product>> {
    const product = await this.productsService.update(
      user.currentEmitterId,
      id,
      updateProductDto,
    );
    return {
      success: true,
      data: product,
      message: 'Product updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete product' })
  @ApiResponse({ status: 200, description: 'Product deleted successfully' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<void>> {
    await this.productsService.remove(user.currentEmitterId, id);
    return {
      success: true,
      message: 'Product deleted successfully',
    };
  }

  // Tax-related endpoints
  @Get(':id/taxes')
  @ApiOperation({ summary: 'Get product tax configurations' })
  async getProductTaxes(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<ProductTax[]>> {
    const taxes = await this.productTaxService.getProductTaxes(id);
    return {
      success: true,
      data: taxes,
    };
  }

  @Post(':id/taxes')
  @ApiOperation({ summary: 'Add tax configuration to product' })
  async addProductTax(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() createTaxDto: CreateProductTaxDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<ProductTax>> {
    const tax = await this.productTaxService.createProductTax(id, createTaxDto);
    return {
      success: true,
      data: tax,
      message: 'Tax configuration added successfully',
    };
  }

  @Post(':id/calculate-taxes')
  @ApiOperation({ summary: 'Calculate taxes for product with given amount' })
  async calculateTaxes(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() calculateDto: CalculateTaxesDto,
    @CurrentUser() user: AuthUser,
  ): Promise<ApiResponse<TaxCalculationResult>> {
    const result = await this.productTaxService.calculateProductTaxes(
      id,
      calculateDto.baseValue,
      calculateDto.quantity,
    );
    return {
      success: true,
      data: result,
    };
  }
}
```

```typescript
// src/modules/products/services/products.service.ts
@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    private readonly productValidationService: ProductValidationService,
    private readonly productTaxService: ProductTaxService,
    @Inject('PRODUCT_CONFIG')
    private readonly config: ProductConfig,
  ) {}

  async create(
    emitterId: string,
    createProductDto: CreateProductDto,
  ): Promise<Product> {
    // Validate business rules
    await this.productValidationService.validateCreate(emitterId, createProductDto);

    // Create product entity
    const product = this.productRepository.create({
      emitterId,
      ...createProductDto,
    });

    // Save product
    const savedProduct = await this.productRepository.save(product);

    // Create tax configurations if provided
    if (createProductDto.taxes && createProductDto.taxes.length > 0) {
      for (const taxDto of createProductDto.taxes) {
        await this.productTaxService.createProductTax(savedProduct.id, taxDto);
      }
    }

    return this.findOne(emitterId, savedProduct.id);
  }

  async findAll(
    emitterId: string,
    queryDto: QueryProductsDto,
  ): Promise<PaginatedResult<Product>> {
    const queryBuilder = this.productRepository
      .createQueryBuilder('product')
      .where('product.emitterId = :emitterId', { emitterId })
      .andWhere('product.isActive = :isActive', { isActive: true });

    // Apply filters
    if (queryDto.search) {
      queryBuilder.andWhere(
        '(product.name ILIKE :search OR product.code ILIKE :search OR product.description ILIKE :search)',
        { search: `%${queryDto.search}%` },
      );
    }

    if (queryDto.ncmCode) {
      queryBuilder.andWhere('product.ncmCode = :ncmCode', { ncmCode: queryDto.ncmCode });
    }

    if (queryDto.cfop) {
      queryBuilder.andWhere('product.cfop = :cfop', { cfop: queryDto.cfop });
    }

    // Apply sorting
    const sortField = queryDto.sortBy || 'name';
    const sortOrder = queryDto.sortOrder || 'ASC';
    queryBuilder.orderBy(`product.${sortField}`, sortOrder);

    // Apply pagination
    const page = queryDto.page || 1;
    const limit = Math.min(queryDto.limit || 20, 100); // Max 100 items per page
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [products, total] = await queryBuilder.getManyAndCount();

    return {
      data: products,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page * limit < total,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(emitterId: string, id: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id, emitterId, isActive: true },
      relations: ['taxes'],
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async update(
    emitterId: string,
    id: string,
    updateProductDto: UpdateProductDto,
  ): Promise<Product> {
    const product = await this.findOne(emitterId, id);

    // Validate business rules for update
    await this.productValidationService.validateUpdate(product, updateProductDto);

    // Update product
    await this.productRepository.update(id, updateProductDto);

    return this.findOne(emitterId, id);
  }

  async remove(emitterId: string, id: string): Promise<void> {
    const product = await this.findOne(emitterId, id);

    // Check if product is used in any invoices
    const isUsedInInvoices = await this.productValidationService.isUsedInInvoices(id);
    if (isUsedInInvoices) {
      throw new ConflictException('Cannot delete product that is used in invoices');
    }

    // Soft delete
    await this.productRepository.update(id, {
      isActive: false,
      deletedAt: new Date(),
    });
  }

  async findByCode(emitterId: string, code: string): Promise<Product | null> {
    return this.productRepository.findOne({
      where: { emitterId, code, isActive: true },
    });
  }

  async bulkImport(
    emitterId: string,
    products: CreateProductDto[],
  ): Promise<BulkImportResult> {
    const results: BulkImportResult = {
      successful: [],
      failed: [],
      totalProcessed: products.length,
    };

    for (const [index, productDto] of products.entries()) {
      try {
        const product = await this.create(emitterId, productDto);
        results.successful.push({
          index,
          product,
        });
      } catch (error) {
        results.failed.push({
          index,
          productDto,
          error: error.message,
        });
      }
    }

    return results;
  }
}
```

```typescript
// src/modules/products/services/product-validation.service.ts
@Injectable()
export class ProductValidationService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(InvoiceItem)
    private readonly invoiceItemRepository: Repository<InvoiceItem>,
  ) {}

  async validateCreate(
    emitterId: string,
    createProductDto: CreateProductDto,
  ): Promise<void> {
    // Check for duplicate product code
    const existingProduct = await this.productRepository.findOne({
      where: { emitterId, code: createProductDto.code, isActive: true },
    });

    if (existingProduct) {
      throw new ConflictException(`Product with code '${createProductDto.code}' already exists`);
    }

    // Validate NCM code format
    if (!this.isValidNCM(createProductDto.ncmCode)) {
      throw new BadRequestException('Invalid NCM code format');
    }

    // Validate CFOP code format
    if (!this.isValidCFOP(createProductDto.cfop)) {
      throw new BadRequestException('Invalid CFOP code format');
    }

    // Check emitter product limit
    const productCount = await this.productRepository.count({
      where: { emitterId, isActive: true },
    });

    if (productCount >= 10000) { // Configurable limit
      throw new BadRequestException('Maximum number of products reached');
    }
  }

  async validateUpdate(
    product: Product,
    updateProductDto: UpdateProductDto,
  ): Promise<void> {
    // Check for duplicate code if code is being changed
    if (updateProductDto.code && updateProductDto.code !== product.code) {
      const existingProduct = await this.productRepository.findOne({
        where: {
          emitterId: product.emitterId,
          code: updateProductDto.code,
          isActive: true,
          id: Not(product.id),
        },
      });

      if (existingProduct) {
        throw new ConflictException(`Product with code '${updateProductDto.code}' already exists`);
      }
    }

    // Validate NCM code if being changed
    if (updateProductDto.ncmCode && !this.isValidNCM(updateProductDto.ncmCode)) {
      throw new BadRequestException('Invalid NCM code format');
    }

    // Validate CFOP code if being changed
    if (updateProductDto.cfop && !this.isValidCFOP(updateProductDto.cfop)) {
      throw new BadRequestException('Invalid CFOP code format');
    }
  }

  async isUsedInInvoices(productId: string): Promise<boolean> {
    const count = await this.invoiceItemRepository.count({
      where: { productId },
    });
    return count > 0;
  }

  private isValidNCM(ncm: string): boolean {
    return /^\d{8}$/.test(ncm);
  }

  private isValidCFOP(cfop: string): boolean {
    return /^\d{4}$/.test(cfop);
  }
}
```

### **Module Structure Guidelines**

#### **1. Controller Guidelines:**
- **Single Responsibility**: Each controller handles one resource type
- **Consistent Naming**: Use resource name + "Controller" (e.g., `ProductsController`)
- **Proper HTTP Methods**: GET, POST, PUT/PATCH, DELETE
- **Validation**: Use DTOs with class-validator decorators
- **Error Handling**: Let global filters handle exceptions
- **Documentation**: Use Swagger decorators for API documentation
- **Authentication**: Apply guards at controller or method level
- **Response Format**: Use consistent API response structure

#### **2. Service Guidelines:**
- **Business Logic**: Contains all business rules and logic
- **Single Responsibility**: Each service handles one domain
- **Dependency Injection**: Use constructor injection for dependencies
- **Error Handling**: Throw meaningful exceptions
- **Transactions**: Use database transactions for complex operations
- **Validation**: Delegate to validation services for complex rules
- **Testing**: Write comprehensive unit tests

#### **3. Entity Guidelines:**
- **TypeORM Decorators**: Use appropriate column types and constraints
- **Relationships**: Define proper entity relationships
- **Validation**: Use class-validator for basic validation
- **Soft Delete**: Implement soft delete for audit trails
- **Timestamps**: Include created/updated timestamps
- **Indexes**: Add database indexes for performance

#### **4. DTO Guidelines:**
- **Validation**: Use class-validator decorators
- **Documentation**: Use Swagger decorators
- **Transformation**: Use class-transformer for data transformation
- **Separation**: Separate DTOs for create, update, query, and response
- **Reusability**: Create base DTOs for common fields

#### **5. Repository Guidelines:**
- **Custom Queries**: Implement complex queries in repositories
- **Query Builder**: Use TypeORM query builder for complex queries
- **Performance**: Optimize queries with proper joins and indexes
- **Testing**: Write tests for custom repository methods

## Database Design

### 1. Entity Relationships
```typescript
User (1) ←→ (N) UserAccount (N) ←→ (1) Account
Account (1) ←→ (N) Emitter
Emitter (1) ←→ (N) Certificate
Emitter (1) ←→ (N) Client
Emitter (1) ←→ (N) Product
Emitter (1) ←→ (N) Carrier
Emitter (1) ←→ (N) PaymentTerm
Emitter (1) ←→ (N) Invoice
Invoice (1) ←→ (N) InvoiceItem
Invoice (N) ←→ (1) Client
Invoice (N) ←→ (1) Carrier [optional]
Invoice (N) ←→ (1) PaymentTerm
InvoiceItem (N) ←→ (1) Product
```

### 2. Entity Specifications

#### Base Entity
```typescript
@Entity()
export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
```

#### User Entity
```typescript
@Entity('user')
export class User extends BaseEntity {
  @Column({ unique: true })
  email: string;

  @Column()
  passwordHash: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  emailVerifiedAt?: Date;

  @OneToMany(() => UserAccount, userAccount => userAccount.user)
  userAccounts: UserAccount[];
}
```

#### Account Entity
```typescript
@Entity('account')
export class Account extends BaseEntity {
  @Column()
  name: string;

  @Column({ type: 'enum', enum: SubscriptionTier })
  subscriptionTier: SubscriptionTier;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => UserAccount, userAccount => userAccount.account)
  userAccounts: UserAccount[];

  @OneToMany(() => Emitter, emitter => emitter.account)
  emitters: Emitter[];
}
```

## Module Specifications

### 1. Authentication Module

#### Features
- JWT-based authentication with access/refresh tokens
- Email verification
- Password reset functionality
- Multi-factor authentication (future)

#### Key Components
```typescript
@Injectable()
export class AuthService {
  async signUp(signUpDto: SignUpDto): Promise<AuthResponseDto>;
  async signIn(signInDto: SignInDto): Promise<AuthResponseDto>;
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthResponseDto>;
  async logout(userId: string, refreshToken: string): Promise<void>;
  async switchEmitter(userId: string, emitterId: string): Promise<AuthResponseDto>;
  async verifyEmail(token: string): Promise<void>;
  async requestPasswordReset(email: string): Promise<void>;
  async resetPassword(token: string, newPassword: string): Promise<void>;
}
```

#### Security Requirements
- Passwords must be hashed using bcrypt with salt rounds >= 12
- JWT tokens must have appropriate expiration times (15min access, 7d refresh)
- Refresh tokens must be stored securely and rotated on use
- Rate limiting must be implemented for auth endpoints

### 2. Entity Management Modules

#### Address Types and Enums
```typescript
export enum AddressType {
  PRIMARY = 'primary',
  BILLING = 'billing',
  DELIVERY = 'delivery',
  PICKUP = 'pickup',
  WAREHOUSE = 'warehouse',
  BRANCH = 'branch',
  OTHER = 'other'
}
```

#### Address Entities
```typescript
@Entity('address')
export class Address extends BaseEntity {
  @Column()
  street: string;

  @Column()
  number: string;

  @Column({ nullable: true })
  complement?: string;

  @Column()
  neighborhood: string;

  @Column()
  city: string;

  @Column({ length: 2 })
  state: string;

  @Column({ length: 8 })
  zipCode: string;

  @Column({ length: 2, default: 'BR' })
  country: string;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude?: number;

  @OneToMany(() => ClientAddress, clientAddress => clientAddress.address)
  clientAddresses: ClientAddress[];

  @OneToMany(() => EmitterAddress, emitterAddress => emitterAddress.address)
  emitterAddresses: EmitterAddress[];

  @OneToMany(() => CarrierAddress, carrierAddress => carrierAddress.address)
  carrierAddresses: CarrierAddress[];
}

@Entity('client_address')
export class ClientAddress extends BaseEntity {
  @Column()
  clientId: string;

  @Column()
  addressId: string;

  @Column({ type: 'enum', enum: AddressType, default: AddressType.PRIMARY })
  addressType: AddressType;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Client, client => client.addresses)
  client: Client;

  @ManyToOne(() => Address, address => address.clientAddresses)
  address: Address;
}

@Entity('emitter_address')
export class EmitterAddress extends BaseEntity {
  @Column()
  emitterId: string;

  @Column()
  addressId: string;

  @Column({ type: 'enum', enum: AddressType, default: AddressType.PRIMARY })
  addressType: AddressType;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Emitter, emitter => emitter.addresses)
  emitter: Emitter;

  @ManyToOne(() => Address, address => address.emitterAddresses)
  address: Address;
}

@Entity('carrier_address')
export class CarrierAddress extends BaseEntity {
  @Column()
  carrierId: string;

  @Column()
  addressId: string;

  @Column({ type: 'enum', enum: AddressType, default: AddressType.PRIMARY })
  addressType: AddressType;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Carrier, carrier => carrier.addresses)
  carrier: Carrier;

  @ManyToOne(() => Address, address => address.carrierAddresses)
  address: Address;
}
```

#### Client Module
```typescript
@Entity('client')
export class Client extends BaseEntity {
  @Column()
  emitterId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  legalName?: string;

  @Column()
  documentType: DocumentType; // CNPJ or CPF

  @Column()
  federalTaxId: string;

  @Column({ nullable: true })
  stateTaxId?: string;

  @Column({ nullable: true })
  municipalTaxId?: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ type: 'enum', enum: TaxRegime })
  taxRegime: TaxRegime;

  @ManyToOne(() => Emitter, emitter => emitter.clients)
  emitter: Emitter;

  @OneToMany(() => ClientAddress, clientAddress => clientAddress.client)
  addresses: ClientAddress[];

  // Helper method to get primary address
  getPrimaryAddress(): Promise<Address | null> {
    return this.addresses
      ?.find(ca => ca.addressType === AddressType.PRIMARY && ca.isActive)
      ?.address || null;
  }

  // Helper method to get address by type
  getAddressByType(type: AddressType): Promise<Address | null> {
    return this.addresses
      ?.find(ca => ca.addressType === type && ca.isActive)
      ?.address || null;
  }
}
```

#### Address Management Service
```typescript
@Injectable()
export class AddressService {
  constructor(
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    @InjectRepository(ClientAddress)
    private readonly clientAddressRepository: Repository<ClientAddress>,
    @InjectRepository(EmitterAddress)
    private readonly emitterAddressRepository: Repository<EmitterAddress>,
    @InjectRepository(CarrierAddress)
    private readonly carrierAddressRepository: Repository<CarrierAddress>,
  ) {}

  async createClientAddress(
    clientId: string,
    createAddressDto: CreateAddressDto,
    addressType: AddressType = AddressType.PRIMARY,
    isDefault: boolean = false
  ): Promise<ClientAddress> {
    // Create the address
    const address = this.addressRepository.create(createAddressDto);
    const savedAddress = await this.addressRepository.save(address);

    // Handle default address logic
    if (isDefault || addressType === AddressType.PRIMARY) {
      await this.clientAddressRepository.update(
        { clientId, addressType, isDefault: true },
        { isDefault: false }
      );
    }

    // Create the client-address relationship
    const clientAddress = this.clientAddressRepository.create({
      clientId,
      addressId: savedAddress.id,
      addressType,
      isDefault: isDefault || addressType === AddressType.PRIMARY,
      isActive: true,
    });

    return this.clientAddressRepository.save(clientAddress);
  }

  async getClientAddresses(clientId: string): Promise<ClientAddress[]> {
    return this.clientAddressRepository.find({
      where: { clientId, isActive: true },
      relations: ['address'],
      order: { isDefault: 'DESC', addressType: 'ASC' },
    });
  }

  async getClientAddressByType(
    clientId: string,
    addressType: AddressType
  ): Promise<ClientAddress | null> {
    return this.clientAddressRepository.findOne({
      where: { clientId, addressType, isActive: true },
      relations: ['address'],
    });
  }

  async updateClientAddress(
    clientId: string,
    addressType: AddressType,
    updateAddressDto: Partial<CreateAddressDto>
  ): Promise<ClientAddress> {
    const clientAddress = await this.getClientAddressByType(clientId, addressType);
    if (!clientAddress) {
      throw new NotFoundException(`Address of type ${addressType} not found for client`);
    }

    // Update the address
    await this.addressRepository.update(clientAddress.addressId, updateAddressDto);

    // Return updated client address
    return this.getClientAddressByType(clientId, addressType);
  }

  async removeClientAddress(clientId: string, addressId: string): Promise<void> {
    const clientAddress = await this.clientAddressRepository.findOne({
      where: { clientId, addressId },
    });

    if (!clientAddress) {
      throw new NotFoundException('Client address not found');
    }

    if (clientAddress.addressType === AddressType.PRIMARY) {
      throw new BadRequestException('Cannot remove primary address');
    }

    // Soft delete the relationship
    await this.clientAddressRepository.update(
      { clientId, addressId },
      { isActive: false }
    );
  }

  async setDefaultAddress(
    clientId: string,
    addressType: AddressType,
    addressId: string
  ): Promise<void> {
    // Remove default from other addresses of the same type
    await this.clientAddressRepository.update(
      { clientId, addressType, isDefault: true },
      { isDefault: false }
    );

    // Set new default
    await this.clientAddressRepository.update(
      { clientId, addressId, addressType },
      { isDefault: true }
    );
  }

  // Similar methods for EmitterAddress and CarrierAddress...
}
```

#### Product and Tax Entities
```typescript
export enum TaxType {
  ICMS = 'icms',
  IPI = 'ipi',
  PIS = 'pis',
  COFINS = 'cofins',
  ISS = 'iss',
  CSLL = 'csll',
  IRPJ = 'irpj',
  CPP = 'cpp',
  OTHER = 'other'
}

@Entity('product')
export class Product extends BaseEntity {
  @Column()
  emitterId: string;

  @Column()
  code: string; // Internal product code

  @Column()
  name: string;

  @Column('text')
  description: string;

  @Column()
  ncmCode: string; // Nomenclatura Comum do Mercosul

  @Column()
  cfop: string; // Código Fiscal de Operações e Prestações

  @Column()
  unit: string; // Unit of measurement

  @Column('decimal', { precision: 12, scale: 2 })
  unitPrice: number;

  @Column({ default: 0 })
  icmsOrigin: number; // 0-8 origin codes

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Emitter, emitter => emitter.products)
  emitter: Emitter;

  @OneToMany(() => ProductTax, productTax => productTax.product)
  taxes: ProductTax[];

  // Helper method to get current tax configuration
  getCurrentTaxes(): ProductTax[] {
    const currentDate = new Date();
    return this.taxes?.filter(tax =>
      tax.isActive &&
      (!tax.validFrom || tax.validFrom <= currentDate) &&
      (!tax.validTo || tax.validTo >= currentDate)
    ) || [];
  }

  // Helper method to get specific tax type
  getTaxByType(taxType: TaxType): ProductTax | null {
    return this.getCurrentTaxes().find(tax => tax.taxType === taxType) || null;
  }

  // Helper method to calculate tax amount
  calculateTaxAmount(taxType: TaxType, baseValue: number): number {
    const tax = this.getTaxByType(taxType);
    if (!tax || tax.isExempt) return 0;

    const calculationBase = (baseValue * tax.calculationBase) / 100;
    const reducedBase = calculationBase * (1 - tax.reductionPercentage / 100);
    const taxAmount = (reducedBase * tax.rate) / 100 + tax.fixedAmount;
    const additionalAmount = (taxAmount * tax.additionalPercentage) / 100;

    return taxAmount + additionalAmount;
  }
}

@Entity('product_tax')
export class ProductTax extends BaseEntity {
  @Column()
  productId: string;

  @Column({ type: 'enum', enum: TaxType })
  taxType: TaxType;

  @Column({ nullable: true })
  cstCode?: string; // CST code

  @Column({ nullable: true })
  csosnCode?: string; // CSOSN code for Simples Nacional

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  rate: number; // Tax rate percentage

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  fixedAmount: number; // Fixed tax amount

  @Column('decimal', { precision: 5, scale: 2, default: 100 })
  calculationBase: number; // Percentage of product value to use as base

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  reductionPercentage: number; // Base reduction percentage

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  additionalPercentage: number; // Additional tax percentage

  @Column({ default: false })
  isExempt: boolean;

  @Column({ nullable: true })
  exemptionReason?: string;

  @Column({ type: 'date', nullable: true })
  validFrom?: Date;

  @Column({ type: 'date', nullable: true })
  validTo?: Date;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Product, product => product.taxes)
  product: Product;
}
```

#### Product Tax Management Service
```typescript
@Injectable()
export class ProductTaxService {
  constructor(
    @InjectRepository(ProductTax)
    private readonly productTaxRepository: Repository<ProductTax>,
  ) {}

  async createProductTax(
    productId: string,
    createTaxDto: CreateProductTaxDto
  ): Promise<ProductTax> {
    // Check for overlapping tax configurations
    await this.validateTaxPeriod(productId, createTaxDto.taxType, createTaxDto.validFrom, createTaxDto.validTo);

    const productTax = this.productTaxRepository.create({
      productId,
      ...createTaxDto,
    });

    return this.productTaxRepository.save(productTax);
  }

  async updateProductTax(
    id: string,
    updateTaxDto: Partial<CreateProductTaxDto>
  ): Promise<ProductTax> {
    const existingTax = await this.productTaxRepository.findOne({ where: { id } });
    if (!existingTax) {
      throw new NotFoundException('Product tax configuration not found');
    }

    // If updating validity period, check for overlaps
    if (updateTaxDto.validFrom || updateTaxDto.validTo) {
      await this.validateTaxPeriod(
        existingTax.productId,
        existingTax.taxType,
        updateTaxDto.validFrom || existingTax.validFrom,
        updateTaxDto.validTo || existingTax.validTo,
        id // Exclude current record from validation
      );
    }

    await this.productTaxRepository.update(id, updateTaxDto);
    return this.productTaxRepository.findOne({ where: { id } });
  }

  async getProductTaxes(
    productId: string,
    taxType?: TaxType,
    activeOnly: boolean = true
  ): Promise<ProductTax[]> {
    const where: any = { productId };

    if (taxType) {
      where.taxType = taxType;
    }

    if (activeOnly) {
      where.isActive = true;
    }

    return this.productTaxRepository.find({
      where,
      order: { taxType: 'ASC', validFrom: 'DESC' },
    });
  }

  async getCurrentProductTaxes(productId: string): Promise<ProductTax[]> {
    const currentDate = new Date();

    return this.productTaxRepository.find({
      where: {
        productId,
        isActive: true,
        validFrom: LessThanOrEqual(currentDate),
        validTo: Or(IsNull(), MoreThanOrEqual(currentDate)),
      },
      order: { taxType: 'ASC' },
    });
  }

  async calculateProductTaxes(
    productId: string,
    baseValue: number,
    quantity: number = 1
  ): Promise<TaxCalculationResult> {
    const taxes = await this.getCurrentProductTaxes(productId);
    const totalValue = baseValue * quantity;

    const taxCalculations: TaxCalculation[] = [];
    let totalTaxAmount = 0;

    for (const tax of taxes) {
      const taxAmount = this.calculateSingleTax(tax, totalValue);

      taxCalculations.push({
        taxType: tax.taxType,
        cstCode: tax.cstCode,
        csosnCode: tax.csosnCode,
        rate: tax.rate,
        calculationBase: (totalValue * tax.calculationBase) / 100,
        taxAmount,
        isExempt: tax.isExempt,
        exemptionReason: tax.exemptionReason,
      });

      totalTaxAmount += taxAmount;
    }

    return {
      baseValue: totalValue,
      taxCalculations,
      totalTaxAmount,
      netValue: totalValue + totalTaxAmount,
    };
  }

  private calculateSingleTax(tax: ProductTax, baseValue: number): number {
    if (tax.isExempt) return 0;

    const calculationBase = (baseValue * tax.calculationBase) / 100;
    const reducedBase = calculationBase * (1 - tax.reductionPercentage / 100);
    const taxAmount = (reducedBase * tax.rate) / 100 + tax.fixedAmount;
    const additionalAmount = (taxAmount * tax.additionalPercentage) / 100;

    return Math.round((taxAmount + additionalAmount) * 100) / 100; // Round to 2 decimal places
  }

  private async validateTaxPeriod(
    productId: string,
    taxType: TaxType,
    validFrom?: Date,
    validTo?: Date,
    excludeId?: string
  ): Promise<void> {
    const query = this.productTaxRepository.createQueryBuilder('tax')
      .where('tax.productId = :productId', { productId })
      .andWhere('tax.taxType = :taxType', { taxType })
      .andWhere('tax.isActive = true');

    if (excludeId) {
      query.andWhere('tax.id != :excludeId', { excludeId });
    }

    // Check for overlapping periods
    if (validFrom && validTo) {
      query.andWhere(
        '(tax.validFrom <= :validTo AND (tax.validTo IS NULL OR tax.validTo >= :validFrom))',
        { validFrom, validTo }
      );
    } else if (validFrom) {
      query.andWhere('(tax.validTo IS NULL OR tax.validTo >= :validFrom)', { validFrom });
    }

    const overlapping = await query.getOne();
    if (overlapping) {
      throw new ConflictException(
        `Overlapping tax configuration found for ${taxType} in the specified period`
      );
    }
  }

  async deactivateProductTax(id: string): Promise<void> {
    await this.productTaxRepository.update(id, { isActive: false });
  }

  async bulkUpdateTaxRates(
    taxType: TaxType,
    newRate: number,
    effectiveDate: Date
  ): Promise<void> {
    // This method can be used for government-mandated tax rate changes
    const affectedTaxes = await this.productTaxRepository.find({
      where: {
        taxType,
        isActive: true,
        validTo: Or(IsNull(), MoreThanOrEqual(effectiveDate)),
      },
    });

    for (const tax of affectedTaxes) {
      // End current tax configuration
      if (!tax.validTo || tax.validTo > effectiveDate) {
        await this.productTaxRepository.update(tax.id, {
          validTo: new Date(effectiveDate.getTime() - 24 * 60 * 60 * 1000), // Day before
        });

        // Create new tax configuration with updated rate
        await this.productTaxRepository.save({
          ...tax,
          id: undefined, // Create new record
          rate: newRate,
          validFrom: effectiveDate,
          validTo: tax.validTo, // Keep original end date
          createdAt: undefined,
          updatedAt: undefined,
        });
      }
    }
  }
}

// Supporting interfaces
interface TaxCalculation {
  taxType: TaxType;
  cstCode?: string;
  csosnCode?: string;
  rate: number;
  calculationBase: number;
  taxAmount: number;
  isExempt: boolean;
  exemptionReason?: string;
}

interface TaxCalculationResult {
  baseValue: number;
  taxCalculations: TaxCalculation[];
  totalTaxAmount: number;
  netValue: number;
}
```

#### Payment Term Entities
```typescript
export enum PaymentMethod {
  MONEY = 'money',
  CHECK = 'check',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  PIX = 'pix',
  BANK_SLIP = 'bank_slip',
  PROMISSORY_NOTE = 'promissory_note',
  DUPLICATE = 'duplicate',
  OTHER = 'other'
}

export enum PaymentTermType {
  CASH = 'cash',
  INSTALLMENTS = 'installments',
  TERM = 'term',
  MIXED = 'mixed'
}

@Entity('payment_term')
export class PaymentTerm extends BaseEntity {
  @Column()
  emitterId: string;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description?: string;

  @Column({ type: 'enum', enum: PaymentTermType, default: PaymentTermType.CASH })
  termType: PaymentTermType;

  @Column({ type: 'enum', enum: PaymentMethod })
  defaultPaymentMethod: PaymentMethod;

  @Column({ default: 1 })
  totalInstallments: number;

  @Column({ default: 30 })
  daysBetweenInstallments: number;

  @Column({ default: 0 })
  firstInstallmentDays: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  cashDiscountPercentage: number;

  @Column({ default: 0 })
  cashDiscountDays: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  lateFeePercentage: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  interestRateMonthly: number;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Emitter, emitter => emitter.paymentTerms)
  emitter: Emitter;

  @OneToMany(() => PaymentTermInstallment, installment => installment.paymentTerm, { cascade: true })
  installments: PaymentTermInstallment[];

  // Helper method to get calculated installments
  getCalculatedInstallments(invoiceAmount: number, invoiceDate: Date): CalculatedInstallment[] {
    if (this.installments && this.installments.length > 0) {
      // Use custom installments
      return this.installments.map(inst => ({
        number: inst.installmentNumber,
        dueDate: new Date(invoiceDate.getTime() + inst.daysToDue * 24 * 60 * 60 * 1000),
        amount: (invoiceAmount * inst.percentage) / 100,
        paymentMethod: inst.paymentMethod || this.defaultPaymentMethod,
        description: inst.description
      }));
    } else {
      // Use simple configuration
      const installments: CalculatedInstallment[] = [];
      const amountPerInstallment = invoiceAmount / this.totalInstallments;

      for (let i = 1; i <= this.totalInstallments; i++) {
        const daysToAdd = this.firstInstallmentDays + (i - 1) * this.daysBetweenInstallments;
        const dueDate = new Date(invoiceDate.getTime() + daysToAdd * 24 * 60 * 60 * 1000);

        installments.push({
          number: i,
          dueDate,
          amount: amountPerInstallment,
          paymentMethod: this.defaultPaymentMethod
        });
      }

      return installments;
    }
  }

  // Helper method to calculate cash discount
  calculateCashDiscount(invoiceAmount: number, paymentDate: Date, invoiceDate: Date): number {
    if (this.cashDiscountPercentage === 0 || this.cashDiscountDays === 0) {
      return 0;
    }

    const daysDiff = Math.floor((paymentDate.getTime() - invoiceDate.getTime()) / (24 * 60 * 60 * 1000));

    if (daysDiff <= this.cashDiscountDays) {
      return (invoiceAmount * this.cashDiscountPercentage) / 100;
    }

    return 0;
  }
}

@Entity('payment_term_installment')
export class PaymentTermInstallment extends BaseEntity {
  @Column()
  paymentTermId: string;

  @Column()
  installmentNumber: number;

  @Column()
  daysToDue: number;

  @Column('decimal', { precision: 5, scale: 2 })
  percentage: number;

  @Column({ type: 'enum', enum: PaymentMethod, nullable: true })
  paymentMethod?: PaymentMethod;

  @Column({ nullable: true })
  description?: string;

  @ManyToOne(() => PaymentTerm, paymentTerm => paymentTerm.installments)
  paymentTerm: PaymentTerm;
}

// Supporting interfaces
interface CalculatedInstallment {
  number: number;
  dueDate: Date;
  amount: number;
  paymentMethod: PaymentMethod;
  description?: string;
}
```

#### Payment Term Management Service
```typescript
@Injectable()
export class PaymentTermService {
  constructor(
    @InjectRepository(PaymentTerm)
    private readonly paymentTermRepository: Repository<PaymentTerm>,
    @InjectRepository(PaymentTermInstallment)
    private readonly installmentRepository: Repository<PaymentTermInstallment>,
  ) {}

  async createPaymentTerm(
    emitterId: string,
    createPaymentTermDto: CreatePaymentTermDto
  ): Promise<PaymentTerm> {
    const paymentTerm = this.paymentTermRepository.create({
      emitterId,
      ...createPaymentTermDto,
    });

    const savedPaymentTerm = await this.paymentTermRepository.save(paymentTerm);

    // Create custom installments if provided
    if (createPaymentTermDto.customInstallments && createPaymentTermDto.customInstallments.length > 0) {
      await this.createCustomInstallments(savedPaymentTerm.id, createPaymentTermDto.customInstallments);
    }

    return this.findOne(savedPaymentTerm.id);
  }

  async findAll(emitterId: string, activeOnly: boolean = true): Promise<PaymentTerm[]> {
    const where: any = { emitterId };
    if (activeOnly) {
      where.isActive = true;
    }

    return this.paymentTermRepository.find({
      where,
      relations: ['installments'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<PaymentTerm> {
    const paymentTerm = await this.paymentTermRepository.findOne({
      where: { id },
      relations: ['installments'],
    });

    if (!paymentTerm) {
      throw new NotFoundException('Payment term not found');
    }

    return paymentTerm;
  }

  async update(
    id: string,
    updatePaymentTermDto: Partial<CreatePaymentTermDto>
  ): Promise<PaymentTerm> {
    const paymentTerm = await this.findOne(id);

    // Update basic payment term data
    await this.paymentTermRepository.update(id, updatePaymentTermDto);

    // Handle custom installments update
    if (updatePaymentTermDto.customInstallments !== undefined) {
      // Remove existing installments
      await this.installmentRepository.delete({ paymentTermId: id });

      // Create new installments if provided
      if (updatePaymentTermDto.customInstallments.length > 0) {
        await this.createCustomInstallments(id, updatePaymentTermDto.customInstallments);
      }
    }

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const paymentTerm = await this.findOne(id);
    await this.paymentTermRepository.update(id, { isActive: false });
  }

  async calculateInstallments(
    paymentTermId: string,
    invoiceAmount: number,
    invoiceDate: Date
  ): Promise<CalculatedInstallment[]> {
    const paymentTerm = await this.findOne(paymentTermId);
    return paymentTerm.getCalculatedInstallments(invoiceAmount, invoiceDate);
  }

  async calculateCashDiscount(
    paymentTermId: string,
    invoiceAmount: number,
    paymentDate: Date,
    invoiceDate: Date
  ): Promise<number> {
    const paymentTerm = await this.findOne(paymentTermId);
    return paymentTerm.calculateCashDiscount(invoiceAmount, paymentDate, invoiceDate);
  }

  async getCommonPaymentTerms(emitterId: string): Promise<PaymentTerm[]> {
    // Return commonly used payment terms for quick selection
    return this.paymentTermRepository.find({
      where: {
        emitterId,
        isActive: true,
        termType: In([PaymentTermType.CASH, PaymentTermType.TERM])
      },
      relations: ['installments'],
      order: { name: 'ASC' },
      take: 10,
    });
  }

  async validateInstallments(installments: CreateInstallmentDto[]): Promise<void> {
    // Validate that installment numbers are sequential
    const numbers = installments.map(i => i.installmentNumber).sort((a, b) => a - b);
    for (let i = 0; i < numbers.length; i++) {
      if (numbers[i] !== i + 1) {
        throw new BadRequestException('Installment numbers must be sequential starting from 1');
      }
    }

    // Validate that percentages sum to 100%
    const totalPercentage = installments.reduce((sum, i) => sum + i.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new BadRequestException('Installment percentages must sum to 100%');
    }

    // Validate due dates are in ascending order
    const sortedByDays = [...installments].sort((a, b) => a.daysToDue - b.daysToDue);
    for (let i = 0; i < sortedByDays.length; i++) {
      if (sortedByDays[i].installmentNumber !== installments[i].installmentNumber) {
        // Allow some flexibility, but warn about potential issues
        console.warn('Installment due dates are not in sequential order');
      }
    }
  }

  private async createCustomInstallments(
    paymentTermId: string,
    installments: CreateInstallmentDto[]
  ): Promise<void> {
    await this.validateInstallments(installments);

    const installmentEntities = installments.map(installment =>
      this.installmentRepository.create({
        paymentTermId,
        ...installment,
      })
    );

    await this.installmentRepository.save(installmentEntities);
  }

  // Predefined payment term templates
  async createCommonPaymentTerms(emitterId: string): Promise<PaymentTerm[]> {
    const commonTerms = [
      {
        name: 'À Vista',
        description: 'Pagamento à vista',
        termType: PaymentTermType.CASH,
        defaultPaymentMethod: PaymentMethod.PIX,
        totalInstallments: 1,
        firstInstallmentDays: 0,
        cashDiscountPercentage: 2,
        cashDiscountDays: 0,
      },
      {
        name: '30 Dias',
        description: 'Pagamento em 30 dias',
        termType: PaymentTermType.TERM,
        defaultPaymentMethod: PaymentMethod.BANK_SLIP,
        totalInstallments: 1,
        firstInstallmentDays: 30,
      },
      {
        name: '2x sem juros',
        description: 'Parcelado em 2x sem juros',
        termType: PaymentTermType.INSTALLMENTS,
        defaultPaymentMethod: PaymentMethod.CREDIT_CARD,
        totalInstallments: 2,
        firstInstallmentDays: 30,
        daysBetweenInstallments: 30,
      },
      {
        name: '3x sem juros',
        description: 'Parcelado em 3x sem juros',
        termType: PaymentTermType.INSTALLMENTS,
        defaultPaymentMethod: PaymentMethod.CREDIT_CARD,
        totalInstallments: 3,
        firstInstallmentDays: 30,
        daysBetweenInstallments: 30,
      },
    ];

    const createdTerms: PaymentTerm[] = [];
    for (const termData of commonTerms) {
      const term = await this.createPaymentTerm(emitterId, termData);
      createdTerms.push(term);
    }

    return createdTerms;
  }
}
```

### 3. Invoice Module

#### Invoice and Tax Entities
```typescript
@Entity('invoice')
export class Invoice extends BaseEntity {
  @Column()
  emitterId: string;

  @Column()
  number: number;

  @Column()
  series: number;

  @Column()
  clientId: string;

  @Column({ nullable: true })
  carrierId?: string;

  @Column()
  paymentTermId: string;

  @Column({ type: 'enum', enum: InvoiceStatus })
  status: InvoiceStatus;

  @Column({ type: 'enum', enum: InvoiceType })
  type: InvoiceType;

  @Column()
  issueDate: Date;

  @Column({ nullable: true })
  dueDate?: Date;

  @Column('decimal', { precision: 12, scale: 2 })
  subtotal: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  discountAmount: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  freightAmount: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  insuranceAmount: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  otherExpenses: number;

  @Column('decimal', { precision: 12, scale: 2 })
  totalAmount: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  totalTaxes: number;

  @Column('text', { nullable: true })
  observations?: string;

  @Column('text', { nullable: true })
  internalNotes?: string;

  @Column({ nullable: true })
  accessKey?: string; // Chave de acesso

  @Column({ nullable: true })
  protocol?: string; // SEFAZ protocol

  @Column({ nullable: true })
  receiptNumber?: string;

  @Column('text', { nullable: true })
  xmlContent?: string;

  @Column({ nullable: true })
  authorizedAt?: Date;

  @Column({ nullable: true })
  cancelledAt?: Date;

  @Column('text', { nullable: true })
  cancellationReason?: string;

  @OneToMany(() => InvoiceItem, item => item.invoice, { cascade: true })
  items: InvoiceItem[];

  @OneToMany(() => InvoiceTax, tax => tax.invoice, { cascade: true })
  taxes: InvoiceTax[];

  @ManyToOne(() => Emitter, emitter => emitter.invoices)
  emitter: Emitter;

  @ManyToOne(() => Client, client => client.invoices)
  client: Client;

  @ManyToOne(() => PaymentTerm, paymentTerm => paymentTerm.invoices)
  paymentTerm: PaymentTerm;

  // Helper methods for tax calculations
  getTaxesByType(taxType: TaxType): InvoiceTax[] {
    return this.taxes?.filter(tax => tax.taxType === taxType) || [];
  }

  getTotalTaxByType(taxType: TaxType): number {
    return this.getTaxesByType(taxType)
      .reduce((sum, tax) => sum + tax.taxAmount, 0);
  }

  recalculateTotals(): void {
    this.subtotal = this.items?.reduce((sum, item) => sum + item.totalPrice, 0) || 0;
    this.totalTaxes = this.taxes?.reduce((sum, tax) => sum + tax.taxAmount, 0) || 0;
    this.totalAmount = this.subtotal - this.discountAmount + this.freightAmount +
                      this.insuranceAmount + this.otherExpenses + this.totalTaxes;
  }
}

@Entity('invoice_item')
export class InvoiceItem extends BaseEntity {
  @Column()
  invoiceId: string;

  @Column()
  productId: string;

  @Column()
  itemNumber: number;

  // Product snapshot
  @Column()
  productCode: string;

  @Column()
  productName: string;

  @Column('text', { nullable: true })
  productDescription?: string;

  @Column()
  ncmCode: string;

  @Column()
  cfop: string;

  @Column()
  unit: string;

  @Column()
  icmsOrigin: number;

  // Quantities and prices
  @Column('decimal', { precision: 10, scale: 4 })
  quantity: number;

  @Column('decimal', { precision: 12, scale: 2 })
  unitPrice: number;

  @Column('decimal', { precision: 12, scale: 2 })
  totalPrice: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  discountAmount: number;

  @ManyToOne(() => Invoice, invoice => invoice.items)
  invoice: Invoice;

  @ManyToOne(() => Product, product => product.invoiceItems)
  product: Product;

  @OneToMany(() => InvoiceTax, tax => tax.invoiceItem)
  taxes: InvoiceTax[];

  // Helper methods
  getTaxesByType(taxType: TaxType): InvoiceTax[] {
    return this.taxes?.filter(tax => tax.taxType === taxType) || [];
  }

  getTotalTaxByType(taxType: TaxType): number {
    return this.getTaxesByType(taxType)
      .reduce((sum, tax) => sum + tax.taxAmount, 0);
  }
}

@Entity('invoice_tax')
export class InvoiceTax extends BaseEntity {
  @Column()
  invoiceId: string;

  @Column({ nullable: true })
  invoiceItemId?: string; // NULL for invoice-level taxes

  @Column({ type: 'enum', enum: TaxType })
  taxType: TaxType;

  @Column({ nullable: true })
  cstCode?: string;

  @Column({ nullable: true })
  csosnCode?: string;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  calculationBase: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  rate: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  taxAmount: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  reductionPercentage: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  additionalPercentage: number;

  @Column('decimal', { precision: 12, scale: 2, default: 0 })
  fixedAmount: number;

  @Column({ default: false })
  isExempt: boolean;

  @Column({ nullable: true })
  exemptionReason?: string;

  @Column({ default: false })
  isManualOverride: boolean;

  @Column({ nullable: true })
  overrideReason?: string;

  @Column({ nullable: true })
  originalProductTaxId?: string;

  @ManyToOne(() => Invoice, invoice => invoice.taxes)
  invoice: Invoice;

  @ManyToOne(() => InvoiceItem, item => item.taxes, { nullable: true })
  invoiceItem?: InvoiceItem;
}
```

#### Invoice Tax Management Service
```typescript
@Injectable()
export class InvoiceTaxService {
  constructor(
    @InjectRepository(InvoiceTax)
    private readonly invoiceTaxRepository: Repository<InvoiceTax>,
    @InjectRepository(ProductTax)
    private readonly productTaxRepository: Repository<ProductTax>,
    private readonly productTaxService: ProductTaxService,
  ) {}

  async calculateAndCreateInvoiceTaxes(
    invoice: Invoice,
    invoiceItems: InvoiceItem[]
  ): Promise<InvoiceTax[]> {
    const allTaxes: InvoiceTax[] = [];

    // Calculate taxes for each invoice item
    for (const item of invoiceItems) {
      const itemTaxes = await this.calculateItemTaxes(invoice, item);
      allTaxes.push(...itemTaxes);
    }

    // Calculate invoice-level taxes (if any)
    const invoiceLevelTaxes = await this.calculateInvoiceLevelTaxes(invoice);
    allTaxes.push(...invoiceLevelTaxes);

    // Save all taxes
    return this.invoiceTaxRepository.save(allTaxes);
  }

  async calculateItemTaxes(
    invoice: Invoice,
    item: InvoiceItem
  ): Promise<InvoiceTax[]> {
    // Get current product tax configurations
    const productTaxes = await this.productTaxService.getCurrentProductTaxes(item.productId);

    const invoiceTaxes: InvoiceTax[] = [];
    const baseValue = item.totalPrice - item.discountAmount;

    for (const productTax of productTaxes) {
      const invoiceTax = await this.createInvoiceTaxFromProductTax(
        invoice.id,
        item.id,
        productTax,
        baseValue
      );
      invoiceTaxes.push(invoiceTax);
    }

    return invoiceTaxes;
  }

  async calculateInvoiceLevelTaxes(invoice: Invoice): Promise<InvoiceTax[]> {
    // Some taxes are calculated at invoice level (e.g., service taxes, retention taxes)
    const invoiceLevelTaxes: InvoiceTax[] = [];

    // Example: ISS for service invoices
    if (invoice.type === InvoiceType.NFE && this.isServiceInvoice(invoice)) {
      const issTax = await this.calculateISSTax(invoice);
      if (issTax) {
        invoiceLevelTaxes.push(issTax);
      }
    }

    return invoiceLevelTaxes;
  }

  private async createInvoiceTaxFromProductTax(
    invoiceId: string,
    invoiceItemId: string,
    productTax: ProductTax,
    baseValue: number
  ): Promise<InvoiceTax> {
    const calculationBase = (baseValue * productTax.calculationBase) / 100;
    const reducedBase = calculationBase * (1 - productTax.reductionPercentage / 100);
    const taxAmount = (reducedBase * productTax.rate) / 100 + productTax.fixedAmount;
    const additionalAmount = (taxAmount * productTax.additionalPercentage) / 100;
    const finalTaxAmount = taxAmount + additionalAmount;

    return this.invoiceTaxRepository.create({
      invoiceId,
      invoiceItemId,
      taxType: productTax.taxType,
      cstCode: productTax.cstCode,
      csosnCode: productTax.csosnCode,
      calculationBase,
      rate: productTax.rate,
      taxAmount: Math.round(finalTaxAmount * 100) / 100,
      reductionPercentage: productTax.reductionPercentage,
      additionalPercentage: productTax.additionalPercentage,
      fixedAmount: productTax.fixedAmount,
      isExempt: productTax.isExempt,
      exemptionReason: productTax.exemptionReason,
      originalProductTaxId: productTax.id,
      isManualOverride: false,
    });
  }

  async updateInvoiceTax(
    id: string,
    updateData: Partial<InvoiceTax>,
    overrideReason?: string
  ): Promise<InvoiceTax> {
    const existingTax = await this.invoiceTaxRepository.findOne({ where: { id } });
    if (!existingTax) {
      throw new NotFoundException('Invoice tax not found');
    }

    // Mark as manual override if values are being changed
    const isOverride = this.isManualOverride(existingTax, updateData);

    await this.invoiceTaxRepository.update(id, {
      ...updateData,
      isManualOverride: isOverride,
      overrideReason: isOverride ? overrideReason : existingTax.overrideReason,
    });

    return this.invoiceTaxRepository.findOne({ where: { id } });
  }

  async recalculateInvoiceTaxes(invoiceId: string): Promise<InvoiceTax[]> {
    // Remove existing taxes
    await this.invoiceTaxRepository.delete({ invoiceId });

    // Get invoice with items
    const invoice = await this.getInvoiceWithItems(invoiceId);

    // Recalculate all taxes
    return this.calculateAndCreateInvoiceTaxes(invoice, invoice.items);
  }

  async getInvoiceTaxSummary(invoiceId: string): Promise<InvoiceTaxSummary> {
    const taxes = await this.invoiceTaxRepository.find({
      where: { invoiceId },
      order: { taxType: 'ASC' },
    });

    const summary: InvoiceTaxSummary = {
      totalTaxAmount: 0,
      taxesByType: {},
      hasManualOverrides: false,
      exemptTaxes: [],
    };

    for (const tax of taxes) {
      summary.totalTaxAmount += tax.taxAmount;

      if (!summary.taxesByType[tax.taxType]) {
        summary.taxesByType[tax.taxType] = {
          taxType: tax.taxType,
          totalBase: 0,
          totalAmount: 0,
          averageRate: 0,
          count: 0,
        };
      }

      const typeSum = summary.taxesByType[tax.taxType];
      typeSum.totalBase += tax.calculationBase;
      typeSum.totalAmount += tax.taxAmount;
      typeSum.count += 1;
      typeSum.averageRate = typeSum.totalBase > 0 ? (typeSum.totalAmount / typeSum.totalBase) * 100 : 0;

      if (tax.isManualOverride) {
        summary.hasManualOverrides = true;
      }

      if (tax.isExempt) {
        summary.exemptTaxes.push({
          taxType: tax.taxType,
          reason: tax.exemptionReason,
        });
      }
    }

    return summary;
  }

  async validateTaxCompliance(invoiceId: string): Promise<TaxComplianceResult> {
    const taxes = await this.invoiceTaxRepository.find({
      where: { invoiceId },
      relations: ['invoiceItem'],
    });

    const issues: TaxComplianceIssue[] = [];

    // Validate required taxes are present
    const requiredTaxTypes = [TaxType.ICMS, TaxType.PIS, TaxType.COFINS];
    for (const requiredType of requiredTaxTypes) {
      const hasTax = taxes.some(tax => tax.taxType === requiredType);
      if (!hasTax) {
        issues.push({
          type: 'missing_tax',
          taxType: requiredType,
          message: `Missing required tax: ${requiredType}`,
          severity: 'error',
        });
      }
    }

    // Validate CST codes are present
    for (const tax of taxes) {
      if (!tax.cstCode && !tax.csosnCode) {
        issues.push({
          type: 'missing_cst',
          taxType: tax.taxType,
          message: `Missing CST/CSOSN code for ${tax.taxType}`,
          severity: 'error',
        });
      }
    }

    // Validate tax amounts are reasonable
    for (const tax of taxes) {
      if (tax.rate > 0 && tax.taxAmount === 0 && !tax.isExempt) {
        issues.push({
          type: 'zero_tax_amount',
          taxType: tax.taxType,
          message: `Tax amount is zero despite positive rate for ${tax.taxType}`,
          severity: 'warning',
        });
      }
    }

    return {
      isCompliant: issues.filter(i => i.severity === 'error').length === 0,
      issues,
      totalIssues: issues.length,
      errorCount: issues.filter(i => i.severity === 'error').length,
      warningCount: issues.filter(i => i.severity === 'warning').length,
    };
  }

  private isManualOverride(existing: InvoiceTax, update: Partial<InvoiceTax>): boolean {
    const significantFields = ['rate', 'taxAmount', 'calculationBase', 'cstCode', 'csosnCode'];
    return significantFields.some(field =>
      update[field] !== undefined && update[field] !== existing[field]
    );
  }

  private async getInvoiceWithItems(invoiceId: string): Promise<Invoice> {
    // This would be implemented in the InvoiceService
    // Placeholder for the actual implementation
    throw new Error('Method should be implemented in InvoiceService');
  }

  private isServiceInvoice(invoice: Invoice): boolean {
    // Logic to determine if invoice is for services
    // Could check CFOP codes, product types, etc.
    return false; // Placeholder
  }

  private async calculateISSTax(invoice: Invoice): Promise<InvoiceTax | null> {
    // ISS calculation logic for service invoices
    // Placeholder implementation
    return null;
  }
}

// Supporting interfaces
interface InvoiceTaxSummary {
  totalTaxAmount: number;
  taxesByType: Record<string, TaxTypeSummary>;
  hasManualOverrides: boolean;
  exemptTaxes: ExemptTax[];
}

interface TaxTypeSummary {
  taxType: TaxType;
  totalBase: number;
  totalAmount: number;
  averageRate: number;
  count: number;
}

interface ExemptTax {
  taxType: TaxType;
  reason?: string;
}

interface TaxComplianceResult {
  isCompliant: boolean;
  issues: TaxComplianceIssue[];
  totalIssues: number;
  errorCount: number;
  warningCount: number;
}

interface TaxComplianceIssue {
  type: 'missing_tax' | 'missing_cst' | 'zero_tax_amount' | 'invalid_rate';
  taxType: TaxType;
  message: string;
  severity: 'error' | 'warning';
}
```

#### Invoice Service
```typescript
@Injectable()
export class InvoiceService {
  async create(emitterId: string, createInvoiceDto: CreateInvoiceDto): Promise<Invoice>;
  async createFromEntities(emitterId: string, dto: CreateInvoiceFromEntitiesDto): Promise<Invoice>;
  async findAll(emitterId: string, filters: InvoiceFiltersDto): Promise<PaginatedResult<Invoice>>;
  async findOne(id: string, emitterId: string): Promise<Invoice>;
  async approve(id: string, emitterId: string, userId: string): Promise<Invoice>;
  async reject(id: string, emitterId: string, userId: string, reason: string): Promise<Invoice>;
  async transmitToSefaz(id: string): Promise<Invoice>;
  async cancel(id: string, emitterId: string, reason: string): Promise<Invoice>;
  async generateXml(invoice: Invoice): Promise<string>;
  async calculateTaxes(items: InvoiceItemDto[], emitter: Emitter): Promise<TaxCalculationResult>;
}
```

## API Design Standards

### 1. RESTful Conventions
- Use plural nouns for resource endpoints
- Follow HTTP method semantics (GET, POST, PUT, PATCH, DELETE)
- Use appropriate HTTP status codes
- Implement consistent error response format

### 2. DTO Validation
```typescript
export class CreateClientDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  tradeName?: string;

  @IsEnum(DocumentType)
  documentType: DocumentType;

  @IsString()
  @Matches(/^\d{11}$|^\d{14}$/) // CPF or CNPJ format
  documentNumber: string;

  @IsEmail()
  email: string;

  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;
}
```

### 3. Response Format
```typescript
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
  };
}
```

## Security Implementation

### 1. Authentication Guards
```typescript
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any) {
    if (err || !user) {
      throw err || new UnauthorizedException();
    }
    return user;
  }
}
```

### 2. Authorization Guards
```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

### 3. Data Encryption
```typescript
@Injectable()
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key: Buffer;

  constructor(@Inject('ENCRYPTION_KEY') encryptionKey: string) {
    this.key = Buffer.from(encryptionKey, 'hex');
  }

  encrypt(text: string): EncryptedData {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('ez-nfe', 'utf8'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decrypt(encryptedData: EncryptedData): string {
    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('ez-nfe', 'utf8'));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

## Background Jobs Implementation

### 1. Queue Configuration
```typescript
@Module({
  imports: [
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT),
      },
    }),
    BullModule.registerQueue(
      { name: 'invoice-processing' },
      { name: 'email-notifications' },
      { name: 'certificate-monitoring' },
    ),
  ],
})
export class QueueModule {}
```

### 2. Job Processors
```typescript
@Processor('invoice-processing')
export class InvoiceProcessor {
  constructor(
    private readonly invoiceService: InvoiceService,
    private readonly hermesService: HermesService,
  ) {}

  @Process('transmit-to-sefaz')
  async handleInvoiceTransmission(job: Job<TransmitInvoiceJobData>) {
    const { invoiceId } = job.data;
    
    try {
      await job.updateProgress(10);
      const invoice = await this.invoiceService.findOne(invoiceId);
      
      await job.updateProgress(30);
      const xmlContent = await this.invoiceService.generateXml(invoice);
      
      await job.updateProgress(50);
      const result = await this.hermesService.transmitInvoice({
        invoiceId,
        xmlContent,
        certificate: invoice.emitter.activeCertificate,
      });
      
      await job.updateProgress(80);
      await this.invoiceService.updateTransmissionResult(invoiceId, result);
      
      await job.updateProgress(100);
      
      return { success: true, result };
    } catch (error) {
      throw new Error(`Failed to transmit invoice: ${error.message}`);
    }
  }
}
```

## Testing Strategy

### 1. Unit Tests
- Test all service methods with mocked dependencies
- Use Jest testing framework
- Achieve minimum 80% code coverage
- Mock external dependencies (database, HTTP clients)

### 2. Integration Tests
- Test complete request/response cycles
- Use test database with transactions
- Test authentication and authorization flows
- Validate business logic integration

### 3. E2E Tests
- Test complete user workflows
- Use separate test environment
- Include API endpoint testing
- Validate cross-module interactions

## Performance Considerations

### 1. Database Optimization
- Implement proper indexing strategy
- Use database connection pooling
- Implement query optimization
- Use pagination for large datasets

### 2. Caching Strategy
- Implement Redis caching for frequently accessed data
- Cache user sessions and permissions
- Cache tax calculation results
- Implement cache invalidation strategies

### 3. API Rate Limiting
```typescript
@Injectable()
export class ThrottlerGuard extends ThrottlerGuard {
  protected getTracker(req: Record<string, any>): string {
    return req.user?.id || req.ip;
  }
}
```

## Monitoring and Logging

### 1. Structured Logging
```typescript
@Injectable()
export class LoggerService {
  private readonly logger = new Logger(LoggerService.name);

  logApiRequest(req: Request, res: Response, responseTime: number) {
    this.logger.log({
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
    });
  }

  logBusinessEvent(event: string, data: any, userId?: string) {
    this.logger.log({
      event,
      data,
      userId,
      timestamp: new Date().toISOString(),
    });
  }
}
```

### 2. Health Checks
```typescript
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private redis: RedisHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.redis.pingCheck('redis'),
    ]);
  }
}
```

## Deployment and DevOps

### 1. Docker Configuration
```dockerfile
# Dockerfile
FROM node:22-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:22-alpine AS production

WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./

EXPOSE 3000
CMD ["node", "dist/main"]
```

### 2. Environment Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:17
    environment:
      POSTGRES_DB: eznfe_production
      POSTGRES_USER: eznfe_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 3. CI/CD Pipeline
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v4
        with:
          node-version: '22'
      - run: npm ci
      - run: npm run test:cov
      - run: npm run test:e2e

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: |
          # Deployment commands
```

This technical specification provides a comprehensive foundation for AI agents to implement the ez-NFe backend following NestJS best practices and industry standards.
