# Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the ez-NFe backend. The testing approach follows the testing pyramid with unit tests at the base, integration tests in the middle, and end-to-end tests at the top.

## Testing Pyramid

```
    /\
   /  \     E2E Tests (15%)
  /____\    - Complete user workflows
 /      \   - Cross-module integration
/________\  Integration Tests (25%)
           - API endpoint testing (100% coverage)
           - Service integration
           - Database operations
___________
           Unit Tests (60%)
           - Service methods
           - Utility functions
           - Business logic
```

## Testing Coverage Requirements

### **100% Endpoint Coverage Mandate**
- **Every API endpoint** must have comprehensive test coverage
- **All HTTP methods** (GET, POST, PUT, PATCH, DELETE) must be tested
- **All response codes** (2xx, 4xx, 5xx) must be validated
- **All authentication scenarios** (authenticated, unauthenticated, insufficient permissions)
- **All input validation paths** (valid data, invalid data, edge cases)

### **User Flow Testing Strategy**
- **Happy Path Flows**: Complete successful user journeys from start to finish
- **Unhappy Path Flows**: Error scenarios, validation failures, and edge cases
- **Mixed Scenarios**: Partial success, recovery flows, and complex state transitions

## Testing Framework Setup

### 1. Jest Configuration
```javascript
// jest.config.js
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/*.e2e-spec.ts',
    '!**/node_modules/**',
    '!**/dist/**',
  ],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/test/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    // Endpoint coverage must be 100%
    './src/**/*controller.ts': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100,
    },
  },
};
```

### 2. Test Setup
```typescript
// src/test/setup.ts
import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

// Global test setup
beforeAll(async () => {
  // Setup test database connection
  process.env.NODE_ENV = 'test';
  process.env.DB_NAME = 'eznfe_test';
});

afterAll(async () => {
  // Cleanup after all tests
});

// Test utilities
export const createTestingModule = async (providers: any[] = []) => {
  return Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({ isGlobal: true }),
      TypeOrmModule.forRoot({
        type: 'sqlite',
        database: ':memory:',
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        synchronize: true,
      }),
    ],
    providers,
  }).compile();
};
```

## Unit Testing

### 1. Service Testing
```typescript
// src/auth/auth.service.spec.ts
describe('AuthService', () => {
  let service: AuthService;
  let userService: jest.Mocked<UserService>;
  let jwtService: jest.Mocked<JwtService>;
  let passwordService: jest.Mocked<PasswordService>;

  beforeEach(async () => {
    const mockUserService = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      findById: jest.fn(),
    };

    const mockJwtService = {
      sign: jest.fn(),
      verify: jest.fn(),
    };

    const mockPasswordService = {
      hash: jest.fn(),
      verify: jest.fn(),
    };

    const module = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: UserService, useValue: mockUserService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: PasswordService, useValue: mockPasswordService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userService = module.get(UserService);
    jwtService = module.get(JwtService);
    passwordService = module.get(PasswordService);
  });

  describe('signUp', () => {
    it('should create a new user successfully', async () => {
      // Arrange
      const signUpDto = {
        email: '<EMAIL>',
        password: 'password123',
        accountName: 'Test Account',
      };

      const hashedPassword = 'hashed_password';
      const userId = 'user-uuid';
      const accessToken = 'access_token';
      const refreshToken = 'refresh_token';

      userService.findByEmail.mockResolvedValue(null);
      passwordService.hash.mockResolvedValue(hashedPassword);
      userService.create.mockResolvedValue({ id: userId } as any);
      jwtService.sign.mockReturnValueOnce(accessToken).mockReturnValueOnce(refreshToken);

      // Act
      const result = await service.signUp(signUpDto);

      // Assert
      expect(userService.findByEmail).toHaveBeenCalledWith(signUpDto.email);
      expect(passwordService.hash).toHaveBeenCalledWith(signUpDto.password);
      expect(userService.create).toHaveBeenCalledWith({
        email: signUpDto.email,
        passwordHash: hashedPassword,
        account: { name: signUpDto.accountName },
      });
      expect(result).toEqual({
        accessToken,
        refreshToken,
        user: { id: userId },
      });
    });

    it('should throw ConflictException if user already exists', async () => {
      // Arrange
      const signUpDto = {
        email: '<EMAIL>',
        password: 'password123',
        accountName: 'Test Account',
      };

      userService.findByEmail.mockResolvedValue({ id: 'existing-user' } as any);

      // Act & Assert
      await expect(service.signUp(signUpDto)).rejects.toThrow(ConflictException);
      expect(userService.findByEmail).toHaveBeenCalledWith(signUpDto.email);
      expect(passwordService.hash).not.toHaveBeenCalled();
    });
  });

  describe('signIn', () => {
    it('should authenticate user with valid credentials', async () => {
      // Arrange
      const signInDto = { email: '<EMAIL>', password: 'password123' };
      const user = {
        id: 'user-uuid',
        email: '<EMAIL>',
        passwordHash: 'hashed_password',
        isActive: true,
      };
      const accessToken = 'access_token';
      const refreshToken = 'refresh_token';

      userService.findByEmail.mockResolvedValue(user as any);
      passwordService.verify.mockResolvedValue(true);
      jwtService.sign.mockReturnValueOnce(accessToken).mockReturnValueOnce(refreshToken);

      // Act
      const result = await service.signIn(signInDto);

      // Assert
      expect(userService.findByEmail).toHaveBeenCalledWith(signInDto.email);
      expect(passwordService.verify).toHaveBeenCalledWith(signInDto.password, user.passwordHash);
      expect(result).toEqual({
        accessToken,
        refreshToken,
        user: { id: user.id, email: user.email },
      });
    });

    it('should throw UnauthorizedException for invalid credentials', async () => {
      // Arrange
      const signInDto = { email: '<EMAIL>', password: 'wrong_password' };
      const user = {
        id: 'user-uuid',
        passwordHash: 'hashed_password',
        isActive: true,
      };

      userService.findByEmail.mockResolvedValue(user as any);
      passwordService.verify.mockResolvedValue(false);

      // Act & Assert
      await expect(service.signIn(signInDto)).rejects.toThrow(UnauthorizedException);
    });
  });
});
```

### 2. Controller Testing
```typescript
// src/invoices/invoices.controller.spec.ts
describe('InvoicesController', () => {
  let controller: InvoicesController;
  let service: jest.Mocked<InvoicesService>;

  beforeEach(async () => {
    const mockService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      cancel: jest.fn(),
    };

    const module = await Test.createTestingModule({
      controllers: [InvoicesController],
      providers: [
        { provide: InvoicesService, useValue: mockService },
      ],
    }).compile();

    controller = module.get<InvoicesController>(InvoicesController);
    service = module.get(InvoicesService);
  });

  describe('create', () => {
    it('should create an invoice successfully', async () => {
      // Arrange
      const createInvoiceDto = {
        clientId: 'client-uuid',
        items: [
          {
            productId: 'product-uuid',
            quantity: 2,
            unitPrice: 100,
          },
        ],
      };
      const user = { accountId: 'account-uuid', emitterId: 'emitter-uuid' };
      const expectedInvoice = { id: 'invoice-uuid', number: 1001 };

      service.create.mockResolvedValue(expectedInvoice as any);

      // Act
      const result = await controller.create(createInvoiceDto, user);

      // Assert
      expect(service.create).toHaveBeenCalledWith(user.emitterId, createInvoiceDto);
      expect(result).toEqual(expectedInvoice);
    });
  });

  describe('findAll', () => {
    it('should return paginated invoices', async () => {
      // Arrange
      const paginationDto = { page: 1, limit: 20 };
      const user = { accountId: 'account-uuid', emitterId: 'emitter-uuid' };
      const expectedResult = {
        data: [{ id: 'invoice-1' }, { id: 'invoice-2' }],
        meta: { total: 2, page: 1, limit: 20 },
      };

      service.findAll.mockResolvedValue(expectedResult as any);

      // Act
      const result = await controller.findAll(null, paginationDto, user);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(user.accountId, null, paginationDto);
      expect(result).toEqual(expectedResult);
    });
  });
});
```

### 3. Utility Function Testing
```typescript
// src/common/utils/validation.utils.spec.ts
describe('ValidationUtils', () => {
  describe('isValidCnpj', () => {
    it('should return true for valid CNPJ', () => {
      const validCnpjs = [
        '**************',
        '**************',
      ];

      validCnpjs.forEach(cnpj => {
        expect(isValidCnpj(cnpj)).toBe(true);
      });
    });

    it('should return false for invalid CNPJ', () => {
      const invalidCnpjs = [
        '**************', // All same digits
        '*************',  // Wrong length
        '**************', // Wrong check digit
        '',               // Empty string
        null,             // Null value
      ];

      invalidCnpjs.forEach(cnpj => {
        expect(isValidCnpj(cnpj)).toBe(false);
      });
    });
  });

  describe('calculateTaxes', () => {
    it('should calculate ICMS correctly', () => {
      const item = {
        value: 1000,
        icmsRate: 18,
        icmsOrigin: 0,
        icmsCst: '00',
      };

      const result = calculateTaxes(item);

      expect(result.icmsBase).toBe(1000);
      expect(result.icmsAmount).toBe(180);
    });

    it('should handle tax exemption', () => {
      const item = {
        value: 1000,
        icmsRate: 0,
        icmsOrigin: 0,
        icmsCst: '40', // Tax exempt
      };

      const result = calculateTaxes(item);

      expect(result.icmsBase).toBe(0);
      expect(result.icmsAmount).toBe(0);
    });
  });
});
```

## Integration Testing

### 1. Database Integration
```typescript
// src/invoices/invoices.service.integration.spec.ts
describe('InvoicesService Integration', () => {
  let service: InvoicesService;
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [__dirname + '/../**/*.entity{.ts,.js}'],
          synchronize: true,
        }),
        InvoicesModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    service = app.get<InvoicesService>(InvoicesService);
    dataSource = app.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean database before each test
    await dataSource.synchronize(true);
    
    // Seed test data
    await seedTestData(dataSource);
  });

  describe('create', () => {
    it('should create invoice with items and calculate totals', async () => {
      // Arrange
      const createInvoiceDto = {
        clientId: 'test-client-uuid',
        paymentTermId: 'test-payment-term-uuid',
        items: [
          {
            productId: 'test-product-uuid',
            quantity: 2,
            unitPrice: 100,
            discount: 10,
          },
        ],
      };

      // Act
      const result = await service.create('test-emitter-uuid', createInvoiceDto);

      // Assert
      expect(result.id).toBeDefined();
      expect(result.items).toHaveLength(1);
      expect(result.subtotal).toBe(200);
      expect(result.totalAmount).toBe(190); // After discount
      
      // Verify database persistence
      const savedInvoice = await dataSource
        .getRepository(Invoice)
        .findOne({
          where: { id: result.id },
          relations: ['items'],
        });
      
      expect(savedInvoice).toBeDefined();
      expect(savedInvoice.items).toHaveLength(1);
    });

    it('should throw error for non-existent client', async () => {
      // Arrange
      const createInvoiceDto = {
        clientId: 'non-existent-client',
        paymentTermId: 'test-payment-term-uuid',
        items: [],
      };

      // Act & Assert
      await expect(
        service.create('test-emitter-uuid', createInvoiceDto)
      ).rejects.toThrow('Client not found');
    });
  });

  describe('findAll', () => {
    it('should return paginated results with filters', async () => {
      // Arrange
      await createTestInvoices(dataSource, 25); // Create 25 test invoices
      
      const filters = {
        status: InvoiceStatus.AUTHORIZED,
        page: 2,
        limit: 10,
      };

      // Act
      const result = await service.findAll('test-account-uuid', null, filters);

      // Assert
      expect(result.data).toHaveLength(10);
      expect(result.meta.page).toBe(2);
      expect(result.meta.total).toBeGreaterThan(0);
      expect(result.meta.totalPages).toBeGreaterThan(1);
    });
  });
});

// Test data seeding helper
async function seedTestData(dataSource: DataSource) {
  const account = dataSource.getRepository(Account).create({
    id: 'test-account-uuid',
    name: 'Test Account',
  });
  await dataSource.getRepository(Account).save(account);

  const emitter = dataSource.getRepository(Emitter).create({
    id: 'test-emitter-uuid',
    accountId: 'test-account-uuid',
    name: 'Test Emitter',
    cnpj: '**************',
  });
  await dataSource.getRepository(Emitter).save(emitter);

  // Seed other required entities...
}
```

### 2. Service Integration
```typescript
// src/certificates/certificates.service.integration.spec.ts
describe('CertificatesService Integration', () => {
  let service: CertificatesService;
  let encryptionService: CertificateEncryptionService;
  let validationService: CertificateValidationService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        CertificatesService,
        CertificateEncryptionService,
        CertificateValidationService,
        {
          provide: getRepositoryToken(Certificate),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<CertificatesService>(CertificatesService);
    encryptionService = module.get<CertificateEncryptionService>(CertificateEncryptionService);
    validationService = module.get<CertificateValidationService>(CertificateValidationService);
  });

  describe('upload', () => {
    it('should validate, encrypt, and store certificate', async () => {
      // Arrange
      const pfxBuffer = Buffer.from('mock-pfx-content');
      const password = 'certificate-password';
      const emitterId = 'test-emitter-uuid';

      const mockCertInfo = {
        subjectName: 'Test Certificate',
        validFrom: new Date(),
        validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        cnpj: '**************',
      };

      jest.spyOn(validationService, 'validateCertificate')
        .mockResolvedValue(mockCertInfo);

      // Act
      const result = await service.upload(emitterId, pfxBuffer, password);

      // Assert
      expect(validationService.validateCertificate).toHaveBeenCalledWith(pfxBuffer, password);
      expect(result.status).toBe(CertificateStatus.PENDING);
      expect(result.subjectName).toBe(mockCertInfo.subjectName);
    });
  });
});
```

## End-to-End Testing

### 1. Complete Endpoint Coverage Testing

#### Endpoint Coverage Matrix
Every endpoint must be tested with the following scenarios:

```typescript
// test/coverage/endpoint-coverage.matrix.ts
export const ENDPOINT_COVERAGE_MATRIX = {
  'POST /auth/signup': {
    happyPath: ['valid_data', 'email_verification'],
    unhappyPath: ['invalid_email', 'weak_password', 'duplicate_email', 'missing_fields'],
    edgeCases: ['special_characters', 'long_inputs', 'sql_injection_attempts']
  },
  'POST /invoices': {
    happyPath: ['entity_referenced', 'direct_data', 'with_carrier', 'without_carrier'],
    unhappyPath: ['invalid_client', 'invalid_product', 'insufficient_permissions', 'validation_errors'],
    edgeCases: ['zero_amount', 'negative_quantity', 'missing_tax_config', 'expired_certificate']
  },
  // ... matrix for all endpoints
};
```

### 2. API Endpoint Testing
```typescript
// test/invoices.e2e-spec.ts
describe('Invoices API - Complete Coverage (e2e)', () => {
  let app: INestApplication;
  let accessToken: string;
  let testEmitterId: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    // Setup test user and get access token
    const authResponse = await request(app.getHttpServer())
      .post('/auth/signup')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        accountName: 'Test Account',
      })
      .expect(201);

    accessToken = authResponse.body.data.accessToken;

    // Create test emitter
    const emitterResponse = await request(app.getHttpServer())
      .post('/emitters')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        name: 'Test Company',
        cnpj: '**************',
        email: '<EMAIL>',
        // ... other required fields
      })
      .expect(201);

    testEmitterId = emitterResponse.body.data.id;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /invoices - Complete Coverage', () => {
    describe('Happy Path Scenarios', () => {
      it('should create invoice with entity references', async () => {
        const createInvoiceDto = {
          clientId: 'test-client-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items: [
            {
              productId: 'test-product-uuid',
              quantity: 2,
              unitPrice: 100.50,
            },
          ],
          observations: 'Test invoice',
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createInvoiceDto)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data.id).toBeDefined();
        expect(response.body.data.status).toBe('draft');
        expect(response.body.data.totalAmount).toBe(201.00);
      });

      it('should create invoice with carrier', async () => {
        const createInvoiceDto = {
          clientId: 'test-client-uuid',
          carrierId: 'test-carrier-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items: [{ productId: 'test-product-uuid', quantity: 1, unitPrice: 100 }],
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createInvoiceDto)
          .expect(201);

        expect(response.body.data.carrierId).toBe('test-carrier-uuid');
      });

      it('should create invoice with multiple items', async () => {
        const createInvoiceDto = {
          clientId: 'test-client-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items: [
            { productId: 'test-product-1', quantity: 2, unitPrice: 100 },
            { productId: 'test-product-2', quantity: 1, unitPrice: 50 },
          ],
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createInvoiceDto)
          .expect(201);

        expect(response.body.data.items).toHaveLength(2);
        expect(response.body.data.totalAmount).toBe(250);
      });
    });

    describe('Unhappy Path Scenarios', () => {
      it('should return 400 for missing required fields', async () => {
        const invalidDto = { items: [] };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(invalidDto)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('VALIDATION_ERROR');
        expect(response.body.error.details).toContainEqual({
          field: 'clientId',
          message: expect.stringContaining('required')
        });
      });

      it('should return 404 for non-existent client', async () => {
        const createInvoiceDto = {
          clientId: 'non-existent-client-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items: [{ productId: 'test-product-uuid', quantity: 1, unitPrice: 100 }],
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createInvoiceDto)
          .expect(404);

        expect(response.body.error.code).toBe('RESOURCE_NOT_FOUND');
        expect(response.body.error.message).toContain('Client not found');
      });

      it('should return 403 for insufficient permissions', async () => {
        const viewerToken = await getTokenForRole('viewer');

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${viewerToken}`)
          .send(validInvoiceDto)
          .expect(403);

        expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');
      });

      it('should return 401 without authentication', async () => {
        const response = await request(app.getHttpServer())
          .post('/invoices')
          .send(validInvoiceDto)
          .expect(401);

        expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
      });

      it('should return 422 for invalid business logic', async () => {
        const invalidDto = {
          clientId: 'test-client-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items: [{ productId: 'test-product-uuid', quantity: -1, unitPrice: 100 }],
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(invalidDto)
          .expect(422);

        expect(response.body.error.code).toBe('BUSINESS_RULE_VIOLATION');
        expect(response.body.error.message).toContain('Quantity must be positive');
      });
    });

    describe('Edge Cases', () => {
      it('should handle zero amount items', async () => {
        const createInvoiceDto = {
          clientId: 'test-client-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items: [{ productId: 'test-product-uuid', quantity: 1, unitPrice: 0 }],
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createInvoiceDto)
          .expect(201);

        expect(response.body.data.totalAmount).toBe(0);
      });

      it('should handle maximum allowed items', async () => {
        const items = Array.from({ length: 100 }, (_, i) => ({
          productId: `test-product-${i}`,
          quantity: 1,
          unitPrice: 10
        }));

        const createInvoiceDto = {
          clientId: 'test-client-uuid',
          paymentTermId: 'test-payment-term-uuid',
          items,
        };

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(createInvoiceDto)
          .expect(201);

        expect(response.body.data.items).toHaveLength(100);
      });

      it('should reject too many items', async () => {
        const items = Array.from({ length: 101 }, (_, i) => ({
          productId: `test-product-${i}`,
          quantity: 1,
          unitPrice: 10
        }));

        const response = await request(app.getHttpServer())
          .post('/invoices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({ clientId: 'test-client-uuid', paymentTermId: 'test-payment-term-uuid', items })
          .expect(400);

        expect(response.body.error.code).toBe('VALIDATION_ERROR');
      });
    });
  });

  describe('GET /invoices', () => {
    it('should return paginated invoices', async () => {
      const response = await request(app.getHttpServer())
        .get('/invoices?page=1&limit=10')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.meta.pagination).toBeDefined();
    });

    it('should filter invoices by status', async () => {
      const response = await request(app.getHttpServer())
        .get('/invoices?status=authorized')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.forEach(invoice => {
        expect(invoice.status).toBe('authorized');
      });
    });
  });
});
```

### 3. Complete User Flow Testing

#### User Flow Categories
```typescript
// test/flows/user-flows.types.ts
export enum UserFlowCategory {
  ONBOARDING = 'onboarding',
  INVOICE_MANAGEMENT = 'invoice_management',
  ENTITY_MANAGEMENT = 'entity_management',
  CERTIFICATE_MANAGEMENT = 'certificate_management',
  USER_MANAGEMENT = 'user_management',
  ERROR_RECOVERY = 'error_recovery'
}

export interface UserFlowTest {
  category: UserFlowCategory;
  name: string;
  description: string;
  happyPath: boolean;
  steps: FlowStep[];
  expectedOutcome: string;
  rollbackSteps?: FlowStep[];
}
```

### 4. Happy Path User Flows
```typescript
// test/flows/happy-paths.e2e-spec.ts
describe('Happy Path User Flows (e2e)', () => {
  describe('Complete Onboarding Flow', () => {
    it('should complete new user onboarding successfully', async () => {
      // 1. User signup
      const signupResponse = await request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          accountName: 'New Company Ltd'
        })
        .expect(201);

      const { accessToken } = signupResponse.body.data;

      // 2. Create first emitter
      const emitterResponse = await request(app.getHttpServer())
        .post('/emitters')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(validEmitterData)
        .expect(201);

      const emitterId = emitterResponse.body.data.id;

      // 3. Upload certificate
      const certificateResponse = await request(app.getHttpServer())
        .post('/certificates')
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('certificate', certificateBuffer, 'cert.pfx')
        .field('password', 'cert-password')
        .expect(201);

      // 4. Create first client
      const clientResponse = await request(app.getHttpServer())
        .post('/clients')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(validClientData)
        .expect(201);

      // 5. Create first product
      const productResponse = await request(app.getHttpServer())
        .post('/products')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(validProductData)
        .expect(201);

      // 6. Create first invoice
      const invoiceResponse = await request(app.getHttpServer())
        .post('/invoices')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          clientId: clientResponse.body.data.id,
          paymentTermId: defaultPaymentTermId,
          items: [{
            productId: productResponse.body.data.id,
            quantity: 1,
            unitPrice: 100
          }]
        })
        .expect(201);

      // Verify complete setup
      expect(invoiceResponse.body.data.status).toBe('draft');
      expect(invoiceResponse.body.data.totalAmount).toBe(100);
    });
  });

  describe('Complete Invoice Lifecycle Flow', () => {
    it('should complete full invoice workflow', async () => {
      // 1. Create invoice
      const createResponse = await request(app.getHttpServer())
        .post('/invoices')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(testData.createInvoiceDto)
        .expect(201);

      const invoiceId = createResponse.body.data.id;
      expect(createResponse.body.data.status).toBe('draft');

      // 2. Approve invoice
      const approveResponse = await request(app.getHttpServer())
        .post(`/invoices/${invoiceId}/approve`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(approveResponse.body.data.status).toBe('approved');

      // 3. Transmit to SEFAZ (mocked)
      const transmitResponse = await request(app.getHttpServer())
        .post(`/invoices/${invoiceId}/transmit`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(transmitResponse.body.data.status).toBe('transmitted');

      // 4. Check authorization status
      const statusResponse = await request(app.getHttpServer())
        .get(`/invoices/${invoiceId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(statusResponse.body.data.accessKey).toBeDefined();
      expect(statusResponse.body.data.protocol).toBeDefined();
      expect(statusResponse.body.data.status).toBe('authorized');

      // 5. Download XML and PDF
      const xmlResponse = await request(app.getHttpServer())
        .get(`/invoices/${invoiceId}/xml`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      const pdfResponse = await request(app.getHttpServer())
        .get(`/invoices/${invoiceId}/pdf`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(xmlResponse.headers['content-type']).toContain('application/xml');
      expect(pdfResponse.headers['content-type']).toContain('application/pdf');
    });
  });

  describe('Multi-User Collaboration Flow', () => {
    it('should handle multi-user invoice workflow', async () => {
      // 1. Member creates invoice
      const memberToken = await getTokenForRole('member');
      const createResponse = await request(app.getHttpServer())
        .post('/invoices')
        .set('Authorization', `Bearer ${memberToken}`)
        .send(validInvoiceData)
        .expect(201);

      const invoiceId = createResponse.body.data.id;

      // 2. Admin approves invoice
      const adminToken = await getTokenForRole('admin');
      const approveResponse = await request(app.getHttpServer())
        .post(`/invoices/${invoiceId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // 3. Owner can view all invoices
      const ownerToken = await getTokenForRole('owner');
      const listResponse = await request(app.getHttpServer())
        .get('/invoices')
        .set('Authorization', `Bearer ${ownerToken}`)
        .expect(200);

      expect(listResponse.body.data.some(inv => inv.id === invoiceId)).toBe(true);
    });
  });
});
```

### 5. Unhappy Path User Flows
```typescript
// test/flows/unhappy-paths.e2e-spec.ts
describe('Unhappy Path User Flows (e2e)', () => {
  describe('Failed Onboarding Scenarios', () => {
    it('should handle signup with existing email', async () => {
      // 1. First user signs up successfully
      await request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          accountName: 'First Company'
        })
        .expect(201);

      // 2. Second user tries same email
      const duplicateResponse = await request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: '<EMAIL>',
          password: 'AnotherPass123!',
          accountName: 'Second Company'
        })
        .expect(409);

      expect(duplicateResponse.body.error.code).toBe('DUPLICATE_RESOURCE');
      expect(duplicateResponse.body.error.message).toContain('Email already exists');
    });

    it('should handle permission escalation attempts', async () => {
      // 1. Create two separate accounts
      const account1Token = await createAccountAndGetToken('<EMAIL>');
      const account2Token = await createAccountAndGetToken('<EMAIL>');

      // 2. Account 1 creates an invoice
      const invoiceResponse = await request(app.getHttpServer())
        .post('/invoices')
        .set('Authorization', `Bearer ${account1Token}`)
        .send(validInvoiceData)
        .expect(201);

      const invoiceId = invoiceResponse.body.data.id;

      // 3. Account 2 tries to access Account 1's invoice
      const unauthorizedResponse = await request(app.getHttpServer())
        .get(`/invoices/${invoiceId}`)
        .set('Authorization', `Bearer ${account2Token}`)
        .expect(404); // Should not reveal existence

      // 4. Verify Account 1's invoice is unchanged
      const verifyResponse = await request(app.getHttpServer())
        .get(`/invoices/${invoiceId}`)
        .set('Authorization', `Bearer ${account1Token}`)
        .expect(200);

      expect(verifyResponse.body.data.id).toBe(invoiceId);
    });

    it('should handle SEFAZ transmission failures gracefully', async () => {
      // 1. Create and approve invoice
      const invoiceResponse = await request(app.getHttpServer())
        .post('/invoices')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(validInvoiceData)
        .expect(201);

      const invoiceId = invoiceResponse.body.data.id;

      // 2. Mock SEFAZ failure
      mockSefazService.transmitInvoice.mockRejectedValue(new Error('SEFAZ unavailable'));

      // 3. Attempt transmission
      const transmitResponse = await request(app.getHttpServer())
        .post(`/invoices/${invoiceId}/transmit`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(500);

      expect(transmitResponse.body.error.code).toBe('SEFAZ_ERROR');

      // 4. Verify invoice status is updated correctly
      const statusResponse = await request(app.getHttpServer())
        .get(`/invoices/${invoiceId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(statusResponse.body.data.status).toBe('error');
    });
  });
});
```

### 6. Endpoint Coverage Validation
```typescript
// test/coverage/endpoint-coverage.e2e-spec.ts
describe('100% Endpoint Coverage Validation', () => {
  const REQUIRED_ENDPOINTS = [
    'POST /auth/signup', 'POST /auth/signin', 'POST /auth/refresh', 'GET /auth/me',
    'GET /emitters', 'POST /emitters', 'GET /emitters/:id', 'PATCH /emitters/:id',
    'GET /clients', 'POST /clients', 'GET /clients/:id', 'PATCH /clients/:id',
    'GET /products', 'POST /products', 'GET /products/:id', 'PATCH /products/:id',
    'GET /invoices', 'POST /invoices', 'GET /invoices/:id', 'POST /invoices/:id/approve',
    'POST /invoices/:id/transmit', 'POST /invoices/:id/cancel', 'GET /invoices/:id/xml',
    'GET /certificates', 'POST /certificates', 'DELETE /certificates/:id',
    'GET /api-keys', 'POST /api-keys', 'DELETE /api-keys/:id',
    'POST /external/invoices', 'GET /external/invoices/:id'
  ];

  const REQUIRED_STATUS_CODES = [200, 201, 400, 401, 403, 404, 422, 500];

  REQUIRED_ENDPOINTS.forEach(endpoint => {
    describe(`${endpoint} - Complete Coverage`, () => {
      REQUIRED_STATUS_CODES.forEach(statusCode => {
        it(`should handle ${statusCode} response`, async () => {
          const testResult = await testEndpointWithStatusCode(endpoint, statusCode);
          expect(testResult.tested).toBe(true);
        });
      });
    });
  });

  it('should achieve 100% endpoint coverage', async () => {
    const coverageReport = await generateEndpointCoverageReport();
    expect(coverageReport.coveragePercentage).toBe(100);
    expect(coverageReport.untestedEndpoints).toHaveLength(0);
  });
});
```

## Test Data Management

### 1. Test Factories
```typescript
// test/factories/invoice.factory.ts
export class InvoiceFactory {
  static create(overrides: Partial<CreateInvoiceDto> = {}): CreateInvoiceDto {
    return {
      clientId: 'test-client-uuid',
      paymentTermId: 'test-payment-term-uuid',
      items: [
        {
          productId: 'test-product-uuid',
          quantity: 1,
          unitPrice: 100,
        },
      ],
      observations: 'Test invoice',
      ...overrides,
    };
  }

  static createMultiple(count: number): CreateInvoiceDto[] {
    return Array.from({ length: count }, (_, index) =>
      this.create({ observations: `Test invoice ${index + 1}` })
    );
  }
}
```

### 2. Database Seeding
```typescript
// test/seeds/test-data.seed.ts
export async function seedTestData(dataSource: DataSource) {
  // Clear existing data
  await dataSource.synchronize(true);

  // Create test account
  const account = await dataSource.getRepository(Account).save({
    name: 'Test Account',
    subscriptionTier: SubscriptionTier.PREMIUM,
  });

  // Create test emitter
  const emitter = await dataSource.getRepository(Emitter).save({
    accountId: account.id,
    name: 'Test Company',
    cnpj: '**************',
    email: '<EMAIL>',
    // ... other required fields
  });

  // Create test entities
  await seedClients(dataSource, emitter.id);
  await seedProducts(dataSource, emitter.id);
  await seedCarriers(dataSource, emitter.id);
  await seedPaymentTerms(dataSource, emitter.id);

  return { account, emitter };
}
```

## Performance Testing

### 1. Load Testing
```typescript
// test/performance/load.test.ts
describe('Load Testing', () => {
  it('should handle concurrent invoice creation', async () => {
    const concurrentRequests = 50;
    const promises = Array.from({ length: concurrentRequests }, () =>
      request(app.getHttpServer())
        .post('/invoices')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(InvoiceFactory.create())
    );

    const startTime = Date.now();
    const responses = await Promise.all(promises);
    const endTime = Date.now();

    // All requests should succeed
    responses.forEach(response => {
      expect(response.status).toBe(201);
    });

    // Performance assertion
    const avgResponseTime = (endTime - startTime) / concurrentRequests;
    expect(avgResponseTime).toBeLessThan(1000); // Less than 1 second average
  });
});
```

## Test Coverage and Quality

### 1. Coverage Configuration
```typescript
// jest.config.js - Coverage settings
module.exports = {
  // ... other config
  collectCoverageFrom: [
    'src/**/*.(t|j)s',
    '!src/**/*.spec.ts',
    '!src/**/*.e2e-spec.ts',
    '!src/main.ts',
    '!src/**/*.module.ts',
    '!src/**/*.interface.ts',
    '!src/**/*.dto.ts',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    // Controllers must have 100% coverage (all endpoints tested)
    './src/**/*controller.ts': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    // Critical business logic modules
    './src/auth/': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    './src/invoices/': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    './src/certificates/': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100,
    },
  },
};
```

### 2. Test Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "test:integration": "jest --testPathPattern=integration",
    "test:unit": "jest --testPathPattern=spec.ts$",
    "test:performance": "jest --testPathPattern=performance"
  }
}
```

## Test Execution and Coverage Reporting

### 1. Continuous Testing Pipeline
```bash
# Run all tests with coverage
npm run test:coverage

# Run endpoint coverage validation
npm run test:endpoints

# Run user flow tests
npm run test:flows

# Generate comprehensive coverage report
npm run test:coverage:report
```

### 2. Coverage Validation Scripts
```typescript
// scripts/validate-coverage.ts
import { execSync } from 'child_process';
import { readFileSync } from 'fs';

interface CoverageReport {
  total: {
    lines: { pct: number };
    functions: { pct: number };
    branches: { pct: number };
    statements: { pct: number };
  };
  endpoints: {
    tested: number;
    total: number;
    coverage: number;
  };
}

async function validateCoverage() {
  // Run tests and generate coverage
  execSync('npm run test:cov', { stdio: 'inherit' });

  // Read coverage report
  const coverageData = JSON.parse(readFileSync('coverage/coverage-summary.json', 'utf8'));

  // Validate endpoint coverage
  const endpointCoverage = await validateEndpointCoverage();

  // Check thresholds
  const failures = [];

  if (coverageData.total.lines.pct < 95) {
    failures.push(`Line coverage ${coverageData.total.lines.pct}% < 95%`);
  }

  if (endpointCoverage.coverage < 100) {
    failures.push(`Endpoint coverage ${endpointCoverage.coverage}% < 100%`);
  }

  if (failures.length > 0) {
    console.error('Coverage validation failed:');
    failures.forEach(failure => console.error(`- ${failure}`));
    process.exit(1);
  }

  console.log('✅ All coverage thresholds met!');
  console.log(`📊 Lines: ${coverageData.total.lines.pct}%`);
  console.log(`🎯 Endpoints: ${endpointCoverage.coverage}%`);
}

validateCoverage().catch(console.error);
```

### 3. Test Quality Gates
- **Pre-commit**: Run unit tests and linting
- **Pre-push**: Run integration tests and coverage validation
- **CI Pipeline**: Run full test suite including E2E flows
- **Release**: Validate 100% endpoint coverage and user flow completion

### 4. Coverage Reporting
```json
{
  "scripts": {
    "test:coverage": "jest --coverage --coverageReporters=text-lcov --coverageReporters=html",
    "test:endpoints": "jest --testPathPattern=endpoint-coverage",
    "test:flows": "jest --testPathPattern=flows",
    "test:coverage:report": "npm run test:coverage && npm run test:endpoints && npm run test:flows",
    "test:validate": "ts-node scripts/validate-coverage.ts"
  }
}
```

This comprehensive testing strategy ensures:
- **100% endpoint coverage** with all HTTP status codes tested
- **Complete user flow validation** covering happy and unhappy paths
- **High code quality** with strict coverage thresholds
- **Reliable system behavior** through extensive integration testing
- **Maintainable test suite** with clear organization and data management
