# Webhook Documentation

## Overview

Webhooks allow you to receive real-time notifications about events that occur in your ez-NFe account. When an event occurs, we'll send an HTTP POST request to the webhook URL you've configured for your emitter.

## Configuration

Webhooks are configured at the emitter level. Each emitter can have its own webhook URL and secret.

To set up a webhook:

1. Navigate to the emitter settings in the dashboard
2. Enter a webhook URL
3. Optionally, set a webhook secret for signature verification

## Event Types

Currently, the following events trigger webhook notifications:

| Event Type | Description |
|------------|-------------|
| `nfe.issued` | An NF-e has been successfully issued |
| `nfe.error` | An error occurred during NF-e issuance |
| `nfe.cancelled` | An NF-e has been cancelled |
| `certificate.expired` | A certificate has expired |
| `certificate.activated` | A certificate has been activated |

## Payload Format

All webhook payloads are sent as JSON with the following structure:

```json
{
  "event": "nfe.issued",
  "nfeId": "uuid-of-the-nfe",
  "emitterId": "uuid-of-the-emitter",
  "status": "issued",
  "protocol": "protocol-number-if-applicable",
  "timestamp": "2023-01-15T12:00:00Z",
  "data": {
    // Additional event-specific data
  }
}
```

### Event-Specific Data

#### `nfe.issued`

```json
{
  "data": {
    "number": "123",
    "series": "1",
    "value": 1000.00,
    "issueDate": "2023-01-15T12:00:00Z",
    "accessKey": "access-key-of-the-nfe"
  }
}
```

#### `nfe.error`

```json
{
  "data": {
    "errorCode": "error-code",
    "errorMessage": "Detailed error message",
    "rejectionReason": "Reason for rejection if applicable"
  }
}
```

#### `nfe.cancelled`

```json
{
  "data": {
    "cancellationDate": "2023-01-15T12:00:00Z",
    "cancellationReason": "Reason for cancellation",
    "cancellationProtocol": "Cancellation protocol number"
  }
}
```

#### `certificate.expired`

```json
{
  "data": {
    "certificateId": "uuid-of-the-certificate",
    "expirationDate": "2023-01-15T12:00:00Z",
    "serialNumber": "Serial number of the certificate"
  }
}
```

#### `certificate.activated`

```json
{
  "data": {
    "certificateId": "uuid-of-the-certificate",
    "activationDate": "2023-01-15T12:00:00Z",
    "validUntil": "2024-01-15T12:00:00Z"
  }
}
```

## Security

### Signature Verification

To verify that webhook requests are coming from ez-NFe, we include a signature in the `X-Signature` header. This signature is a HMAC SHA-256 hash of the request body, using your webhook secret as the key.

To verify the signature:

1. Get the signature from the `X-Signature` header
2. Create an HMAC SHA-256 hash of the raw request body using your webhook secret
3. Compare the two signatures

Example in Node.js:

```javascript
const crypto = require('crypto');

function verifySignature(payload, signature, secret) {
  const computedSignature = crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(computedSignature)
  );
}

// In your webhook handler
const signature = req.headers['x-signature'];
const isValid = verifySignature(req.body, signature, process.env.WEBHOOK_SECRET);

if (!isValid) {
  return res.status(401).send('Invalid signature');
}
```

## Retry Policy

If your webhook endpoint returns a non-2xx response or times out, we'll retry the webhook delivery with the following policy:

- Maximum of 5 retry attempts
- Exponential backoff between retries:
  - 1st retry: ~1 minute after initial failure
  - 2nd retry: ~5 minutes after 1st retry
  - 3rd retry: ~15 minutes after 2nd retry
  - 4th retry: ~30 minutes after 3rd retry
  - 5th retry: ~60 minutes after 4th retry

After 5 failed attempts, the webhook will be discarded.

## Best Practices

1. **Respond quickly**: Your webhook endpoint should respond within 5 seconds to avoid timeouts
2. **Verify signatures**: Always verify the signature to ensure the request is legitimate
3. **Process asynchronously**: Acknowledge receipt of the webhook immediately, then process it asynchronously
4. **Handle duplicates**: Implement idempotency to handle potential duplicate webhook deliveries
5. **Monitor failures**: Set up monitoring for webhook failures to detect issues early
