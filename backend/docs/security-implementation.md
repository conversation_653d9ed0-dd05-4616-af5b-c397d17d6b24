# Security Implementation Guide

## Overview

This document provides comprehensive security implementation guidelines for the ez-NFe backend. Security is critical for handling sensitive financial data, digital certificates, and ensuring compliance with Brazilian regulations.

## Authentication & Authorization

### 1. JWT Implementation

#### Token Structure
```typescript
interface JwtPayload {
  sub: string;           // User ID
  email: string;         // User email
  accountId: string;     // Current account ID
  emitterId?: string;    // Current emitter ID (optional)
  roles: string[];       // User roles in current account
  iat: number;          // Issued at
  exp: number;          // Expires at
  type: 'access' | 'refresh';
}
```

#### Token Configuration
```typescript
@Injectable()
export class JwtConfigService {
  getJwtConfig(): JwtModuleOptions {
    return {
      secret: process.env.JWT_SECRET,
      signOptions: {
        expiresIn: '15m',        // Access token: 15 minutes
        issuer: 'ez-nfe-api',
        audience: 'ez-nfe-client'
      }
    };
  }

  getRefreshTokenConfig(): JwtSignOptions {
    return {
      secret: process.env.JWT_REFRESH_SECRET,
      expiresIn: '7d',          // Refresh token: 7 days
      issuer: 'ez-nfe-api',
      audience: 'ez-nfe-client'
    };
  }
}
```

#### JWT Strategy Implementation
```typescript
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly userService: UserService,
    private readonly accountService: AccountService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
      issuer: 'ez-nfe-api',
      audience: 'ez-nfe-client'
    });
  }

  async validate(payload: JwtPayload): Promise<AuthenticatedUser> {
    // Validate user exists and is active
    const user = await this.userService.findById(payload.sub);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Validate account access
    const hasAccountAccess = await this.accountService.userHasAccess(
      payload.sub, 
      payload.accountId
    );
    if (!hasAccountAccess) {
      throw new UnauthorizedException('Invalid account access');
    }

    return {
      id: payload.sub,
      email: payload.email,
      accountId: payload.accountId,
      emitterId: payload.emitterId,
      roles: payload.roles
    };
  }
}
```

### 2. Role-Based Access Control (RBAC)

#### Role Definitions
```typescript
export enum Role {
  OWNER = 'owner',         // Full access to account
  ADMIN = 'admin',         // Manage users, emitters, invoices
  MEMBER = 'member',       // Create/edit invoices, manage entities
  VIEWER = 'viewer'        // Read-only access
}

export enum Permission {
  // User management
  MANAGE_USERS = 'manage:users',
  VIEW_USERS = 'view:users',
  
  // Emitter management
  MANAGE_EMITTERS = 'manage:emitters',
  VIEW_EMITTERS = 'view:emitters',
  
  // Invoice management
  CREATE_INVOICES = 'create:invoices',
  APPROVE_INVOICES = 'approve:invoices',
  CANCEL_INVOICES = 'cancel:invoices',
  VIEW_INVOICES = 'view:invoices',
  
  // Entity management
  MANAGE_CLIENTS = 'manage:clients',
  MANAGE_PRODUCTS = 'manage:products',
  MANAGE_CARRIERS = 'manage:carriers',
  
  // Certificate management
  MANAGE_CERTIFICATES = 'manage:certificates',
  VIEW_CERTIFICATES = 'view:certificates',
  
  // API keys
  MANAGE_API_KEYS = 'manage:api_keys'
}
```

#### Permission Matrix
```typescript
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [Role.OWNER]: [
    // All permissions
    ...Object.values(Permission)
  ],
  [Role.ADMIN]: [
    Permission.MANAGE_USERS,
    Permission.VIEW_USERS,
    Permission.MANAGE_EMITTERS,
    Permission.VIEW_EMITTERS,
    Permission.CREATE_INVOICES,
    Permission.APPROVE_INVOICES,
    Permission.CANCEL_INVOICES,
    Permission.VIEW_INVOICES,
    Permission.MANAGE_CLIENTS,
    Permission.MANAGE_PRODUCTS,
    Permission.MANAGE_CARRIERS,
    Permission.MANAGE_CERTIFICATES,
    Permission.VIEW_CERTIFICATES,
    Permission.MANAGE_API_KEYS
  ],
  [Role.MEMBER]: [
    Permission.VIEW_USERS,
    Permission.VIEW_EMITTERS,
    Permission.CREATE_INVOICES,
    Permission.VIEW_INVOICES,
    Permission.MANAGE_CLIENTS,
    Permission.MANAGE_PRODUCTS,
    Permission.MANAGE_CARRIERS,
    Permission.VIEW_CERTIFICATES
  ],
  [Role.VIEWER]: [
    Permission.VIEW_USERS,
    Permission.VIEW_EMITTERS,
    Permission.VIEW_INVOICES,
    Permission.VIEW_CERTIFICATES
  ]
};
```

#### Guards Implementation
```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>('permissions', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    const userPermissions = this.getUserPermissions(user.roles);
    
    return requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );
  }

  private getUserPermissions(roles: Role[]): Permission[] {
    return roles.reduce((permissions, role) => {
      return [...permissions, ...ROLE_PERMISSIONS[role]];
    }, [] as Permission[]);
  }
}
```

#### Decorators
```typescript
export const Roles = (...roles: Role[]) => SetMetadata('roles', roles);
export const Permissions = (...permissions: Permission[]) => 
  SetMetadata('permissions', permissions);

// Usage example
@Controller('emitters')
@UseGuards(JwtAuthGuard, RolesGuard)
export class EmittersController {
  @Post()
  @Roles(Role.OWNER, Role.ADMIN)
  create(@Body() createEmitterDto: CreateEmitterDto) {
    // Only owners and admins can create emitters
  }

  @Get()
  @Permissions(Permission.VIEW_EMITTERS)
  findAll() {
    // Users with view emitters permission can access
  }
}
```

## Data Encryption

### 1. Certificate Encryption
```typescript
@Injectable()
export class CertificateEncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16;  // 128 bits
  private readonly tagLength = 16; // 128 bits

  constructor() {
    if (!process.env.CERTIFICATE_ENCRYPTION_KEY) {
      throw new Error('CERTIFICATE_ENCRYPTION_KEY environment variable is required');
    }
  }

  private getKey(): Buffer {
    const key = process.env.CERTIFICATE_ENCRYPTION_KEY;
    return crypto.scryptSync(key, 'ez-nfe-salt', this.keyLength);
  }

  encrypt(data: string): EncryptedData {
    const key = this.getKey();
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipher(this.algorithm, key);
    cipher.setAAD(Buffer.from('certificate', 'utf8'));

    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decrypt(encryptedData: EncryptedData): string {
    const key = this.getKey();
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const authTag = Buffer.from(encryptedData.authTag, 'hex');

    const decipher = crypto.createDecipher(this.algorithm, key);
    decipher.setAAD(Buffer.from('certificate', 'utf8'));
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
```

### 2. Password Hashing
```typescript
@Injectable()
export class PasswordService {
  private readonly saltRounds = 12;

  async hash(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }

  async verify(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  generateSecurePassword(length: number = 16): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }
}
```

## API Security

### 1. Rate Limiting
```typescript
@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  protected async getTracker(req: Record<string, any>): Promise<string> {
    // Use user ID if authenticated, otherwise IP address
    return req.user?.id || req.ip;
  }

  protected async getLimit(context: ExecutionContext): Promise<number> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    // Different limits based on subscription tier
    if (user?.subscriptionTier === 'enterprise') {
      return 10000; // 10k requests per hour
    } else if (user?.subscriptionTier === 'premium') {
      return 5000;  // 5k requests per hour
    } else if (user?.subscriptionTier === 'basic') {
      return 1000;  // 1k requests per hour
    }
    
    return 100; // Free tier: 100 requests per hour
  }
}

// Apply rate limiting
@UseGuards(CustomThrottlerGuard)
@Controller('api')
export class ApiController {
  // Rate limited endpoints
}
```

### 2. API Key Security
```typescript
@Injectable()
export class ApiKeyService {
  private readonly keyLength = 32;
  private readonly prefixLength = 8;

  generateApiKey(): { key: string; hash: string; prefix: string } {
    const key = crypto.randomBytes(this.keyLength).toString('hex');
    const prefix = key.substring(0, this.prefixLength);
    const hash = crypto.createHash('sha256').update(key).digest('hex');

    return { key, hash, prefix };
  }

  async validateApiKey(providedKey: string): Promise<ApiKey | null> {
    const hash = crypto.createHash('sha256').update(providedKey).digest('hex');
    
    const apiKey = await this.apiKeyRepository.findOne({
      where: { 
        keyHash: hash,
        isActive: true,
        expiresAt: MoreThan(new Date())
      },
      relations: ['account', 'emitter']
    });

    if (apiKey) {
      // Update last used timestamp
      await this.apiKeyRepository.update(apiKey.id, {
        lastUsedAt: new Date()
      });
    }

    return apiKey;
  }
}

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private readonly apiKeyService: ApiKeyService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];

    if (!apiKey) {
      throw new UnauthorizedException('API key required');
    }

    const validApiKey = await this.apiKeyService.validateApiKey(apiKey);
    if (!validApiKey) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Attach API key info to request
    request.apiKey = validApiKey;
    return true;
  }
}
```

### 3. Input Validation & Sanitization
```typescript
@Injectable()
export class ValidationPipe extends DefaultValidationPipe {
  constructor() {
    super({
      whitelist: true,           // Strip unknown properties
      forbidNonWhitelisted: true, // Throw error for unknown properties
      transform: true,           // Transform payloads to DTO instances
      transformOptions: {
        enableImplicitConversion: true
      },
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = errors.map(error => ({
          field: error.property,
          message: Object.values(error.constraints || {}).join(', ')
        }));
        
        return new BadRequestException({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: messages
          }
        });
      }
    });
  }
}

// Custom validators
@ValidatorConstraint({ name: 'isCnpj', async: false })
export class IsCnpjConstraint implements ValidatorConstraintInterface {
  validate(cnpj: string): boolean {
    if (!cnpj || cnpj.length !== 14) return false;
    
    // CNPJ validation algorithm
    const digits = cnpj.split('').map(Number);
    
    // First check digit
    let sum = 0;
    const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    for (let i = 0; i < 12; i++) {
      sum += digits[i] * weights1[i];
    }
    const remainder1 = sum % 11;
    const checkDigit1 = remainder1 < 2 ? 0 : 11 - remainder1;
    
    if (digits[12] !== checkDigit1) return false;
    
    // Second check digit
    sum = 0;
    const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    for (let i = 0; i < 13; i++) {
      sum += digits[i] * weights2[i];
    }
    const remainder2 = sum % 11;
    const checkDigit2 = remainder2 < 2 ? 0 : 11 - remainder2;
    
    return digits[13] === checkDigit2;
  }

  defaultMessage(): string {
    return 'Invalid CNPJ format';
  }
}

export function IsCnpj(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsCnpjConstraint,
    });
  };
}
```

## Security Headers & CORS

### 1. Security Headers
```typescript
// main.ts
import helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // CORS configuration
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
    credentials: true,
  });

  await app.listen(3000);
}
```

### 2. Request Logging & Monitoring
```typescript
@Injectable()
export class SecurityLoggerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SecurityLoggerInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Log security-relevant information
        this.logger.log({
          method: request.method,
          url: request.url,
          statusCode: response.statusCode,
          duration,
          userAgent: request.get('User-Agent'),
          ip: request.ip,
          userId: request.user?.id,
          accountId: request.user?.accountId,
          timestamp: new Date().toISOString()
        });

        // Log suspicious activities
        if (response.statusCode === 401 || response.statusCode === 403) {
          this.logger.warn({
            event: 'UNAUTHORIZED_ACCESS_ATTEMPT',
            ip: request.ip,
            userAgent: request.get('User-Agent'),
            url: request.url,
            method: request.method
          });
        }
      }),
      catchError((error) => {
        this.logger.error({
          event: 'REQUEST_ERROR',
          error: error.message,
          stack: error.stack,
          ip: request.ip,
          url: request.url,
          method: request.method
        });
        throw error;
      })
    );
  }
}
```

## Certificate Security

### 1. Certificate Validation
```typescript
@Injectable()
export class CertificateValidationService {
  async validateCertificate(pfxBuffer: Buffer, password: string): Promise<CertificateInfo> {
    try {
      // Parse PFX certificate
      const p12 = forge.pkcs12.pkcs12FromAsn1(
        forge.asn1.fromDer(pfxBuffer.toString('binary'))
      );

      // Verify password
      const bags = forge.pkcs12.getBags(p12, password);
      
      if (!bags.certBags || bags.certBags.length === 0) {
        throw new Error('No certificates found in PFX file');
      }

      const cert = bags.certBags[0].cert;
      
      // Extract certificate information
      const subject = cert.subject.getField('CN')?.value || '';
      const issuer = cert.issuer.getField('CN')?.value || '';
      const serialNumber = cert.serialNumber;
      const validFrom = cert.validity.notBefore;
      const validTo = cert.validity.notAfter;

      // Validate certificate is not expired
      const now = new Date();
      if (now < validFrom || now > validTo) {
        throw new Error('Certificate is expired or not yet valid');
      }

      // Validate certificate is for the correct CNPJ
      const cnpjMatch = subject.match(/\d{14}/);
      if (!cnpjMatch) {
        throw new Error('Certificate does not contain valid CNPJ');
      }

      return {
        subjectName: subject,
        issuerName: issuer,
        serialNumber,
        validFrom,
        validTo,
        federalTaxId: cnpjMatch[0]
      };

    } catch (error) {
      throw new BadRequestException(`Invalid certificate: ${error.message}`);
    }
  }

  async checkCertificateExpiry(): Promise<void> {
    const expiringCertificates = await this.certificateRepository.find({
      where: {
        status: CertificateStatus.VALID,
        validTo: LessThan(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // 30 days
      },
      relations: ['emitter']
    });

    for (const cert of expiringCertificates) {
      // Send notification about expiring certificate
      await this.notificationService.sendCertificateExpiryWarning(cert);
    }
  }
}
```

## Environment Security

### 1. Environment Variables
```bash
# .env.example
# Application
NODE_ENV=production
PORT=3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=eznfe_user
DB_PASSWORD=secure_password
DB_NAME=eznfe_production

# JWT Secrets (use strong, unique values)
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here

# Encryption Keys (use crypto.randomBytes(32).toString('hex'))
CERTIFICATE_ENCRYPTION_KEY=your-32-byte-hex-encryption-key-here

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# External Services
HERMES_API_URL=http://localhost:8000
HERMES_API_KEY=hermes-internal-api-key

# Storage
STORAGE_TYPE=s3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=eznfe-documents

# Security
ALLOWED_ORIGINS=https://app.eznfe.com,https://dashboard.eznfe.com
RATE_LIMIT_TTL=3600
RATE_LIMIT_MAX=1000
```

### 2. Security Checklist
```typescript
// Security validation on startup
@Injectable()
export class SecurityValidationService implements OnModuleInit {
  private readonly logger = new Logger(SecurityValidationService.name);

  onModuleInit() {
    this.validateSecurityConfiguration();
  }

  private validateSecurityConfiguration() {
    const requiredEnvVars = [
      'JWT_SECRET',
      'JWT_REFRESH_SECRET',
      'CERTIFICATE_ENCRYPTION_KEY',
      'DB_PASSWORD'
    ];

    const missingVars = requiredEnvVars.filter(
      varName => !process.env[varName]
    );

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Validate JWT secret strength
    if (process.env.JWT_SECRET.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long');
    }

    // Validate encryption key format
    if (!/^[0-9a-f]{64}$/i.test(process.env.CERTIFICATE_ENCRYPTION_KEY)) {
      throw new Error('CERTIFICATE_ENCRYPTION_KEY must be a 64-character hex string');
    }

    this.logger.log('Security configuration validated successfully');
  }
}
```

This security implementation provides comprehensive protection for the ez-NFe platform, covering authentication, authorization, data encryption, API security, and certificate management.
