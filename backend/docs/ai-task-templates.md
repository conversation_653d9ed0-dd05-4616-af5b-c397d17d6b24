# AI Agent Task Templates

## Overview

This document provides specific task templates that AI agents must follow for different types of development work. Each template includes mandatory steps, documentation references, and validation criteria.

## Template Usage Instructions

1. **Select Appropriate Template**: Choose the template that matches your task type
2. **Follow All Steps**: Complete every step in the specified order
3. **Reference Documentation**: Use the specified documentation for each step
4. **Validate Completion**: Ensure all criteria are met before marking complete

## Task Template 1: Create New Entity Module

### **Prerequisites**
- [ ] Read `backend/docs/database-schema.md` for entity definition
- [ ] Read `backend/docs/technical-specifications.md` for module structure
- [ ] Read `backend/docs/api-design-standards.md` for API patterns

### **Step 1: Entity Analysis**
```
Documentation to Review:
- database-schema.md: Find entity definition and relationships
- Look for foreign key relationships
- Identify constraints and validation rules
- Check for junction tables or address relationships

Questions to Answer:
1. What is the primary entity name?
2. What other entities does it relate to?
3. What are the required vs optional fields?
4. Are there any special business rules?
5. Does it need soft delete capability?
```

### **Step 2: Module Structure Creation**
```
Follow technical-specifications.md module template:

Required Files:
□ [module].module.ts - Module definition
□ controllers/[module].controller.ts - REST API
□ services/[module].service.ts - Business logic
□ entities/[entity].entity.ts - TypeORM entity
□ dto/create-[entity].dto.ts - Creation DTO
□ dto/update-[entity].dto.ts - Update DTO
□ dto/query-[entity].dto.ts - Query DTO
□ dto/response-[entity].dto.ts - Response DTO
□ [module].controller.spec.ts - Controller tests
□ [module].service.spec.ts - Service tests
```

### **Step 3: Entity Implementation**
```
Reference: database-schema.md entity definition

Implementation Checklist:
□ All columns match database schema exactly
□ Proper TypeORM decorators (@Entity, @Column, etc.)
□ Correct relationships (@ManyToOne, @OneToMany, etc.)
□ Validation decorators where appropriate
□ Soft delete fields (deletedAt) if required
□ Base entity extension for common fields
```

### **Step 4: Service Implementation**
```
Reference: technical-specifications.md service patterns

Required Methods:
□ create(dto) - Create new entity
□ findAll(query) - List with pagination/filtering
□ findOne(id) - Get single entity
□ update(id, dto) - Update entity
□ remove(id) - Soft delete entity

Business Logic Requirements:
□ Proper validation before operations
□ Error handling with meaningful messages
□ Transaction handling for complex operations
□ Integration with related services
```

### **Step 5: Controller Implementation**
```
Reference: api-design-standards.md patterns

Required Endpoints:
□ POST /[resource] - Create
□ GET /[resource] - List with pagination
□ GET /[resource]/:id - Get by ID
□ PATCH /[resource]/:id - Update
□ DELETE /[resource]/:id - Delete

Implementation Requirements:
□ Proper HTTP status codes
□ Swagger documentation (@ApiOperation, @ApiResponse)
□ Authentication guards (@UseGuards)
□ Validation pipes for DTOs
□ Consistent response format
```

### **Step 6: DTO Implementation**
```
Reference: api-design-standards.md DTO patterns

DTO Requirements:
□ class-validator decorators for validation
□ Swagger decorators for documentation
□ Proper TypeScript types
□ Optional vs required fields correctly marked
□ Transformation decorators where needed
```

### **Step 7: Testing Implementation**
```
Reference: testing-strategy.md requirements

Test Coverage Required:
□ Unit tests for all service methods
□ Controller tests for all endpoints
□ Integration tests for complete workflows
□ Error scenario testing
□ Edge case testing
□ Minimum 95% code coverage
```

### **Completion Criteria**
- [ ] All files created following module template
- [ ] Entity matches database schema exactly
- [ ] All CRUD operations implemented
- [ ] Full test coverage achieved
- [ ] API documentation complete
- [ ] Code passes linting and validation

## Task Template 2: Add New Feature to Existing Module

### **Prerequisites**
- [ ] Identify target module and review its current structure
- [ ] Read relevant documentation for the feature requirements
- [ ] Understand impact on existing functionality

### **Step 1: Impact Analysis**
```
Analysis Questions:
1. Which existing entities are affected?
2. Are new database fields/tables needed?
3. What API endpoints need modification?
4. What new endpoints are required?
5. How does this affect existing business logic?
6. What tests need updating?
```

### **Step 2: Database Changes**
```
If database changes needed:
□ Create migration script
□ Update entity definitions
□ Update database-schema.md documentation
□ Ensure backward compatibility
□ Test migration on sample data
```

### **Step 3: Service Layer Updates**
```
Service Modifications:
□ Add new methods for feature functionality
□ Update existing methods if needed
□ Maintain backward compatibility
□ Add proper error handling
□ Update validation logic
```

### **Step 4: API Layer Updates**
```
Controller Modifications:
□ Add new endpoints for feature
□ Update existing endpoints if needed
□ Create/update DTOs
□ Update Swagger documentation
□ Maintain API versioning if breaking changes
```

### **Step 5: Testing Updates**
```
Test Updates Required:
□ Add tests for new functionality
□ Update existing tests if behavior changed
□ Add integration tests for new workflows
□ Ensure all edge cases covered
□ Maintain 95%+ coverage
```

### **Completion Criteria**
- [ ] Feature implemented according to requirements
- [ ] No breaking changes to existing functionality
- [ ] All tests pass including new feature tests
- [ ] Documentation updated
- [ ] API documentation reflects changes

## Task Template 3: Fix Bug or Issue

### **Prerequisites**
- [ ] Understand the bug report or issue description
- [ ] Identify affected modules and components
- [ ] Review relevant documentation

### **Step 1: Root Cause Analysis**
```
Investigation Steps:
1. Reproduce the issue in development environment
2. Identify the exact component causing the issue
3. Review related code and business logic
4. Check if issue affects other similar functionality
5. Determine if it's a code bug or design issue
```

### **Step 2: Solution Design**
```
Solution Planning:
□ Identify the minimal fix required
□ Ensure fix doesn't break existing functionality
□ Consider edge cases and side effects
□ Plan testing strategy for the fix
□ Document the solution approach
```

### **Step 3: Implementation**
```
Fix Implementation:
□ Make minimal necessary changes
□ Follow existing code patterns
□ Add proper error handling
□ Update validation if needed
□ Ensure backward compatibility
```

### **Step 4: Testing**
```
Testing Requirements:
□ Test the specific bug scenario
□ Test related functionality
□ Run full test suite to ensure no regressions
□ Add new tests to prevent future occurrences
□ Test edge cases around the fix
```

### **Completion Criteria**
- [ ] Bug is completely resolved
- [ ] No new issues introduced
- [ ] All existing tests still pass
- [ ] New tests added to prevent regression
- [ ] Code follows project standards

## Task Template 4: Database Migration

### **Prerequisites**
- [ ] Review current database schema
- [ ] Understand the required changes
- [ ] Plan for data migration if needed

### **Step 1: Migration Planning**
```
Planning Checklist:
□ Identify all tables/columns affected
□ Plan for data preservation
□ Consider performance impact
□ Plan rollback strategy
□ Identify dependencies
```

### **Step 2: Migration Script Creation**
```
Migration Requirements:
□ Create TypeORM migration file
□ Include both up and down methods
□ Handle data migration if needed
□ Add proper error handling
□ Test on sample data
```

### **Step 3: Entity Updates**
```
Entity Modifications:
□ Update TypeORM entities
□ Update relationships if changed
□ Update validation rules
□ Maintain backward compatibility where possible
```

### **Step 4: Documentation Updates**
```
Documentation Updates:
□ Update database-schema.md
□ Update entity relationship diagrams
□ Document any breaking changes
□ Update API documentation if affected
```

### **Completion Criteria**
- [ ] Migration runs successfully
- [ ] All data preserved correctly
- [ ] Entities match new schema
- [ ] Documentation updated
- [ ] Rollback tested and working

## Validation Commands

After completing any task, run these validation commands:

```bash
# Code quality
npm run lint
npm run format

# Testing
npm run test
npm run test:cov
npm run test:e2e

# Database
npm run migration:run
npm run migration:revert

# Documentation
npm run docs:build
npm run docs:validate
```

## Task Completion Checklist

Every task must meet these criteria:

- [ ] Code follows all documented patterns and standards
- [ ] All tests pass with required coverage
- [ ] Documentation updated where necessary
- [ ] No breaking changes without proper versioning
- [ ] Code passes all linting and formatting checks
- [ ] Integration tests validate end-to-end functionality
- [ ] Error handling is comprehensive and meaningful
- [ ] API documentation is complete and accurate
