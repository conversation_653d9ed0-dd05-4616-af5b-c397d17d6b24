# Database Schema Specification

## Overview

This document defines the complete database schema for the ez-NFe SaaS platform. The schema is designed for PostgreSQL and uses TypeORM as the ORM layer.

## Schema Design Principles

### 1. Naming Conventions
- Table names: snake_case, singular (e.g., `user`, `invoice_item`)
- Column names: snake_case (e.g., `created_at`, `document_number`)
- Foreign keys: `{table_name}_id` (e.g., `user_id`, `emitter_id`)
- Indexes: `idx_{table}_{columns}` (e.g., `idx_invoice_status_created_at`)

### 2. Data Types
- Primary keys: UUID (using `uuid-ossp` extension)
- Timestamps: `timestamp with time zone`
- Monetary values: `decimal(12,2)` for currency amounts
- Text fields: `varchar(255)` for short text, `text` for long content
- Enums: Use PostgreSQL enum types for fixed value sets

### 3. Constraints
- All tables must have primary keys
- Foreign key constraints must be defined
- Unique constraints for business keys
- Check constraints for data validation
- Not null constraints for required fields

## Core Tables

### 1. User Table
```sql
CREATE TABLE user (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_user_email ON user(email);
CREATE INDEX idx_user_active ON user(is_active) WHERE is_active = true;
```

### 2. Account Table
```sql
CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'premium', 'enterprise');

CREATE TABLE account (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subscription_tier subscription_tier DEFAULT 'free',
    is_active BOOLEAN DEFAULT true,
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_account_subscription ON account(subscription_tier, is_active);
```

### 3. User Account Junction Table
```sql
CREATE TYPE user_role AS ENUM ('owner', 'admin', 'member', 'viewer');

CREATE TABLE user_account (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES account(id) ON DELETE CASCADE,
    role user_role NOT NULL DEFAULT 'member',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, account_id)
);

CREATE INDEX idx_user_account_user ON user_account(user_id);
CREATE INDEX idx_user_account_account ON user_account(account_id);
```

### 4. Emitter Table (Business Units)
```sql
CREATE TYPE tax_regime AS ENUM (
    'simples_nacional',
    'lucro_presumido',
    'lucro_real',
    'lucro_arbitrado'
);

CREATE TYPE certificate_status AS ENUM ('pending', 'valid', 'expired', 'revoked');

CREATE TABLE emitter (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES account(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    trade_name VARCHAR(255),
    cnpj VARCHAR(14) UNIQUE NOT NULL,
    state_tax_id VARCHAR(20),
    municipal_tax_id VARCHAR(20),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),

    -- Tax configuration
    tax_regime tax_regime NOT NULL,
    cnae VARCHAR(10),
    legal_nature VARCHAR(10),

    -- Invoice numbering
    current_series INTEGER DEFAULT 1,
    current_number INTEGER DEFAULT 1,

    -- Certificate status
    certificate_status certificate_status DEFAULT 'pending',

    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT chk_cnpj_format CHECK (cnpj ~ '^\d{14}$')
);

CREATE INDEX idx_emitter_account ON emitter(account_id);
CREATE INDEX idx_emitter_cnpj ON emitter(cnpj);
CREATE INDEX idx_emitter_active ON emitter(is_active) WHERE is_active = true;
```

### 5. Certificate Table
```sql
CREATE TABLE certificate (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE CASCADE,
    pfx_content TEXT NOT NULL, -- Base64 encoded encrypted PFX
    password_encrypted TEXT NOT NULL,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_to TIMESTAMP WITH TIME ZONE NOT NULL,
    status certificate_status DEFAULT 'pending',
    subject_name VARCHAR(255),
    issuer_name VARCHAR(255),
    serial_number VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    activated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_valid_dates CHECK (valid_to > valid_from)
);

CREATE INDEX idx_certificate_emitter ON certificate(emitter_id);
CREATE INDEX idx_certificate_status ON certificate(status);
CREATE INDEX idx_certificate_expiry ON certificate(valid_to) WHERE status = 'valid';
```

## Address Management Tables

### 6. Address Table
```sql
CREATE TABLE address (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    street VARCHAR(255) NOT NULL,
    number VARCHAR(10) NOT NULL,
    complement VARCHAR(100),
    neighborhood VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(2) NOT NULL,
    zip_code VARCHAR(8) NOT NULL,
    country VARCHAR(2) DEFAULT 'BR',

    -- Geolocation (optional for future features)
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT chk_state_format CHECK (state ~ '^[A-Z]{2}$'),
    CONSTRAINT chk_zip_code_format CHECK (zip_code ~ '^\d{8}$'),
    CONSTRAINT chk_country_format CHECK (country ~ '^[A-Z]{2}$')
);

CREATE INDEX idx_address_location ON address(city, state);
CREATE INDEX idx_address_zip_code ON address(zip_code);
```

### 7. Emitter Address Table
```sql
CREATE TYPE address_type AS ENUM (
    'primary',      -- Main address (required)
    'billing',      -- Billing address
    'delivery',     -- Delivery address
    'pickup',       -- Pickup address
    'warehouse',    -- Warehouse address
    'branch',       -- Branch office
    'other'         -- Other address types
);

CREATE TABLE emitter_address (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE CASCADE,
    address_id UUID NOT NULL REFERENCES address(id) ON DELETE CASCADE,
    address_type address_type NOT NULL DEFAULT 'primary',
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(emitter_id, address_type, is_default) WHERE is_default = true,
    UNIQUE(emitter_id, address_id) -- Same address can't be used twice for same emitter
);

CREATE INDEX idx_emitter_address_emitter ON emitter_address(emitter_id);
CREATE INDEX idx_emitter_address_type ON emitter_address(address_type);
CREATE INDEX idx_emitter_address_default ON emitter_address(emitter_id, is_default) WHERE is_default = true;
```

### 8. Client Address Table
```sql
CREATE TABLE client_address (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES client(id) ON DELETE CASCADE,
    address_id UUID NOT NULL REFERENCES address(id) ON DELETE CASCADE,
    address_type address_type NOT NULL DEFAULT 'primary',
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(client_id, address_type, is_default) WHERE is_default = true,
    UNIQUE(client_id, address_id) -- Same address can't be used twice for same client
);

CREATE INDEX idx_client_address_client ON client_address(client_id);
CREATE INDEX idx_client_address_type ON client_address(address_type);
CREATE INDEX idx_client_address_default ON client_address(client_id, is_default) WHERE is_default = true;
```

### 9. Carrier Address Table
```sql
CREATE TABLE carrier_address (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    carrier_id UUID NOT NULL REFERENCES carrier(id) ON DELETE CASCADE,
    address_id UUID NOT NULL REFERENCES address(id) ON DELETE CASCADE,
    address_type address_type NOT NULL DEFAULT 'primary',
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(carrier_id, address_type, is_default) WHERE is_default = true,
    UNIQUE(carrier_id, address_id) -- Same address can't be used twice for same carrier
);

CREATE INDEX idx_carrier_address_carrier ON carrier_address(carrier_id);
CREATE INDEX idx_carrier_address_type ON carrier_address(address_type);
CREATE INDEX idx_carrier_address_default ON carrier_address(carrier_id, is_default) WHERE is_default = true;
```

## Entity Management Tables

### 10. Client Table
```sql
CREATE TYPE document_type AS ENUM ('cpf', 'cnpj');

CREATE TABLE client (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    legal_name VARCHAR(255),
    document_type document_type NOT NULL,
    federal_tax_id VARCHAR(14) NOT NULL,
    state_tax_id VARCHAR(20),
    municipal_tax_id VARCHAR(20),
    email VARCHAR(255),
    phone VARCHAR(20),

    tax_regime tax_regime,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    UNIQUE(emitter_id, federal_tax_id),
    CONSTRAINT chk_document_format CHECK (
        (document_type = 'cpf' AND federal_tax_id ~ '^\d{11}$') OR
        (document_type = 'cnpj' AND federal_tax_id ~ '^\d{14}$')
    )
);

CREATE INDEX idx_client_emitter ON client(emitter_id);
CREATE INDEX idx_client_document ON client(document_type, federal_tax_id);
CREATE INDEX idx_client_active ON client(emitter_id, is_active) WHERE is_active = true;
```

### 11. Product Table
```sql
CREATE TABLE product (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE CASCADE,
    code VARCHAR(50) NOT NULL, -- Internal product code
    name VARCHAR(255) NOT NULL,
    description TEXT,
    ncm_code VARCHAR(8) NOT NULL, -- Nomenclatura Comum do Mercosul
    cfop VARCHAR(4) NOT NULL, -- Código Fiscal de Operações
    unit VARCHAR(10) NOT NULL, -- Unit of measurement
    unit_price DECIMAL(12,2) NOT NULL,

    -- Basic tax information
    icms_origin INTEGER DEFAULT 0, -- 0-8 origin codes (required for ICMS)

    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    UNIQUE(emitter_id, code),
    CONSTRAINT chk_ncm_format CHECK (ncm_code ~ '^\d{8}$'),
    CONSTRAINT chk_cfop_format CHECK (cfop ~ '^\d{4}$'),
    CONSTRAINT chk_unit_price_positive CHECK (unit_price >= 0),
    CONSTRAINT chk_icms_origin CHECK (icms_origin >= 0 AND icms_origin <= 8)
);

CREATE INDEX idx_product_emitter ON product(emitter_id);
CREATE INDEX idx_product_code ON product(emitter_id, code);
CREATE INDEX idx_product_ncm ON product(ncm_code);
CREATE INDEX idx_product_active ON product(emitter_id, is_active) WHERE is_active = true;
```

### 11.1. Product Tax Table
```sql
CREATE TYPE tax_type AS ENUM (
    'icms',     -- ICMS (state tax)
    'ipi',      -- IPI (federal tax)
    'pis',      -- PIS (federal contribution)
    'cofins',   -- COFINS (federal contribution)
    'iss',      -- ISS (municipal tax for services)
    'csll',     -- CSLL (social contribution)
    'irpj',     -- IRPJ (income tax)
    'cpp',      -- CPP (social security)
    'other'     -- Other tax types
);

CREATE TABLE product_tax (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES product(id) ON DELETE CASCADE,
    tax_type tax_type NOT NULL,

    -- Tax situation codes (CST/CSOSN)
    cst_code VARCHAR(3), -- CST code (e.g., '00', '10', '20')
    csosn_code VARCHAR(3), -- CSOSN code for Simples Nacional

    -- Tax rates and values
    rate DECIMAL(5,2) DEFAULT 0, -- Tax rate percentage
    fixed_amount DECIMAL(12,2) DEFAULT 0, -- Fixed tax amount

    -- Tax calculation base
    calculation_base DECIMAL(5,2) DEFAULT 100, -- Percentage of product value to use as base

    -- Additional tax information
    reduction_percentage DECIMAL(5,2) DEFAULT 0, -- Base reduction percentage
    additional_percentage DECIMAL(5,2) DEFAULT 0, -- Additional tax percentage

    -- Tax exemption/suspension
    is_exempt BOOLEAN DEFAULT false,
    exemption_reason VARCHAR(255), -- Reason for exemption

    -- Validity period
    valid_from DATE,
    valid_to DATE,

    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(product_id, tax_type, valid_from), -- One tax config per type per period
    CONSTRAINT chk_rate_positive CHECK (rate >= 0 AND rate <= 100),
    CONSTRAINT chk_fixed_amount_positive CHECK (fixed_amount >= 0),
    CONSTRAINT chk_calculation_base_valid CHECK (calculation_base > 0 AND calculation_base <= 100),
    CONSTRAINT chk_reduction_percentage_valid CHECK (reduction_percentage >= 0 AND reduction_percentage <= 100),
    CONSTRAINT chk_validity_period CHECK (valid_to IS NULL OR valid_to >= valid_from)
);

CREATE INDEX idx_product_tax_product ON product_tax(product_id);
CREATE INDEX idx_product_tax_type ON product_tax(tax_type);
CREATE INDEX idx_product_tax_active ON product_tax(product_id, is_active) WHERE is_active = true;
CREATE INDEX idx_product_tax_validity ON product_tax(valid_from, valid_to);
CREATE INDEX idx_product_tax_current ON product_tax(product_id, tax_type, valid_from, valid_to)
    WHERE is_active = true AND (valid_to IS NULL OR valid_to >= CURRENT_DATE);
```

### 12. Carrier Table
```sql
CREATE TABLE carrier (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    cnpj VARCHAR(14),
    state_tax_id VARCHAR(20),
    email VARCHAR(255),
    phone VARCHAR(20),

    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT chk_carrier_cnpj CHECK (cnpj IS NULL OR cnpj ~ '^\d{14}$')
);

CREATE INDEX idx_carrier_emitter ON carrier(emitter_id);
CREATE INDEX idx_carrier_cnpj ON carrier(cnpj) WHERE cnpj IS NOT NULL;
```

### 13. Payment Term Table
```sql
CREATE TYPE payment_method AS ENUM (
    'money',           -- Cash (Dinheiro)
    'check',           -- Check (Cheque)
    'credit_card',     -- Credit Card (Cartão de Crédito)
    'debit_card',      -- Debit Card (Cartão de Débito)
    'bank_transfer',   -- Bank Transfer (Transferência Bancária)
    'pix',             -- PIX (Brazilian instant payment)
    'bank_slip',       -- Bank Slip/Boleto (Boleto Bancário)
    'promissory_note', -- Promissory Note (Nota Promissória)
    'duplicate',       -- Duplicate (Duplicata)
    'other'            -- Other methods
);

CREATE TYPE payment_term_type AS ENUM (
    'cash',            -- À vista (immediate payment)
    'installments',    -- Parcelado (multiple installments)
    'term',            -- A prazo (single future payment)
    'mixed'            -- Misto (combination of methods)
);

CREATE TABLE payment_term (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    term_type payment_term_type NOT NULL DEFAULT 'cash',

    -- Default payment method (can be overridden in installments)
    default_payment_method payment_method NOT NULL,

    -- Quick configuration for simple terms
    total_installments INTEGER DEFAULT 1,
    days_between_installments INTEGER DEFAULT 30,
    first_installment_days INTEGER DEFAULT 0,

    -- Discount and interest configuration
    cash_discount_percentage DECIMAL(5,2) DEFAULT 0,
    cash_discount_days INTEGER DEFAULT 0,
    late_fee_percentage DECIMAL(5,2) DEFAULT 0,
    interest_rate_monthly DECIMAL(5,2) DEFAULT 0,

    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT chk_installments_positive CHECK (total_installments > 0),
    CONSTRAINT chk_days_positive CHECK (days_between_installments >= 0),
    CONSTRAINT chk_first_installment_days CHECK (first_installment_days >= 0),
    CONSTRAINT chk_cash_discount CHECK (cash_discount_percentage >= 0 AND cash_discount_percentage <= 100),
    CONSTRAINT chk_late_fee CHECK (late_fee_percentage >= 0),
    CONSTRAINT chk_interest_rate CHECK (interest_rate_monthly >= 0)
);

CREATE INDEX idx_payment_term_emitter ON payment_term(emitter_id);
CREATE INDEX idx_payment_term_type ON payment_term(term_type);
```

### 13.1. Payment Term Installment Table
```sql
CREATE TABLE payment_term_installment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_term_id UUID NOT NULL REFERENCES payment_term(id) ON DELETE CASCADE,
    installment_number INTEGER NOT NULL,

    -- Days from invoice date to this installment due date
    days_to_due INTEGER NOT NULL,

    -- Percentage of total invoice amount for this installment
    percentage DECIMAL(5,2) NOT NULL,

    -- Payment method for this specific installment (optional override)
    payment_method payment_method,

    -- Installment-specific configurations
    description VARCHAR(255),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(payment_term_id, installment_number),
    CONSTRAINT chk_installment_number_positive CHECK (installment_number > 0),
    CONSTRAINT chk_days_to_due_positive CHECK (days_to_due >= 0),
    CONSTRAINT chk_percentage_valid CHECK (percentage > 0 AND percentage <= 100)
);

CREATE INDEX idx_payment_term_installment_term ON payment_term_installment(payment_term_id);
CREATE INDEX idx_payment_term_installment_number ON payment_term_installment(payment_term_id, installment_number);
```

## Invoice Tables

### 14. Invoice Table
```sql
CREATE TYPE invoice_status AS ENUM (
    'draft', 'pending_approval', 'approved', 'transmitted',
    'authorized', 'denied', 'cancelled', 'error'
);

CREATE TYPE invoice_type AS ENUM ('nfe', 'nfce'); -- Future support for NFC-e

CREATE TABLE invoice (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    emitter_id UUID NOT NULL REFERENCES emitter(id) ON DELETE RESTRICT,
    client_id UUID NOT NULL REFERENCES client(id) ON DELETE RESTRICT,
    carrier_id UUID REFERENCES carrier(id) ON DELETE SET NULL,
    payment_term_id UUID NOT NULL REFERENCES payment_term(id) ON DELETE RESTRICT,
    
    number INTEGER NOT NULL,
    series INTEGER NOT NULL,
    access_key VARCHAR(44), -- Chave de acesso (44 digits)
    
    status invoice_status DEFAULT 'draft',
    type invoice_type DEFAULT 'nfe',
    
    issue_date TIMESTAMP WITH TIME ZONE NOT NULL,
    due_date TIMESTAMP WITH TIME ZONE,
    
    -- Totals
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    freight_amount DECIMAL(12,2) DEFAULT 0,
    insurance_amount DECIMAL(12,2) DEFAULT 0,
    other_expenses DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    
    -- Tax totals
    icms_total DECIMAL(12,2) DEFAULT 0,
    ipi_total DECIMAL(12,2) DEFAULT 0,
    pis_total DECIMAL(12,2) DEFAULT 0,
    cofins_total DECIMAL(12,2) DEFAULT 0,
    total_taxes DECIMAL(12,2) DEFAULT 0,
    
    observations TEXT,
    internal_notes TEXT,
    
    -- SEFAZ data
    protocol VARCHAR(20), -- Authorization protocol
    receipt_number VARCHAR(20),
    xml_content TEXT, -- Signed XML content
    
    -- Timestamps
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES user(id),
    transmitted_at TIMESTAMP WITH TIME ZONE,
    authorized_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    UNIQUE(emitter_id, series, number),
    CONSTRAINT chk_access_key_format CHECK (access_key IS NULL OR access_key ~ '^\d{44}$'),
    CONSTRAINT chk_amounts_positive CHECK (
        subtotal >= 0 AND total_amount >= 0 AND
        discount_amount >= 0 AND freight_amount >= 0
    )
);

CREATE INDEX idx_invoice_emitter ON invoice(emitter_id);
CREATE INDEX idx_invoice_client ON invoice(client_id);
CREATE INDEX idx_invoice_status ON invoice(status);
CREATE INDEX idx_invoice_access_key ON invoice(access_key) WHERE access_key IS NOT NULL;
CREATE INDEX idx_invoice_issue_date ON invoice(issue_date);
CREATE INDEX idx_invoice_number ON invoice(emitter_id, series, number);
```

### 15. Invoice Item Table
```sql
CREATE TABLE invoice_item (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES invoice(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES product(id) ON DELETE RESTRICT,

    item_number INTEGER NOT NULL, -- Sequential item number in invoice

    -- Product info (snapshot at time of invoice)
    product_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_description TEXT,
    ncm_code VARCHAR(8) NOT NULL,
    cfop VARCHAR(4) NOT NULL,
    unit VARCHAR(10) NOT NULL,
    icms_origin INTEGER NOT NULL, -- Snapshot of ICMS origin

    -- Quantities and prices
    quantity DECIMAL(10,4) NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    discount_amount DECIMAL(12,2) DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(invoice_id, item_number),
    CONSTRAINT chk_quantity_positive CHECK (quantity > 0),
    CONSTRAINT chk_prices_positive CHECK (unit_price >= 0 AND total_price >= 0)
);

CREATE INDEX idx_invoice_item_invoice ON invoice_item(invoice_id);
CREATE INDEX idx_invoice_item_product ON invoice_item(product_id);
```

### 15.1. Invoice Tax Table
```sql
CREATE TABLE invoice_tax (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES invoice(id) ON DELETE CASCADE,
    invoice_item_id UUID REFERENCES invoice_item(id) ON DELETE CASCADE, -- NULL for invoice-level taxes
    tax_type tax_type NOT NULL,

    -- Tax situation codes (snapshot from product or manual override)
    cst_code VARCHAR(3),
    csosn_code VARCHAR(3),

    -- Tax calculation details
    calculation_base DECIMAL(12,2) NOT NULL DEFAULT 0,
    rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(12,2) NOT NULL DEFAULT 0,

    -- Additional tax information
    reduction_percentage DECIMAL(5,2) DEFAULT 0,
    additional_percentage DECIMAL(5,2) DEFAULT 0,
    fixed_amount DECIMAL(12,2) DEFAULT 0,

    -- Tax exemption/suspension
    is_exempt BOOLEAN DEFAULT false,
    exemption_reason VARCHAR(255),

    -- Manual override flags
    is_manual_override BOOLEAN DEFAULT false,
    override_reason VARCHAR(255),

    -- Original product tax reference (for audit)
    original_product_tax_id UUID,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT chk_tax_amount_positive CHECK (tax_amount >= 0),
    CONSTRAINT chk_calculation_base_positive CHECK (calculation_base >= 0),
    CONSTRAINT chk_rate_valid CHECK (rate >= 0 AND rate <= 100)
);

CREATE INDEX idx_invoice_tax_invoice ON invoice_tax(invoice_id);
CREATE INDEX idx_invoice_tax_item ON invoice_tax(invoice_item_id);
CREATE INDEX idx_invoice_tax_type ON invoice_tax(tax_type);
CREATE INDEX idx_invoice_tax_invoice_type ON invoice_tax(invoice_id, tax_type);
```

## Integration Tables

### 12. API Key Table
```sql
CREATE TABLE api_key (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES account(id) ON DELETE CASCADE,
    emitter_id UUID REFERENCES emitter(id) ON DELETE CASCADE, -- NULL = all emitters
    
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) UNIQUE NOT NULL, -- Hashed API key
    key_prefix VARCHAR(10) NOT NULL, -- First 8 chars for identification
    
    permissions TEXT[], -- Array of permissions
    rate_limit INTEGER DEFAULT 1000, -- Requests per hour
    
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_api_key_account ON api_key(account_id);
CREATE INDEX idx_api_key_emitter ON api_key(emitter_id);
CREATE INDEX idx_api_key_hash ON api_key(key_hash);
CREATE INDEX idx_api_key_active ON api_key(is_active) WHERE is_active = true;
```

### 13. Refresh Token Table
```sql
CREATE TABLE refresh_token (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_expires_future CHECK (expires_at > created_at)
);

CREATE INDEX idx_refresh_token_user ON refresh_token(user_id);
CREATE INDEX idx_refresh_token_hash ON refresh_token(token_hash);
CREATE INDEX idx_refresh_token_expires ON refresh_token(expires_at);
```

## Audit and Logging Tables

### 14. Audit Log Table
```sql
CREATE TYPE audit_action AS ENUM ('create', 'update', 'delete', 'login', 'logout');

CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user(id) ON DELETE SET NULL,
    account_id UUID REFERENCES account(id) ON DELETE SET NULL,
    emitter_id UUID REFERENCES emitter(id) ON DELETE SET NULL,
    
    action audit_action NOT NULL,
    entity_type VARCHAR(50) NOT NULL, -- Table name
    entity_id UUID, -- Record ID
    
    old_values JSONB, -- Previous values
    new_values JSONB, -- New values
    
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_audit_log_user ON audit_log(user_id);
CREATE INDEX idx_audit_log_entity ON audit_log(entity_type, entity_id);
CREATE INDEX idx_audit_log_created ON audit_log(created_at);
```

## Views and Functions

### 1. Active Certificate View
```sql
CREATE VIEW active_certificate AS
SELECT
    c.*,
    e.name as emitter_name,
    e.cnpj as emitter_cnpj
FROM certificate c
JOIN emitter e ON c.emitter_id = e.id
WHERE c.status = 'valid'
  AND c.valid_to > NOW()
  AND e.is_active = true;
```

### 2. Invoice Summary View
```sql
CREATE VIEW invoice_summary AS
SELECT
    i.id,
    i.number,
    i.series,
    i.status,
    i.total_amount,
    i.issue_date,
    e.name as emitter_name,
    c.name as client_name,
    COUNT(ii.id) as item_count
FROM invoice i
JOIN emitter e ON i.emitter_id = e.id
JOIN client c ON i.client_id = c.id
LEFT JOIN invoice_item ii ON i.id = ii.invoice_id
WHERE i.deleted_at IS NULL
GROUP BY i.id, e.name, c.name;
```

### 3. Product Tax Summary View
```sql
CREATE VIEW product_tax_summary AS
SELECT
    p.id as product_id,
    p.name as product_name,
    p.code as product_code,
    pt.tax_type,
    pt.rate,
    pt.cst_code,
    pt.csosn_code,
    pt.is_exempt,
    pt.valid_from,
    pt.valid_to,
    CASE
        WHEN pt.valid_to IS NULL OR pt.valid_to >= CURRENT_DATE THEN true
        ELSE false
    END as is_current
FROM product p
LEFT JOIN product_tax pt ON p.id = pt.product_id
WHERE p.is_active = true
  AND (pt.is_active = true OR pt.is_active IS NULL)
ORDER BY p.name, pt.tax_type;
```

### 3. Certificate Expiry Function
```sql
CREATE OR REPLACE FUNCTION check_certificate_expiry()
RETURNS void AS $$
BEGIN
    -- Mark expired certificates
    UPDATE certificate
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'valid' AND valid_to <= NOW();

    -- Update emitter certificate status
    UPDATE emitter
    SET certificate_status = 'expired', updated_at = NOW()
    WHERE id IN (
        SELECT DISTINCT emitter_id
        FROM certificate
        WHERE status = 'expired'
        AND emitter_id NOT IN (
            SELECT emitter_id
            FROM certificate
            WHERE status = 'valid'
        )
    );
END;
$$ LANGUAGE plpgsql;
```

## Indexes and Performance

### 1. Composite Indexes
```sql
-- Invoice search optimization
CREATE INDEX idx_invoice_search ON invoice(emitter_id, status, issue_date DESC);
CREATE INDEX idx_invoice_client_date ON invoice(client_id, issue_date DESC);

-- Product search optimization
CREATE INDEX idx_product_search ON product(emitter_id, is_active, name);

-- Client search optimization
CREATE INDEX idx_client_search ON client(emitter_id, is_active, name);
```

### 2. Partial Indexes
```sql
-- Only index active records
CREATE INDEX idx_emitter_active_cnpj ON emitter(cnpj) WHERE is_active = true;
CREATE INDEX idx_product_active_code ON product(emitter_id, code) WHERE is_active = true;
CREATE INDEX idx_client_active_doc ON client(emitter_id, federal_tax_id) WHERE is_active = true;
```

This database schema provides a solid foundation for the ez-NFe SaaS platform with proper normalization, constraints, and performance optimizations.
