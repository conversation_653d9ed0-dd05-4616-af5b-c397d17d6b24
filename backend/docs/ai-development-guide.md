# AI Agent Development Guide

## Overview

This guide ensures AI agents consistently apply all project documentation and standards when developing the ez-NFe backend system. Every development task must follow this systematic approach.

## Pre-Development Checklist

### **1. Documentation Review Protocol**

Before starting any development task, AI agents MUST review these documents in order:

#### **Core Architecture Documents:**
- [ ] `backend/docs/database-schema.md` - Database design and relationships
- [ ] `backend/docs/technical-specifications.md` - Module structure and patterns
- [ ] `backend/docs/api-design-standards.md` - API conventions and DTOs
- [ ] `backend/docs/testing-strategy.md` - Testing requirements and coverage

#### **Task-Specific Documentation:**
- [ ] Review relevant entity relationships in database schema
- [ ] Check existing module patterns in technical specifications
- [ ] Verify API design standards for endpoints being created/modified
- [ ] Understand testing requirements for the specific module

### **2. Context Gathering Process**

For each development task, AI agents must:

#### **A. Understand the Domain:**
```
1. What business domain does this task affect? (invoices, products, clients, etc.)
2. What entities are involved?
3. What relationships exist between entities?
4. What business rules apply?
```

#### **B. Review Existing Patterns:**
```
1. How are similar modules structured?
2. What naming conventions are used?
3. How are similar business rules implemented?
4. What validation patterns exist?
```

#### **C. Check Dependencies:**
```
1. What other modules does this depend on?
2. What services need to be injected?
3. What external integrations are required?
4. What database migrations are needed?
```

## Development Task Templates

### **Template 1: Creating a New Module**

#### **Step 1: Architecture Review**
```
AI Agent Checklist:
□ Reviewed module structure template in technical-specifications.md
□ Identified all entities needed from database-schema.md
□ Checked entity relationships and foreign keys
□ Reviewed similar modules for patterns
□ Identified required DTOs from api-design-standards.md
```

#### **Step 2: Implementation Plan**
```
Create implementation plan covering:
□ Module structure following standard template
□ Entity definitions with proper relationships
□ Service layer with business logic
□ Controller with all CRUD operations
□ DTOs with proper validation
□ Repository with custom queries (if needed)
□ Tests following testing-strategy.md requirements
```

#### **Step 3: Code Generation**
```
Generate code following:
□ Naming conventions from technical-specifications.md
□ API patterns from api-design-standards.md
□ Database constraints from database-schema.md
□ Testing patterns from testing-strategy.md
```

### **Template 2: Modifying Existing Module**

#### **Step 1: Current State Analysis**
```
AI Agent Checklist:
□ Reviewed existing module structure
□ Identified current entity relationships
□ Checked existing API endpoints
□ Reviewed current test coverage
□ Identified potential breaking changes
```

#### **Step 2: Impact Assessment**
```
Analyze impact on:
□ Database schema changes needed
□ API endpoint modifications
□ Service layer changes
□ DTO updates required
□ Test updates needed
□ Documentation updates required
```

#### **Step 3: Implementation**
```
Implement changes ensuring:
□ Backward compatibility where possible
□ Proper migration scripts for database changes
□ Updated API documentation
□ Updated tests with new scenarios
□ Updated DTOs with new validation rules
```

### **Template 3: Adding New Feature**

#### **Step 1: Feature Analysis**
```
AI Agent Checklist:
□ Identified which modules are affected
□ Reviewed business rules in database-schema.md
□ Checked API design patterns for similar features
□ Identified testing requirements
□ Planned integration points
```

#### **Step 2: Design Validation**
```
Validate design against:
□ Existing entity relationships
□ API design standards
□ Module structure patterns
□ Testing coverage requirements
□ Performance considerations
```

## Quality Assurance Protocol

### **Code Review Checklist**

Before submitting any code, AI agents must verify:

#### **Database Compliance:**
- [ ] Entities follow database schema specifications
- [ ] Relationships are properly defined
- [ ] Constraints match database design
- [ ] Migrations are provided for schema changes

#### **API Compliance:**
- [ ] Endpoints follow RESTful conventions
- [ ] DTOs have proper validation decorators
- [ ] Response formats match API standards
- [ ] Error handling follows established patterns

#### **Module Structure Compliance:**
- [ ] Files are organized according to module template
- [ ] Naming conventions are consistent
- [ ] Dependencies are properly injected
- [ ] Services follow single responsibility principle

#### **Testing Compliance:**
- [ ] Unit tests cover all service methods
- [ ] Integration tests cover API endpoints
- [ ] Test data follows fixture patterns
- [ ] Coverage meets minimum requirements (95%)

## Documentation Update Protocol

### **When to Update Documentation**

AI agents must update documentation when:

#### **Database Changes:**
- [ ] New entities added → Update database-schema.md
- [ ] Relationships modified → Update entity relationship diagrams
- [ ] New constraints added → Update constraint documentation

#### **API Changes:**
- [ ] New endpoints added → Update api-design-standards.md
- [ ] DTOs modified → Update DTO examples
- [ ] Response formats changed → Update response documentation

#### **Module Changes:**
- [ ] New modules created → Update technical-specifications.md
- [ ] Service patterns changed → Update service examples
- [ ] New testing patterns → Update testing-strategy.md

### **Documentation Update Process**

1. **Identify Changes**: List all documentation that needs updates
2. **Update Content**: Modify relevant documentation files
3. **Validate Consistency**: Ensure all docs are consistent with changes
4. **Update Examples**: Provide updated code examples where applicable

## Error Prevention Strategies

### **Common Pitfalls to Avoid**

#### **Database Issues:**
- ❌ Creating entities without proper relationships
- ❌ Missing foreign key constraints
- ❌ Ignoring soft delete patterns
- ❌ Not following naming conventions

#### **API Issues:**
- ❌ Inconsistent response formats
- ❌ Missing validation decorators
- ❌ Improper HTTP status codes
- ❌ Missing Swagger documentation

#### **Module Issues:**
- ❌ Incorrect file organization
- ❌ Missing dependency injection
- ❌ Circular dependencies
- ❌ Inconsistent naming patterns

#### **Testing Issues:**
- ❌ Insufficient test coverage
- ❌ Missing integration tests
- ❌ Not testing error scenarios
- ❌ Inconsistent test data

### **Validation Commands**

AI agents should run these validation commands:

```bash
# Lint code for consistency
npm run lint

# Run all tests
npm run test

# Check test coverage
npm run test:cov

# Validate API documentation
npm run docs:validate

# Check database schema
npm run db:validate
```

## Task Completion Criteria

### **Definition of Done**

A task is complete only when:

- [ ] All code follows documented patterns and standards
- [ ] Database changes include proper migrations
- [ ] API endpoints are fully documented
- [ ] Tests achieve required coverage (95%+)
- [ ] Documentation is updated where necessary
- [ ] Code passes all validation checks
- [ ] Integration tests pass
- [ ] No breaking changes without proper versioning

## Emergency Procedures

### **When Documentation is Unclear**

If AI agents encounter unclear or conflicting documentation:

1. **Stop Development**: Do not proceed with assumptions
2. **Document the Issue**: Clearly describe the ambiguity
3. **Request Clarification**: Ask for specific guidance
4. **Propose Solution**: Suggest how to resolve the ambiguity
5. **Wait for Approval**: Do not implement until clarified

### **When Patterns Don't Fit**

If established patterns don't fit the current task:

1. **Document the Exception**: Explain why patterns don't apply
2. **Propose Alternative**: Suggest alternative approach
3. **Justify Decision**: Provide reasoning for deviation
4. **Update Documentation**: Add new patterns if approved

## AI Agent Task Execution Protocol

### **Mandatory Task Initiation Process**

Every AI agent MUST start each development task with this exact process:

#### **Step 1: Task Understanding (5 minutes)**
```
AI Agent Declaration:
"I am starting a [TASK_TYPE] task: [TASK_DESCRIPTION]

Before I begin coding, I will:
1. Review all relevant documentation
2. Understand the business context
3. Identify all affected components
4. Plan my implementation approach
5. Validate my understanding"
```

#### **Step 2: Documentation Review (10 minutes)**
```
Required Documentation Review:
□ Read database-schema.md for entity definitions
□ Read technical-specifications.md for patterns
□ Read api-design-standards.md for API conventions
□ Read testing-strategy.md for test requirements
□ Read ai-quick-reference.md for common patterns

AI Agent Must State:
"I have reviewed [LIST_OF_DOCS] and understand:
- Entity relationships: [SUMMARY]
- Required patterns: [SUMMARY]
- API conventions: [SUMMARY]
- Testing requirements: [SUMMARY]"
```

#### **Step 3: Implementation Planning (10 minutes)**
```
AI Agent Must Create:
1. Detailed implementation plan
2. List of files to create/modify
3. Dependencies and relationships
4. Testing strategy
5. Potential risks or challenges

AI Agent Must State:
"My implementation plan:
- Files to create: [LIST]
- Files to modify: [LIST]
- Dependencies: [LIST]
- Testing approach: [SUMMARY]
- Estimated complexity: [LOW/MEDIUM/HIGH]"
```

#### **Step 4: Validation Before Coding**
```
AI Agent Must Confirm:
□ I understand the business requirements
□ I have identified all affected components
□ I know which patterns to follow
□ I have a clear testing strategy
□ I am ready to implement following all standards

Only after this confirmation can coding begin.
```

### **Continuous Validation During Development**

#### **After Each Major Component (Entity, Service, Controller)**
```
AI Agent Must:
1. Validate against documentation patterns
2. Check for consistency with existing code
3. Verify all relationships are correct
4. Ensure proper error handling
5. Confirm testing approach

AI Agent Must State:
"Component [NAME] completed:
- Follows pattern: [YES/NO - DETAILS]
- Relationships correct: [YES/NO - DETAILS]
- Error handling: [YES/NO - DETAILS]
- Ready for testing: [YES/NO - DETAILS]"
```

### **Pre-Submission Validation**

#### **Final Quality Check**
```
AI Agent Must Complete:
□ All code follows documented patterns
□ All tests pass with required coverage
□ All documentation is updated
□ All validation commands pass
□ No breaking changes introduced

AI Agent Must Provide:
"Task completion summary:
- Files created: [LIST]
- Files modified: [LIST]
- Tests added: [COUNT]
- Coverage achieved: [PERCENTAGE]
- Documentation updated: [YES/NO]
- All validations passed: [YES/NO]"
```

## Automated Validation Integration

### **Pre-commit Hooks**
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm run validate:ai-standards"
    }
  }
}
```

### **AI Standards Validation Script**
```bash
#!/bin/bash
# validate-ai-standards.sh

echo "🤖 Validating AI Agent Standards Compliance..."

# Check if documentation was updated when needed
npm run docs:check-updates

# Validate code patterns
npm run lint:patterns

# Check test coverage
npm run test:coverage:validate

# Validate API documentation
npm run api:docs:validate

# Check database consistency
npm run db:validate

echo "✅ All AI standards validations passed!"
```

This systematic approach ensures consistent, high-quality development by AI agents while maintaining architectural integrity and documentation accuracy.
