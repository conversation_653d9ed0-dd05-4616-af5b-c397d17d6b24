# Database Design Documentation - Phase 1.1

## Overview
This document describes the database design for the EZ NFe SaaS platform Phase 1.1, implementing a multi-tenant Brazilian fiscal compliance system with comprehensive data models for invoice management, audit trails, and Brazilian regulatory validation.

## Database Schema

### Technology Stack
- **Database**: PostgreSQL 14+
- **ORM**: TypeORM with NestJS
- **Migration Management**: TypeORM migrations
- **Indexing Strategy**: Optimized for multi-tenant queries and Brazilian fiscal regulations

## Entity Relationship Overview

### Multi-Tenant Foundation
The system is built on a hierarchical multi-tenant architecture:
- **Account** (top-level tenant)
- **Business Unit** (sub-tenant within account)
- **Users** (scoped to account with current business unit)

### Core Entity Categories

#### 1. Foundation Entities
- **Account**: Top-level tenant containing all data
- **Subscription**: Account billing and feature management
- **BusinessUnit**: Company/CNPJ within an account
- **User**: System users with role-based access

#### 2. Business Entities
- **Client**: Customers/suppliers for invoicing
- **Product**: Items sold with tax configurations
- **Carrier**: Shipping/transport companies
- **PaymentTerm**: Payment condition templates

#### 3. Invoice System
- **Invoice**: NF-e/NFC-e/NFS-e documents
- **InvoiceItem**: Line items with Brazilian tax calculations

#### 4. Support Systems
- **Certificate**: Digital certificates for signing
- **ApiKey**: API access management
- **Subscription**: Billing and feature management

#### 5. Governance & Compliance
- **AuditLog**: Complete system audit trail
- **Notification**: System communications

#### 6. Brazilian Validation Framework
- **CNPJValidation**: Business identifier validation
- **CEPValidation**: Postal code validation

## Detailed Entity Descriptions

### Foundation Entities

#### Account
**Purpose**: Top-level tenant container for all customer data
**Multi-tenancy**: Root tenant entity

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Account display name
- `cnpj`: Primary business identifier
- `status`: Account state (active, suspended, cancelled, trial)
- `trialEndsAt`: Trial period expiration
- `settings`: JSON configuration object
- `metadata`: Additional account data

**Relationships**:
- One-to-one with Subscription
- One-to-many with BusinessUnit
- One-to-many with User (account scoped)

**Indexes**:
- Primary: `id` (clustered)
- Secondary: `status`, `cnpj`

#### BusinessUnit
**Purpose**: Represents a company/CNPJ within an account for multi-entity scenarios
**Multi-tenancy**: Sub-tenant under Account

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Business unit name
- `cnpj`: CNPJ identifier (unique per business unit)
- `stateRegistration`: State tax ID (Inscrição Estadual)
- `municipalRegistration`: Municipal tax ID
- `companyType`: Legal entity type (MEI, LTDA, SA, etc.)
- `status`: Business unit status
- `isDefault`: Default BU for account
- `taxConfiguration`: JSON tax settings
- `nfeConfiguration`: JSON NF-e settings

**Relationships**:
- Many-to-one with Account
- One-to-many with User (current business unit)
- One-to-many with all business entities (clients, products, etc.)

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `cnpj`, `status`

#### User
**Purpose**: System users with role-based access control
**Multi-tenancy**: Account scoped with business unit context

**Key Fields**:
- `id`: Primary key (UUID)
- `email`: Unique email address
- `role`: User role (owner, admin, member, viewer)
- `status`: User status (active, inactive, suspended, pending_verification)
- `currentBusinessUnitId`: Active business unit context
- Authentication fields (password, tokens, etc.)
- Security fields (login attempts, locks, etc.)

**Relationships**:
- Many-to-one with Account
- Many-to-one with BusinessUnit (current)
- One-to-many with AuditLog
- One-to-many with Notification

**Indexes**:
- Primary: `id`
- Unique: `email`
- Secondary: `accountId`, `status`, `currentBusinessUnitId`

### Business Entities

#### Client
**Purpose**: Customer and supplier records for invoicing
**Multi-tenancy**: Business Unit scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Client name
- `document`: CPF or CNPJ
- `documentType`: Document type identifier
- `email`, `phone`: Contact information
- `address`: JSON address object
- `isActive`: Active status flag
- `tags`: JSON array of tags
- `notes`: Additional notes
- `metadata`: Extended properties

**Relationships**:
- Many-to-one with Account
- Many-to-one with BusinessUnit
- One-to-many with Invoice

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`, `document`

#### Product
**Purpose**: Product/service catalog with Brazilian tax configurations
**Multi-tenancy**: Business Unit scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Product name
- `description`: Product description
- `sku`: Stock keeping unit
- `ean`: Barcode (GTIN)
- `ncm`: Brazilian tax classification (NCM)
- `cfop`: Fiscal operation code
- `unitOfMeasure`: Unit of measurement
- `price`: Sale price
- `costPrice`: Cost price
- `weight`: Product weight
- `taxConfiguration`: JSON tax settings
- `dimensions`: JSON dimension data

**Relationships**:
- Many-to-one with Account
- Many-to-one with BusinessUnit
- One-to-many with InvoiceItem

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`, `sku`, `ncm`

#### Carrier
**Purpose**: Shipping and transport company records
**Multi-tenancy**: Business Unit scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Carrier name
- `document`: CNPJ identifier
- `stateRegistration`: State tax ID
- Contact and address information
- `isActive`: Active status

**Relationships**:
- Many-to-one with Account
- Many-to-one with BusinessUnit
- One-to-many with Invoice

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`

#### PaymentTerm
**Purpose**: Payment condition templates for invoicing
**Multi-tenancy**: Business Unit scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Payment term name
- `installments`: Number of installments
- `intervalDays`: Days between installments
- `firstInstallmentDays`: Days to first payment
- Discount and penalty percentages
- `isDefault`: Default payment term flag

**Relationships**:
- Many-to-one with Account
- Many-to-one with BusinessUnit
- One-to-many with Invoice

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`

### Invoice System

#### Invoice
**Purpose**: Brazilian fiscal documents (NF-e, NFC-e, NFS-e)
**Multi-tenancy**: Business Unit scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `number`: Invoice number (sequential)
- `series`: Invoice series
- `type`: Document type (nfe, nfce, nfse)
- `status`: Document status (draft → issued → sent/received)
- `issueDate`: Issue date
- `accessKey`: Brazilian access key (chave de acesso)
- `authorizationCode`: SEFAZ authorization
- XML and PDF storage paths
- Financial totals (subtotal, taxes, discounts, etc.)
- Brazilian tax totals (ICMS, IPI, PIS, COFINS, ISS)
- Transport information
- Payment information
- Approval workflow fields
- Error and warning storage

**Relationships**:
- Many-to-one with Account
- Many-to-one with BusinessUnit
- Many-to-one with Client
- Many-to-one with Carrier (optional)
- Many-to-one with PaymentTerm (optional)
- Many-to-one with User (creator, approver, etc.)
- One-to-many with InvoiceItem

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`, `clientId`, `status`, `type`
- Composite: `number` + `series` (uniqueness per business unit)

#### InvoiceItem
**Purpose**: Invoice line items with comprehensive Brazilian tax calculations
**Multi-tenancy**: Inherits from Invoice

**Key Fields**:
- `id`: Primary key (UUID)
- `sequence`: Item order in invoice
- Product information (code, description, NCM, CFOP)
- Quantity and pricing fields
- Weight and measurement data
- Complete Brazilian tax fields:
  - ICMS (origin, CST, rates, amounts)
  - ICMS-ST (rates and amounts)
  - IPI (CST, rates, amounts)
  - PIS (CST, rates, amounts)
  - COFINS (CST, rates, amounts)
  - ISS (rates and amounts)
- Import declaration data
- Fuel-specific data
- Additional information

**Relationships**:
- Many-to-one with Invoice
- Many-to-one with Product (optional)

**Indexes**:
- Primary: `id`
- Secondary: `invoiceId`, `productId`

### Support Systems

#### Certificate
**Purpose**: Digital certificate management for NF-e signing
**Multi-tenancy**: Business Unit scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: Certificate name
- `type`: Certificate type (A1, A3)
- Certificate identity fields (serial, issuer, subject)
- Validity period (validFrom, validUntil)
- `status`: Certificate status
- Cryptographic data (thumbprint, keys)
- Installation information
- Usage tracking
- Backup information
- Alert configuration

**Relationships**:
- Many-to-one with BusinessUnit

**Indexes**:
- Primary: `id`
- Secondary: `businessUnitId`, `status`, `validUntil`

#### ApiKey
**Purpose**: API access key management with rate limiting
**Multi-tenancy**: Account scoped

**Key Fields**:
- `id`: Primary key (UUID)
- `name`: API key name
- `keyPrefix`: Public key prefix
- `hashedKey`: Hashed key for validation
- `status`: Key status (active, suspended, revoked)
- `scopes`: JSON array of permissions
- Rate limiting configuration
- IP and origin restrictions
- Usage tracking and statistics
- Expiration settings

**Relationships**:
- Many-to-one with Account
- Many-to-one with User (creator)

**Indexes**:
- Primary: `id`
- Unique: `keyPrefix`
- Secondary: `accountId`, `status`, `createdByUserId`

#### Subscription
**Purpose**: Account subscription and billing management
**Multi-tenancy**: Account scoped (one-to-one)

**Key Fields**:
- `id`: Primary key (UUID)
- `tier`: Subscription tier (free, basic, professional, enterprise)
- `status`: Subscription status
- `billingCycle`: Billing frequency
- Pricing and discount fields
- Trial period management
- Feature and limit configuration (JSON)
- Usage tracking (JSON)
- Payment method integration
- Stripe integration fields

**Relationships**:
- One-to-one with Account

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `status`, `tier`

### Governance & Compliance

#### AuditLog
**Purpose**: Comprehensive system audit trail for compliance and troubleshooting
**Multi-tenancy**: Account scoped with cross-entity visibility

**Key Fields**:
- `id`: Primary key (UUID)
- Action information (action, entityType, entityId)
- Severity and category classification
- Detailed description and metadata
- Change tracking (oldValues, newValues in JSON)
- Context information (IP, user agent, session)
- Request tracking (requestId, endpoint, HTTP data)
- Geographic information (country, city, timezone)
- Risk assessment (suspicious flag, risk score)
- Compliance metadata (retention requirements)
- System information (version, environment, module)
- Error information (for failed operations)

**Relationships**:
- Many-to-one with Account (optional, for system-wide events)
- Many-to-one with BusinessUnit (optional)
- Many-to-one with User (optional, for anonymous actions)

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`, `userId`
- Secondary: `action`, `entityType`, `severity`, `category`
- Secondary: `performedAt`, `entityId`

**Compliance Features**:
- LGPD compliance with retention policies
- Security event flagging and risk scoring
- Change tracking for all entities
- Performance and access pattern analysis

#### Notification
**Purpose**: System communications and user alerts with multi-channel delivery
**Multi-tenancy**: Account scoped

**Key Fields**:
- `id`: Primary key (UUID)
- Content fields (title, message, description)
- Classification (type, category, priority)
- Delivery configuration (channels, scheduling)
- Status tracking (sent, delivered, read timestamps)
- Action configuration (URL, label, data)
- UI configuration (icon, color, pinning)
- Context metadata (source system, entity references)
- Delivery tracking (retry logic, errors, receipts)
- Expiration and cleanup settings
- Template and localization support

**Relationships**:
- Many-to-one with Account (optional)
- Many-to-one with BusinessUnit (optional)
- Many-to-one with User (target user)

**Indexes**:
- Primary: `id`
- Secondary: `accountId`, `businessUnitId`, `userId`
- Secondary: `type`, `category`, `status`, `priority`
- Secondary: `scheduledFor`, `createdAt`, `isRead`

**Features**:
- Multi-channel delivery (in-app, email, SMS, push, webhook, Slack)
- Intelligent retry with exponential backoff
- Template-based content generation
- Bulk operations and threading support
- Priority-based processing

### Brazilian Validation Framework

#### CNPJValidation
**Purpose**: Brazilian business identifier (CNPJ) validation and data enrichment
**Multi-tenancy**: Account scoped with business unit context

**Key Fields**:
- `id`: Primary key (UUID)
- CNPJ information (raw and formatted)
- Validation status and format checking
- Complete company information from Receita Federal:
  - Company names (razão social, nome fantasia)
  - Company classification (type, size, tax regime)
  - Legal representative information
  - Registration and update dates
  - Complete address information
  - Economic activity (primary and secondary CNAEs)
  - Contact information
  - Share capital
  - Federal tax situation
  - Special tax regimes (Simples Nacional, MEI)
- Validation metadata and error tracking
- Retry and rate limiting logic

**Relationships**:
- Many-to-one with Account (optional)
- Many-to-one with BusinessUnit (optional)

**Indexes**:
- Primary: `id`
- Unique: `cnpj`
- Secondary: `accountId`, `businessUnitId`, `status`
- Secondary: `validatedAt`, `expiresAt`

**Features**:
- Real-time CNPJ validation with Receita Federal APIs
- Automatic data enrichment and caching
- Expiration-based revalidation (30-day cache)
- Rate limiting and error recovery
- Brazilian tax regime detection
- Address geocoding integration ready

#### CEPValidation
**Purpose**: Brazilian postal code (CEP) validation with address resolution
**Multi-tenancy**: Account scoped with business unit context

**Key Fields**:
- `id`: Primary key (UUID)
- CEP information (raw and formatted)
- Validation status and format checking
- Complete address information:
  - Street address (logradouro)
  - Neighborhood (bairro)
  - City and state
  - Government codes (IBGE, GIA, DDD, SIAFI)
  - Geographic coordinates (latitude, longitude)
  - Regional information (region, timezone)
- Service availability information
- Validation metadata and quality scoring
- Usage statistics and confidence metrics

**Relationships**:
- Many-to-one with Account (optional)
- Many-to-one with BusinessUnit (optional)

**Indexes**:
- Primary: `id`
- Unique: `cep`
- Secondary: `accountId`, `businessUnitId`, `status`
- Secondary: `validatedAt`, `expiresAt`
- Secondary: `state`, `city`

**Features**:
- Integration with ViaCEP and other Brazilian APIs
- Long-term caching (90-day expiration for stable data)
- Distance calculation between CEPs
- Delivery service availability checking
- Rural area and PO Box detection
- Data quality and confidence scoring

## Data Relationships and Constraints

### Multi-Tenancy Implementation

#### Hierarchical Structure
```
Account (Root Tenant)
├── Subscription (1:1)
├── Users (1:N, account-scoped)
├── BusinessUnits (1:N)
│   ├── Users (current BU context)
│   ├── Clients (1:N, BU-scoped)
│   ├── Products (1:N, BU-scoped)
│   ├── Carriers (1:N, BU-scoped)
│   ├── PaymentTerms (1:N, BU-scoped)
│   ├── Invoices (1:N, BU-scoped)
│   └── Certificates (1:N, BU-scoped)
├── ApiKeys (1:N, account-scoped)
├── AuditLogs (1:N, account-scoped)
├── Notifications (1:N, account-scoped)
├── CNPJValidations (1:N, account-scoped)
└── CEPValidations (1:N, account-scoped)
```

#### Isolation Strategy
- **Account Level**: Complete data isolation between accounts
- **Business Unit Level**: Shared users, isolated business data
- **Global Level**: System-wide entities (audit, notifications) with tenant filtering

### Referential Integrity

#### Cascade Delete Rules
- **Account deletion**: Cascades to all owned data
- **BusinessUnit deletion**: Cascades to BU-scoped entities
- **User deletion**: Preserved in audit logs (SET NULL)
- **Invoice deletion**: Cascades to invoice items

#### Foreign Key Constraints
- All multi-tenant relationships enforce valid tenant scope
- Optional relationships use SET NULL for soft dependencies
- Critical relationships use RESTRICT to prevent data loss

### Data Validation and Business Rules

#### Entity-Level Validation
- **CNPJ**: Format and check digit validation
- **CEP**: Format validation and existence checking
- **Email**: RFC-compliant email format
- **Brazilian tax fields**: Valid CST codes and rate ranges
- **Invoice numbers**: Sequential numbering per series per business unit

#### Business Logic Constraints
- **Account trial limits**: Feature and usage restrictions
- **Invoice workflow**: Status progression validation
- **Certificate validity**: Expiration checking and alerts
- **API rate limits**: Usage quotas and throttling
- **User permissions**: Role-based access control

## Performance Optimization

### Indexing Strategy

#### Primary Indexes
- All tables use UUID primary keys with B-tree clustering
- Unique constraints on business identifiers (email, CNPJ, CEP)

#### Multi-Tenant Indexes
- Composite indexes on (accountId, businessUnitId) for tenant filtering
- Status and type indexes for efficient filtering
- Timestamp indexes for audit and reporting queries

#### Query-Specific Indexes
- Invoice lookup: (number, series) composite index
- Audit queries: (performedAt, entityType, action) composite index
- Notification queries: (userId, isRead, priority) composite index
- Tax queries: (ncm, cfop) for product classification

### Query Optimization Patterns

#### Tenant-Aware Queries
All queries include tenant context in WHERE clauses:
```sql
-- Account-scoped query pattern
WHERE accountId = $1 AND ...

-- Business Unit-scoped query pattern  
WHERE accountId = $1 AND businessUnitId = $2 AND ...
```

#### Pagination Strategy
- Cursor-based pagination for large datasets
- Offset-based pagination for small, bounded datasets
- Pre-computed counts for dashboard metrics

#### Caching Strategy
- Application-level caching for validation results
- Database-level materialized views for reporting
- Redis integration for session and rate limit data

## Security Considerations

### Data Protection

#### Encryption at Rest
- Database-level encryption for sensitive fields
- Certificate private keys encrypted with account-specific keys
- PII data tagged for LGPD compliance

#### Access Control
- Row-level security (RLS) for multi-tenant isolation
- Role-based permissions with fine-grained scoping
- API key-based access with scope limitations

#### Audit Requirements
- Complete change tracking for all business entities
- Immutable audit log entries with retention policies
- Compliance reporting for Brazilian regulations

### Brazilian Regulatory Compliance

#### LGPD (Data Protection)
- Personal data identification and tagging
- Consent management and data portability
- Right to erasure with audit trail preservation
- Data processing purpose documentation

#### Fiscal Compliance
- Complete invoice audit trail preservation
- Tax calculation change tracking
- Certificate management and security
- Integration with SEFAZ protocols

## Migration Strategy

### Database Schema Evolution

#### Migration Management
- TypeORM migrations with rollback support
- Environment-specific migration execution
- Data transformation scripts for major changes

#### Version Control
- Schema versioning aligned with application releases
- Backward compatibility for API clients
- Progressive feature rollout support

#### Data Migration Patterns
- Blue-green deployments for zero-downtime updates
- Bulk data operations with progress tracking
- Rollback procedures for failed migrations

### Deployment Considerations

#### Environment Configuration
- Environment-specific database connections
- Feature flags for gradual rollout
- Monitoring and alerting integration

#### Backup and Recovery
- Automated daily backups with point-in-time recovery
- Cross-region backup replication
- Disaster recovery procedures and testing

#### Performance Monitoring
- Query performance tracking and alerting
- Connection pool monitoring
- Index usage analysis and optimization

## Future Considerations

### Phase 2 Enhancements

#### Additional Entities
- **Financial integrations**: Bank accounts, payment processors
- **Inventory management**: Stock tracking, warehouses
- **Document attachments**: File storage and management
- **Webhook system**: Event-driven integrations
- **Reporting engine**: Custom reports and dashboards

#### Scalability Improvements
- **Horizontal partitioning**: Date-based partitioning for large tables
- **Read replicas**: Query distribution for reporting workloads
- **Archive strategies**: Cold storage for historical data
- **Search optimization**: Full-text search integration

#### Advanced Features
- **Machine learning**: Tax classification automation
- **Real-time synchronization**: Live data updates
- **Advanced analytics**: Business intelligence integration
- **Mobile optimization**: Offline-first data strategies

This comprehensive database design provides a solid foundation for the EZ NFe SaaS platform, ensuring scalability, compliance, and maintainability while supporting the complex requirements of Brazilian fiscal regulations and multi-tenant architecture.
