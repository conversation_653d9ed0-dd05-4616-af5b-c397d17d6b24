# AI Agent Prompt Templates

## Overview

This document contains ready-to-use prompt templates for different types of development tasks. Copy and modify these templates when requesting AI agents to implement features, fix bugs, or make changes to the ez-NFe system.

## Template 1: New Feature Development

```
# Task: [FEATURE_NAME]

## Context
I need you to develop [SPECIFIC_FEATURE_DESCRIPTION] for the ez-NFe SaaS backend system.

## Mandatory Pre-Development Process
Before writing any code, you MUST follow the AI Agent Task Execution Protocol:

1. **Read and acknowledge** that you've reviewed these documentation files:
   - `backend/docs/database-schema.md` - For entity definitions and relationships
   - `backend/docs/technical-specifications.md` - For module structure and patterns
   - `backend/docs/api-design-standards.md` - For API conventions and DTOs
   - `backend/docs/testing-strategy.md` - For testing requirements
   - `backend/docs/ai-quick-reference.md` - For common patterns and decision trees

2. **State your understanding** of:
   - Which entities are involved
   - What relationships exist
   - What business rules apply
   - What patterns you'll follow

3. **Create an implementation plan** covering:
   - Files to create/modify
   - Dependencies and integrations
   - Testing strategy
   - Potential challenges

## Feature Requirements
[DETAILED_FEATURE_REQUIREMENTS_HERE]

## Specific Constraints
- Follow the module structure template exactly
- Use the address management pattern (no embedded address fields)
- Use the tax management pattern (ProductTax/InvoiceTax entities)
- Achieve 95%+ test coverage
- Follow Brazilian business rules and validations
- [ADD_SPECIFIC_CONSTRAINTS_HERE]

## Expected Deliverables
- Complete module implementation following technical specifications
- Full CRUD API endpoints following API design standards
- Comprehensive test suite following testing strategy
- Updated documentation where necessary
- [ADD_SPECIFIC_DELIVERABLES_HERE]

## Validation Requirements
Before submitting, confirm:
- All code follows documented patterns
- All tests pass with required coverage
- API documentation is complete
- No breaking changes introduced
- [ADD_SPECIFIC_VALIDATIONS_HERE]

Please start by acknowledging you've read the documentation and stating your implementation plan.
```

## Template 2: Bug Fix

```
# Task: Fix Bug - [BUG_DESCRIPTION]

## Context
There is a bug in the ez-NFe system: [DETAILED_BUG_DESCRIPTION]

## Mandatory Pre-Development Process
Before writing any code, you MUST follow the AI Agent Task Execution Protocol:

1. **Read and acknowledge** that you've reviewed these documentation files:
   - `backend/docs/database-schema.md` - For entity definitions and relationships
   - `backend/docs/technical-specifications.md` - For module structure and patterns
   - `backend/docs/ai-task-templates.md` - Use Task Template 3 (Fix Bug or Issue)
   - `backend/docs/ai-quick-reference.md` - For troubleshooting guidance

2. **State your understanding** of:
   - The root cause of the bug
   - Which components are affected
   - What the correct behavior should be
   - What patterns you'll follow for the fix

3. **Create a fix plan** covering:
   - Root cause analysis
   - Minimal fix approach
   - Files to modify
   - Testing strategy for the fix
   - Regression prevention

## Bug Details
- **Current Behavior**: [DESCRIBE_CURRENT_BEHAVIOR]
- **Expected Behavior**: [DESCRIBE_EXPECTED_BEHAVIOR]
- **Steps to Reproduce**: [LIST_REPRODUCTION_STEPS]
- **Affected Components**: [LIST_AFFECTED_MODULES/FILES]
- **Error Messages**: [INCLUDE_ERROR_MESSAGES_IF_ANY]

## Specific Constraints
- Make minimal necessary changes
- Ensure no breaking changes to existing functionality
- Follow existing code patterns
- Add tests to prevent regression
- [ADD_SPECIFIC_CONSTRAINTS_HERE]

## Expected Deliverables
- Bug fix implementation
- Tests covering the bug scenario
- Tests ensuring no regression
- Updated documentation if needed
- [ADD_SPECIFIC_DELIVERABLES_HERE]

## Validation Requirements
Before submitting, confirm:
- Bug is completely resolved
- All existing tests still pass
- New tests prevent future regression
- No new issues introduced
- Code follows project standards

Please start by acknowledging you've read the documentation and stating your fix plan.
```

## Template 3: Database Migration

```
# Task: Database Migration - [MIGRATION_DESCRIPTION]

## Context
I need you to implement a database migration for: [DETAILED_MIGRATION_DESCRIPTION]

## Mandatory Pre-Development Process
Before writing any code, you MUST follow the AI Agent Task Execution Protocol:

1. **Read and acknowledge** that you've reviewed these documentation files:
   - `backend/docs/database-schema.md` - For current schema and relationships
   - `backend/docs/technical-specifications.md` - For entity patterns
   - `backend/docs/ai-task-templates.md` - Use Task Template 4 (Database Migration)
   - `backend/docs/ai-quick-reference.md` - For entity and migration patterns

2. **State your understanding** of:
   - Current database structure
   - Required changes
   - Impact on existing data
   - Entity relationships affected

3. **Create a migration plan** covering:
   - Migration script approach
   - Data preservation strategy
   - Entity updates needed
   - Rollback strategy
   - Testing approach

## Migration Requirements
[DETAILED_MIGRATION_REQUIREMENTS_HERE]

## Specific Constraints
- Preserve all existing data
- Maintain referential integrity
- Follow TypeORM migration patterns
- Include both up and down methods
- Test migration on sample data
- [ADD_SPECIFIC_CONSTRAINTS_HERE]

## Expected Deliverables
- TypeORM migration script
- Updated entity definitions
- Updated database-schema.md documentation
- Migration testing validation
- Rollback verification
- [ADD_SPECIFIC_DELIVERABLES_HERE]

## Validation Requirements
Before submitting, confirm:
- Migration runs successfully
- All data is preserved correctly
- Entities match new schema
- Rollback works properly
- Documentation is updated

Please start by acknowledging you've read the documentation and stating your migration plan.
```

## Template 4: Module Enhancement

```
# Task: Enhance [MODULE_NAME] - [ENHANCEMENT_DESCRIPTION]

## Context
I need you to enhance the existing [MODULE_NAME] module with: [DETAILED_ENHANCEMENT_DESCRIPTION]

## Mandatory Pre-Development Process
Before writing any code, you MUST follow the AI Agent Task Execution Protocol:

1. **Read and acknowledge** that you've reviewed these documentation files:
   - `backend/docs/database-schema.md` - For entity definitions and relationships
   - `backend/docs/technical-specifications.md` - For module structure and patterns
   - `backend/docs/api-design-standards.md` - For API conventions and DTOs
   - `backend/docs/ai-task-templates.md` - Use Task Template 2 (Add New Feature to Existing Module)
   - `backend/docs/ai-quick-reference.md` - For common patterns

2. **State your understanding** of:
   - Current module structure
   - Existing functionality
   - How enhancement integrates
   - Impact on existing features

3. **Create an enhancement plan** covering:
   - Files to modify
   - New files to create
   - API changes needed
   - Database changes required
   - Testing strategy

## Enhancement Requirements
[DETAILED_ENHANCEMENT_REQUIREMENTS_HERE]

## Specific Constraints
- Maintain backward compatibility
- Follow existing module patterns
- Preserve existing functionality
- Update related documentation
- Achieve 95%+ test coverage
- [ADD_SPECIFIC_CONSTRAINTS_HERE]

## Expected Deliverables
- Enhanced module functionality
- Updated/new API endpoints
- Updated/new DTOs and validation
- Comprehensive test coverage
- Updated documentation
- [ADD_SPECIFIC_DELIVERABLES_HERE]

## Validation Requirements
Before submitting, confirm:
- All existing functionality still works
- New functionality works as specified
- All tests pass including new ones
- API documentation is updated
- No breaking changes introduced

Please start by acknowledging you've read the documentation and stating your enhancement plan.
```

## Template 5: New Module Creation

```
# Task: Create New Module - [MODULE_NAME]

## Context
I need you to create a new module for [MODULE_PURPOSE] in the ez-NFe system.

## Mandatory Pre-Development Process
Before writing any code, you MUST follow the AI Agent Task Execution Protocol:

1. **Read and acknowledge** that you've reviewed these documentation files:
   - `backend/docs/database-schema.md` - For entity definitions and relationships
   - `backend/docs/technical-specifications.md` - For module structure template and patterns
   - `backend/docs/api-design-standards.md` - For API conventions and DTOs
   - `backend/docs/testing-strategy.md` - For testing requirements
   - `backend/docs/ai-task-templates.md` - Use Task Template 1 (Create New Entity Module)
   - `backend/docs/ai-quick-reference.md` - For common patterns

2. **State your understanding** of:
   - Entity definitions and relationships
   - Module structure requirements
   - API patterns to follow
   - Testing requirements

3. **Create a module plan** covering:
   - Complete file structure
   - Entity relationships
   - Service methods needed
   - API endpoints required
   - Testing approach

## Module Requirements
[DETAILED_MODULE_REQUIREMENTS_HERE]

## Specific Constraints
- Follow module structure template exactly
- Use proper entity relationships (no embedded objects)
- Implement full CRUD operations
- Follow API design standards
- Achieve 95%+ test coverage
- Include proper validation and error handling
- [ADD_SPECIFIC_CONSTRAINTS_HERE]

## Expected Deliverables
- Complete module following technical specifications
- All entities with proper relationships
- Full service layer with business logic
- Complete controller with all endpoints
- Comprehensive DTO set with validation
- Full test suite (unit, integration, e2e)
- Updated documentation
- [ADD_SPECIFIC_DELIVERABLES_HERE]

## Validation Requirements
Before submitting, confirm:
- Module follows standard structure template
- All CRUD operations work correctly
- All relationships are properly defined
- API follows design standards
- Tests achieve required coverage
- Documentation is complete

Please start by acknowledging you've read the documentation and stating your module plan.
```

## Usage Instructions

### **How to Use These Templates:**

1. **Choose the Right Template**: Select based on your task type
2. **Fill in the Placeholders**: Replace all `[PLACEHOLDER]` text with specific details
3. **Customize Constraints**: Add any specific requirements for your task
4. **Modify Deliverables**: Adjust expected outputs based on your needs
5. **Add Validation**: Include any specific validation requirements

### **Placeholder Guide:**

- `[FEATURE_NAME]` - Short name for the feature
- `[SPECIFIC_FEATURE_DESCRIPTION]` - Detailed description of what needs to be built
- `[DETAILED_FEATURE_REQUIREMENTS_HERE]` - Comprehensive requirements list
- `[ADD_SPECIFIC_CONSTRAINTS_HERE]` - Any additional constraints or rules
- `[ADD_SPECIFIC_DELIVERABLES_HERE]` - Any additional expected outputs
- `[ADD_SPECIFIC_VALIDATIONS_HERE]` - Any additional validation requirements

### **Example Usage:**

Replace:
```
# Task: [FEATURE_NAME]
```

With:
```
# Task: Payment Term Installments Management
```

### **Tips for Better Prompts:**

1. **Be Specific**: Provide detailed requirements and context
2. **Reference Documentation**: Point to specific sections when relevant
3. **Include Examples**: Provide examples of expected behavior
4. **Set Clear Boundaries**: Define what should and shouldn't be included
5. **Specify Quality Gates**: Be clear about testing and validation requirements

### **Quality Checklist:**

Before sending any prompt, ensure:
- [ ] Appropriate template selected
- [ ] All placeholders filled with specific information
- [ ] Relevant documentation files referenced
- [ ] Clear requirements and constraints specified
- [ ] Expected deliverables clearly defined
- [ ] Validation requirements included
