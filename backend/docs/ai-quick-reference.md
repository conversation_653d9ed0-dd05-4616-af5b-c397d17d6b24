# AI Agent Quick Reference Guide

## Documentation Quick Access

### **Core Documentation Files**
- `database-schema.md` - All entity definitions, relationships, constraints
- `technical-specifications.md` - Module structure, patterns, examples
- `api-design-standards.md` - API conventions, DTOs, response formats
- `testing-strategy.md` - Testing requirements, coverage, patterns
- `ai-development-guide.md` - Development process for AI agents
- `ai-task-templates.md` - Step-by-step task templates

## Quick Decision Trees

### **"What Documentation Do I Need?"**

```
Are you creating a new entity?
├─ YES → Read database-schema.md first
│   └─ Then technical-specifications.md for module structure
└─ NO → Are you modifying an API?
    ├─ YES → Read api-design-standards.md
    └─ NO → Are you writing tests?
        └─ YES → Read testing-strategy.md
```

### **"What Files Do I Need to Create?"**

```
New Module:
├─ [module].module.ts (always required)
├─ entities/[entity].entity.ts (if new entity)
├─ controllers/[module].controller.ts (if API endpoints)
├─ services/[module].service.ts (always required)
├─ dto/ (create, update, query, response DTOs)
└─ tests/ (unit, integration, e2e tests)

Existing Module Modification:
├─ Update relevant service methods
├─ Update/add controller endpoints
├─ Update/add DTOs
├─ Update entity if database changes
├─ Add/update tests
└─ Update documentation
```

## Common Patterns Quick Reference

### **Entity Pattern**
```typescript
@Entity('[table_name]')
export class EntityName extends BaseEntity {
  @Column()
  field: string;

  @ManyToOne(() => RelatedEntity)
  relation: RelatedEntity;

  @OneToMany(() => ChildEntity, child => child.parent)
  children: ChildEntity[];
}
```

### **Service Pattern**
```typescript
@Injectable()
export class EntityService {
  constructor(
    @InjectRepository(Entity)
    private readonly repository: Repository<Entity>,
  ) {}

  async create(dto: CreateDto): Promise<Entity> {
    // Validation, creation, return
  }

  async findAll(query: QueryDto): Promise<PaginatedResult<Entity>> {
    // Query building, pagination, return
  }
}
```

### **Controller Pattern**
```typescript
@Controller('entities')
@UseGuards(JwtAuthGuard)
@ApiTags('Entities')
export class EntityController {
  @Post()
  @ApiOperation({ summary: 'Create entity' })
  async create(@Body() dto: CreateDto): Promise<ApiResponse<Entity>> {
    // Call service, return formatted response
  }
}
```

### **DTO Pattern**
```typescript
export class CreateEntityDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Field description' })
  field: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  optionalField?: string;
}
```

## Database Quick Reference

### **Common Entity Relationships**
```typescript
// One-to-Many (Parent has many children)
@OneToMany(() => Child, child => child.parent)
children: Child[];

// Many-to-One (Child belongs to parent)
@ManyToOne(() => Parent, parent => parent.children)
parent: Parent;

// Many-to-Many (with junction table)
@ManyToMany(() => Related, related => related.entities)
@JoinTable()
related: Related[];
```

### **Common Column Types**
```typescript
@Column()                                    // VARCHAR(255)
@Column('text')                             // TEXT
@Column('decimal', { precision: 12, scale: 2 }) // DECIMAL(12,2)
@Column({ type: 'enum', enum: MyEnum })     // ENUM
@Column({ nullable: true })                 // NULL allowed
@Column({ default: 'value' })               // Default value
```

### **Address Pattern (Use for all entities with addresses)**
```typescript
// Don't embed address fields - use relationships
@OneToMany(() => EntityAddress, ea => ea.entity)
addresses: EntityAddress[];

// Helper method
getPrimaryAddress(): Address | null {
  return this.addresses?.find(a => a.addressType === 'primary')?.address;
}
```

### **Tax Pattern (Use for all entities with taxes)**
```typescript
// Don't embed tax fields - use relationships
@OneToMany(() => EntityTax, et => et.entity)
taxes: EntityTax[];

// Helper method
getCurrentTaxes(): EntityTax[] {
  return this.taxes?.filter(t => t.isActive && this.isCurrentlyValid(t));
}
```

## API Quick Reference

### **Standard Response Format**
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  meta?: {
    pagination?: PaginationMeta;
    timestamp?: string;
  };
}
```

### **Pagination Pattern**
```typescript
interface PaginatedResult<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
```

### **Standard HTTP Status Codes**
- `200` - OK (GET, PATCH successful)
- `201` - Created (POST successful)
- `204` - No Content (DELETE successful)
- `400` - Bad Request (validation error)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate resource)
- `422` - Unprocessable Entity (business rule violation)
- `500` - Internal Server Error (unexpected error)

## Testing Quick Reference

### **Test Structure Pattern**
```typescript
describe('EntityService', () => {
  let service: EntityService;
  let repository: Repository<Entity>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        EntityService,
        { provide: getRepositoryToken(Entity), useValue: mockRepository },
      ],
    }).compile();

    service = module.get<EntityService>(EntityService);
    repository = module.get<Repository<Entity>>(getRepositoryToken(Entity));
  });

  describe('create', () => {
    it('should create entity successfully', async () => {
      // Arrange, Act, Assert
    });

    it('should throw error for invalid data', async () => {
      // Test error scenarios
    });
  });
});
```

### **Coverage Requirements**
- **Unit Tests**: 95% coverage minimum
- **Integration Tests**: All API endpoints
- **E2E Tests**: Complete user workflows
- **Error Testing**: All error scenarios

## Common Validation Rules

### **Brazilian-Specific Validations**
```typescript
// CPF validation
@Matches(/^\d{11}$/, { message: 'CPF must be 11 digits' })

// CNPJ validation
@Matches(/^\d{14}$/, { message: 'CNPJ must be 14 digits' })

// CEP validation
@Matches(/^\d{8}$/, { message: 'CEP must be 8 digits' })

// State code validation
@Matches(/^[A-Z]{2}$/, { message: 'State must be 2 uppercase letters' })
```

### **Common Field Validations**
```typescript
@IsString()
@IsNotEmpty()
@Length(1, 255)

@IsEmail()

@IsOptional()
@IsString()

@IsNumber()
@Min(0)
@Max(100)

@IsEnum(MyEnum)

@IsDateString()

@ValidateNested()
@Type(() => NestedDto)
```

## Error Handling Quick Reference

### **Common Exception Types**
```typescript
throw new BadRequestException('Validation failed');
throw new NotFoundException('Entity not found');
throw new ConflictException('Duplicate resource');
throw new UnauthorizedException('Authentication required');
throw new ForbiddenException('Insufficient permissions');
throw new UnprocessableEntityException('Business rule violation');
```

### **Custom Exception Pattern**
```typescript
export class CustomBusinessException extends HttpException {
  constructor(message: string, code: string) {
    super({
      message,
      error: 'Business Rule Violation',
      code,
    }, HttpStatus.UNPROCESSABLE_ENTITY);
  }
}
```

## Module Dependencies Quick Reference

### **Common Module Imports**
```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([Entity, RelatedEntity]),
    forwardRef(() => RelatedModule), // Avoid circular deps
    CacheModule,
  ],
  controllers: [EntityController],
  providers: [EntityService, EntityValidationService],
  exports: [EntityService], // Export what other modules need
})
```

### **Dependency Injection Pattern**
```typescript
constructor(
  @InjectRepository(Entity)
  private readonly repository: Repository<Entity>,
  private readonly relatedService: RelatedService,
  @Inject('CONFIG_TOKEN')
  private readonly config: ConfigType,
) {}
```

## Quick Troubleshooting

### **Common Issues and Solutions**

**"Circular dependency detected"**
- Use `forwardRef(() => Module)` in imports
- Avoid importing modules that import back

**"Repository not found"**
- Add entity to `TypeOrmModule.forFeature([Entity])`
- Check entity is properly decorated with `@Entity()`

**"Validation failed"**
- Check DTO has proper validation decorators
- Ensure ValidationPipe is applied globally

**"Tests failing"**
- Mock all dependencies in test setup
- Use proper test data fixtures
- Check async/await usage

## Remember: Always Follow the Documentation!

1. **Read First**: Always read relevant documentation before coding
2. **Follow Patterns**: Use established patterns consistently
3. **Test Everything**: Write comprehensive tests
4. **Document Changes**: Update docs when making changes
5. **Validate**: Run all validation commands before submitting
